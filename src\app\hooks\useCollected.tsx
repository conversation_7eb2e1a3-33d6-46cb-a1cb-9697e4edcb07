import { useState, useEffect } from 'react';
import Database from 'app/services/storage/DB';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { MSState } from '../redux/redux';

const useCollected = (show: number, season: number = null, episode: number = null, totalEpisodes: number = 0) => {
    const [collected, setCollected] = useState(false);
    const updated = useSelector<MSState, number>(state => state.meta.dbUpdates.collection);

    const calculate = () => {
        let query;
        if (show && season && episode) {
            query = 'SELECT * FROM collection WHERE show=? AND season = ? AND episode=?';
        } else if (show && season) {
            query = 'SELECT * FROM collection WHERE show=? AND season = ? AND episode != ?';
        } else if (show) {
            query = 'SELECT * FROM collection WHERE show=? AND season != ? AND episode != ?';
        }
        const values = [show, season || -1, episode || -1];
        Database.query(query, values).then(r => {
            if (_.isArray(r) && r.length > 0) {
                setCollected(r.length === totalEpisodes);
            }
        });
    };
    useEffect(() => {
        calculate();
    }, []);
    useEffect(() => {
        calculate();
    }, [updated]);
    return { collected };
};
export default useCollected;
