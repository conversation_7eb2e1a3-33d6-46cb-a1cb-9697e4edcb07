import React from 'react';
import { StyleSheet, View } from 'react-native';
import useGuest from 'app/app/hooks/useGuest';
import Text from 'app/app/components/elements/Text';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import { useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import EmptyWatching from 'app/views/watching/view/components/EmptyWatching';

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: SPACING.medium
    }
});

export interface EmptyResultProps {
    type: 'watchnow' | 'upcoming';
}

const EmptyResult = ({ type }: EmptyResultProps) => {
    const isGuest = useGuest();
    const hasFavorites = useSelector((state: MSState) => state.favorites.result.length > 0);
    return isGuest ? (
        <View style={{ ...styles.container }}>
            <Text textStyle="large" centered>
                {i18n.t('WATCHLIST_GUEST_HELP')}
            </Text>
        </View>
    ) : (
        <View style={{ ...styles.container }}>
            {hasFavorites ? (
                <Text textStyle="large" centered>
                    {type === 'upcoming' ? i18n.t('NO_UPCOMING_EPISODES') : i18n.t('NO_EPISODES_TO_WATCH')}
                </Text>
            ) : (
                <EmptyWatching {...{ type: 'watchlist' }} />
            )}
        </View>
    );
};

export default React.memo(EmptyResult);
