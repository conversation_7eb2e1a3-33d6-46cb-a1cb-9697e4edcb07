import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import TextInput from 'app/app/components/elements/form/TextInput';
import CheckBox from 'app/app/components/elements/form/CheckBox';
import i18n from 'i18n-js';

const styles = StyleSheet.create({
    inputField: { padding: 0, paddingLeft: 0, paddingBottom: 0, marginBottom: 0 }
});

export interface FormProps {
    placeholder: string;
    data?: Trakt.CommentForm;
    error: string;
    onChange: (data: Trakt.CommentForm) => void;
}

const Form = ({ placeholder, data, error: theError, onChange }: FormProps) => {
    const [comment, setComment] = useState(data ? data.comment : '');
    const [spoiler, setSpoiler] = useState(data ? data.spoiler : false);
    const [error, setError] = useState(theError || null);
    const clearError = () => {
        setError(null);
    };
    const onChangeComment = (comment: string) => {
        setComment(comment);
        onChange({ comment, spoiler });
    };

    const onChangeSpoiler = () => {
        setSpoiler(!spoiler);
        onChange({ comment, spoiler });
    };

    useEffect(() => {
        setError(theError);
    }, [theError]);

    return (
        <>
            <TextInput
                onFocus={clearError}
                errorMessage={error}
                autoFocus
                placeholder={placeholder}
                value={comment}
                multiline
                numberOfLines={4}
                onChangeText={onChangeComment}
                containerStyle={{ ...styles.inputField }}
                useEmoji
            />
            <CheckBox
                label={i18n.t('SPOILER')}
                subtitle={i18n.t('SPOILER_TIP')}
                checked={spoiler}
                onPress={onChangeSpoiler}
            />
        </>
    );
};

export default React.memo(Form);
