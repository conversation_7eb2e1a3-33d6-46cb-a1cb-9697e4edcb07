import React from 'react';
import { FlatList } from 'react-native';
import EmptyResults from 'app/app/components/containers/EmptyResults';
import i18n from 'i18n-js';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import ApiHelper from 'app/services/helpers/ApiHelper';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { useDispatch, useSelector } from 'react-redux';
import { searchShows } from 'app/app/redux/search/actions';
import { MSState } from 'app/app/redux/redux';
import MoreLoader from 'app/app/components/loaders/MoreLoader';
import _ from 'lodash';

export interface SearchResultsProps {
    searchParams: RequestParams;
    hasSearch: boolean;
}

const SearchResults = ({ hasSearch, searchParams }: SearchResultsProps) => {
    const dispatch = useDispatch();
    const loading = useSelector((state: MSState) => state.search.shows.loading);
    const headers = useSelector((state: MSState) => state.search.shows.headers);
    const { result, loadingMore } = useSelector((state: MSState) => {
        const { result } = state.search.shows;
        return { result, loadingMore: state.search.shows.loadingMore };
    }, _.isEqual);

    const renderItem = ({ item: trakt }: { item: number }) => {
        return <ShowListItem {...{ trakt }} />;
    };

    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (searchParams && !loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                ...searchParams,
                page: pagination.currentPage + 1
            };

            dispatch(searchShows(params));
        }
    };
    return (
        hasSearch && (
            <FlatList<number>
                data={result}
                keyExtractor={item => item.toString()}
                renderItem={renderItem}
                onEndReached={loadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={loadingMore && <MoreLoader />}
                ListEmptyComponent={!loading && <EmptyResults text={i18n.t('NO_SHOWS_FOUND')} />}
            />
        )
    );
};

export default React.memo(SearchResults, _.isEqual);
