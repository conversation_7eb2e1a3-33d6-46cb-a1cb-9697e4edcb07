import React, { FC } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { SvgProps } from 'react-native-svg';
import { SPACING } from 'app/app/styles/sizes';

const styles = StyleSheet.create({
    container: { marginHorizontal: SPACING.small }
});

export interface LanguageButtonProps {
    Element: FC<SvgProps>;
    onPress?: () => void;
    size: number;
    disabled?: boolean;
}

export default ({ Element, onPress, size, disabled }: LanguageButtonProps) => {
    const opacity = disabled ? 0.2 : 1;
    return (
        <TouchableOpacity style={[styles.container, { opacity }]} {...{ onPress, disabled }}>
            <Element {...{ width: size, height: size }} />
        </TouchableOpacity>
    );
};
