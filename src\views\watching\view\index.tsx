import React, { useEffect } from 'react';
import { FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { getFavorites, FavoriteAction } from 'app/app/redux/favorite/actions';
import { MSState } from 'app/app/redux/redux';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import Container from 'app/app/components/containers/Container';
import getEntities from 'app/app/redux/entities/selectors';
import _ from 'lodash';
import RefreshControl from 'app/app/components/refresh/RefreshControl';
import Banner from 'app/app/components/containers/Banner';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import useGuest from 'app/app/hooks/useGuest';
import EmptyWatching from './components/EmptyWatching';
import Restore from './components/Restore';

const Watching = () => {
    const dispatch: MS.Dispatch<FavoriteAction> = useDispatch();
    const isGuest = useGuest();
    const renderItem = ({ item: favorite }: { item: Api.Entity<MS.Favorite> }) => {
        const {
            attributes: {
                ids: { trakt }
            }
        } = favorite;
        return <ShowListItem {...{ trakt }} />;
    };
    const { favorites, refreshing }: { favorites: Api.Entity<MS.Favorite>[]; refreshing: boolean } = useSelector(
        (state: MSState) => {
            const favIds = state.favorites.result;
            const favorites = getEntities(state, favIds, 'favorite') as Api.Entity<MS.Favorite>[];
            return {
                favorites,
                refreshing: state.favorites.refreshing
            };
        },
        _.isEqual
    );
    useEffect(() => {
        dispatch(getFavorites());
    }, []);
    const onRefresh = () => dispatch(getFavorites(true));
    const refreshControl = <RefreshControl {...{ refreshing, onRefresh }} />;
    const restoreAvailable = favorites?.length === 0 && !isGuest && <Restore />;
    return (
        <>
            <Header centerComponent={{ text: i18n.t('WATCHING') }} />
            <Container useScrollView={false} withPadding={false}>
                {!isGuest && favorites?.length === 0 ? (
                    restoreAvailable
                ) : (
                    <FlatList<Api.Entity<MS.Favorite>>
                        data={favorites}
                        keyExtractor={item => item.id.toString()}
                        renderItem={renderItem}
                        ListEmptyComponent={EmptyWatching}
                        refreshControl={refreshControl}
                    />
                )}
            </Container>
            <Banner />
        </>
    );
};

export default React.memo(Watching);
