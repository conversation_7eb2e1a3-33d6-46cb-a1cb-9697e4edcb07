import Database from 'app/services/storage/DB';
import apiClient from 'app/services/api/client';
import _ from 'lodash';
import { UPDATE_RATINGS } from '../meta/actions';
import { MSState } from '../redux';
import { UNATHORIZED } from '../app/actions';
import { updateLastActivity } from './actions';

export const GET_MY_RATINGS_REQUEST = 'GET_MY_RATINGS_REQUEST';
export const GET_MY_RATINGS_SUCCESS = 'GET_MY_RATINGS_SUCCESS';
export const GET_MY_RATINGS_ERROR = 'GET_MY_RATINGS_ERROR';

export const RATE_REQUEST = 'RATE_REQUEST';
export const RATE_SUCCESS = 'RATE_SUCCESS';
export const RATE_ERROR = 'RATE_ERROR';

export const CLEAR_RATINGS_REQUEST = 'CLEAR_RATINGS_REQUEST';
export const CLEAR_RATINGS_SUCCESS = 'CLEAR_RATINGS_SUCCESS';
export const CLEAR_RATINGS_ERROR = 'CLEAR_RATINGS_ERROR';

const extractRatingData = (data: any[]) => {
    return data
        .map(value => {
            const { type, show, season, episode, movie, rating } = value;
            const showId = type !== 'movie' ? show?.ids.trakt : movie.trakt;
            const seasonNumber = type === 'season' ? season.number : type === 'episode' ? episode.season : -1;
            const episodeNumber = type === 'episode' ? episode?.number : -1;
            if (type !== 'movie') {
                return {
                    show: showId,
                    season: seasonNumber,
                    episode: episodeNumber,
                    type,
                    rating
                };
            }
            return null;
        })
        .filter(v => !!v);
};

export const clearRatings = () => dispatch => {
    dispatch({ type: CLEAR_RATINGS_REQUEST });
    return Database.query('DROP TABLE ratings')
        .then(() => {
            dispatch({ type: UPDATE_RATINGS });
            return dispatch({ type: CLEAR_RATINGS_SUCCESS });
        })
        .catch(error => {
            dispatch({ type: CLEAR_RATINGS_ERROR, error });
        });
};

export const getMyRatings = (type: TMDB.RatingType = 'all') => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                GET_MY_RATINGS_REQUEST,
                {
                    type: GET_MY_RATINGS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async json => {
                            await Database.query('DELETE FROM ratings');
                            const ratings = extractRatingData(json);
                            const sql = 'INSERT OR REPLACE INTO ratings (type,show,season,episode,rating) VALUES ';
                            _.chunk(ratings, 50).forEach(async rating => {
                                const queries = rating.map(row => {
                                    const { type, episode, rating, season, show } = row;
                                    return {
                                        q: '(?,?,?,?,?)',
                                        p: [type, show, season, episode, rating]
                                    };
                                });
                                const placeholders = queries.map(q => q.q).join(',');
                                const values = queries.map(q => q.p).flat(1);
                                const query = `${sql}${placeholders}`;
                                await Database.query(query, values);
                            });
                            dispatch({ type: UPDATE_RATINGS });
                            return {};
                        });
                    }
                },
                GET_MY_RATINGS_ERROR
            ],
            `/sync/ratings/${type}`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const rate = (type: Trakt.RateType, data: Trakt.RateData) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const showObj = data.shows[0];
    const show = showObj.ids.trakt;
    const season = type !== 'shows' ? showObj.seasons[0].number : -1;
    const episode = type === 'episodes' ? showObj.seasons[0].episodes[0].number : -1;
    const rating =
        type === 'shows'
            ? showObj.rating
            : type === 'seasons'
            ? showObj.seasons[0].rating
            : showObj.seasons[0].episodes[0].rating;
    return dispatch(
        apiClient(
            [
                RATE_REQUEST,
                {
                    type: RATE_SUCCESS,
                    payload: (action, state, res) => {
                        dispatch(updateLastActivity());
                        return res.json().then(() => {
                            const sql =
                                'INSERT OR REPLACE INTO ratings (type,show,season,episode,rating) VALUES (?,?,?,?,?)';
                            const values = [type, show, season, episode, rating];
                            return Database.query(sql, values).then(result => {
                                dispatch({ type: UPDATE_RATINGS });
                                return { result };
                            });
                        });
                    }
                },
                RATE_ERROR
            ],
            `/sync/ratings/`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: data
            }
        )
    );
};
