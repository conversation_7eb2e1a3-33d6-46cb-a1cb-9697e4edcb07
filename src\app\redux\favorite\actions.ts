import { Action } from 'redux';
import Database from 'app/services/storage/DB';
import { normalize } from 'normalizr';
import ShowHelper from 'app/services/helpers/ShowHelper';
import Storage from 'app/services/storage/Storage';
import { FavoritesSchema } from '../schemas';
import { getShowProgress } from '../sync/watch';

export const GET_FAVORITES_REQUEST = 'GET_FAVORITES_REQUEST';
export const GET_FAVORITES_SUCCESS = 'GET_FAVORITES_SUCCESS';
export const GET_FAVORITES_ERROR = 'GET_FAVORITES_ERROR';

export const ADD_TO_FAVORITES_REQUEST = 'ADD_TO_FAVORITES_REQUEST';
export const ADD_TO_FAVORITES_SUCCESS = 'ADD_TO_FAVORITES_SUCCESS';
export const ADD_TO_FAVORITES_ERROR = 'ADD_TO_FAVORITES_ERROR';

export const REMOVE_FROM_FAVORITES_REQUEST = 'REMOVE_FROM_FAVORITES_REQUEST';
export const REMOVE_FROM_FAVORITES_SUCCESS = 'REMOVE_FROM_FAVORITES_SUCCESS';
export const REMOVE_FROM_FAVORITES_ERROR = 'REMOVE_FROM_FAVORITES_ERROR';

export const CLEAR_FAVORITES_REQUEST = 'CLEAR_FAVORITES_REQUEST';
export const CLEAR_FAVORITES_SUCCESS = 'CLEAR_FAVORITES_SUCCESS';
export const CLEAR_FAVORITES_ERROR = 'CLEAR_FAVORITES_ERROR';

export const BACKUP_FAVORITES_REQUEST = 'BACKUP_FAVORITES_REQUEST';
export const BACKUP_FAVORITES_SUCCESS = 'BACKUP_FAVORITES_SUCCESS';
export const BACKUP_FAVORITES_ERROR = 'BACKUP_FAVORITES_ERROR';

export const RESTORE_FAVORITES_REQUEST = 'RESTORE_FAVORITES_REQUEST';
export const RESTORE_FAVORITES_SUCCESS = 'RESTORE_FAVORITES_SUCCESS';
export const RESTORE_FAVORITES_ERROR = 'RESTORE_FAVORITES_ERROR';

export interface FavoriteAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<MS.Favorite>;
        refreshing: boolean;
        show: number;
    };
}

export const getFavorites = (refreshing: boolean = false) => dispatch => {
    dispatch({ type: GET_FAVORITES_REQUEST, payload: { refreshing } });
    return Database.query('SELECT * FROM favorites ORDER BY title_sort ASC')
        .then(result => {
            const normalized = normalize(result, [FavoritesSchema]);
            return dispatch({ type: GET_FAVORITES_SUCCESS, payload: { normalized } });
        })
        .catch(error => {
            dispatch({ type: GET_FAVORITES_ERROR, error });
        });
};

export const addToFavorites = (show: Api.Entity<Trakt.Show, {}>, poster: string) => dispatch => {
    dispatch({ type: ADD_TO_FAVORITES_REQUEST });
    const {
        title,
        year,
        network,
        status,
        ids,
        ids: { trakt }
    } = show.attributes;
    const now = new Date().getTime();
    const idsStr = JSON.stringify(ids);
    const title_sort = ShowHelper.getSortTitle(title);
    return Database.query(
        'REPLACE INTO favorites (trakt,ids,poster,title,title_sort,status,year,network,created) VALUES (?,?,?,?,?,?,?,?,?)',
        [trakt, idsStr, poster, title, title_sort, status, year, network, now]
    )
        .then(() => {
            dispatch(getFavorites());
            dispatch(getShowProgress(trakt));
            return dispatch({ type: ADD_TO_FAVORITES_SUCCESS });
        })
        .catch(error => {
            dispatch({ type: ADD_TO_FAVORITES_ERROR, error });
        });
};

export const removeFromFavorites = (trakt: number) => dispatch => {
    dispatch({ type: REMOVE_FROM_FAVORITES_REQUEST });
    return Database.query('DELETE FROM favorites WHERE trakt = ?', [trakt])
        .then(() => {
            dispatch(getFavorites());
            return dispatch({ type: REMOVE_FROM_FAVORITES_SUCCESS, payload: { show: trakt } });
        })
        .catch(error => {
            dispatch({ type: REMOVE_FROM_FAVORITES_ERROR, error });
        });
};

export const clearFavorites = () => dispatch => {
    dispatch({ type: CLEAR_FAVORITES_REQUEST });
    return Database.query('DROP TABLE favorites')
        .then(() => {
            dispatch({ type: CLEAR_FAVORITES_SUCCESS });
        })
        .catch(error => {
            dispatch({ type: CLEAR_FAVORITES_ERROR, error });
        });
};

export const backUpFavorites = () => dispatch => {
    dispatch({ type: BACKUP_FAVORITES_REQUEST });
    return Database.query('SELECT * FROM favorites')
        .then(result => {
            return Storage.setItem(Storage.KEY_FAVORITES, result as any[]).then(() => {
                dispatch({ type: BACKUP_FAVORITES_SUCCESS });
            });
        })
        .catch(error => {
            dispatch({ type: BACKUP_FAVORITES_ERROR, error });
        });
};

export const restoreFavorites = () => dispatch => {
    dispatch({ type: RESTORE_FAVORITES_REQUEST });
    return Storage.getItem(Storage.KEY_FAVORITES)
        .then(favorites => {
            const query =
                'INSERT INTO favorites (trakt, ids, poster,title, title_sort,status,year,network,created, updated) VALUES ';
            const values = [];
            const params = [];
            favorites.forEach(favorite => {
                values.push('(?,?,?,?,?,?,?,?,?,?)');
                Object.keys(favorite).forEach(key => {
                    const v = favorite[key];
                    params.push(typeof v === 'object' ? JSON.stringify(v) : v);
                });
            });
            const sql = `${query}  ${values.join(',')}`;
            return Database.query(sql, params).then(() => {
                dispatch(getFavorites());
                return Storage.removeItem(Storage.KEY_FAVORITES).then(() => {
                    dispatch({ type: RESTORE_FAVORITES_SUCCESS });
                });
            });
        })
        .catch(e => {
            dispatch({ type: RESTORE_FAVORITES_ERROR, e });
        });
};
