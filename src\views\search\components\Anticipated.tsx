import React, { useEffect } from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { anticipatedShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Teaser from 'app/app/components/containers/Teaser';
import _ from 'lodash';
import Listed from 'app/app/components/show/Listed';

export interface AnticipatedProps {}

type Props = AnticipatedProps & NavigationInjectedProps<{}>;
const Anticipated = ({ navigation }: Props) => {
    const dispatch = useDispatch();
    const { headers, result, fetched } = useSelector((state: MSState) => {
        const { anticipated } = state.shows;
        return {
            headers: anticipated.headers,
            result: anticipated.result.slice(0, Constants.App.LIST_TEASER_ITEMS),
            fetched: anticipated.fetched
        };
    }, _.isEqual);
    useEffect(() => {
        if (ApiHelper.shouldFetch(fetched, UpdateThresholds.LOW)) {
            const params: RequestParams = {
                extended: 'full',
                limit: 20
            };
            dispatch(anticipatedShows(params));
        }
    }, []);

    const renderItem = (trakt: number) => {
        return (
            <ShowListItem
                {...{
                    key: trakt,
                    trakt,
                    rightElement: (
                        <Listed
                            {...{
                                trakt,
                                nFormatted: true
                            }}
                        />
                    )
                }}
            />
        );
    };
    const { totalItems } = ApiHelper.getPagination(headers);
    const onPress = () => {
        navigation.navigate(Constants.Navigation.Search.ANTICIPATED);
    };
    return (
        result.length > 0 && (
            <Teaser
                {...{
                    data: result,
                    onPress,
                    renderItem,
                    title: i18n.t('ANTICIPATED'),
                    subtitle: i18n.t('ANTICIPATED_SUB'),
                    showMore: totalItems > Constants.App.LIST_TEASER_ITEMS
                }}
            />
        )
    );
};

export default withNavigation(React.memo(Anticipated, _.isEqual));
