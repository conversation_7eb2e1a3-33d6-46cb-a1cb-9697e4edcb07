import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { SPACING } from 'app/app/styles/sizes';
import _ from 'lodash';
import ShowHelper from 'app/services/helpers/ShowHelper';
import i18n from 'i18n-js';
import { common } from 'app/app/styles/themes';
import withExternalIdsFetch, { ExternalIdsFetchProps } from 'app/app/hocs/withExternalIdsFetch';
import Text from '../../elements/Text';
import Image from '../../elements/Image';

export const PERSON_IMAGE_WIDTH = 185 * 0.55;
export const PERSON_IMAGE_HEIGHT = 278 * 0.55;
const styles = StyleSheet.create({
    container: {
        marginRight: SPACING.medium,
        paddingVertical: SPACING.normal,
        width: PERSON_IMAGE_WIDTH
    },
    imageContainer: {
        backgroundColor: 'white'
    }
});

export interface PersonCardItemProps {
    id: number; // tmdb id
    name: string;
    character?: string;
    profile_path: string;
    department?: string;
    job?: string;
    gender: number;
}
type Props = PersonCardItemProps & ExternalIdsFetchProps;
const PersonCardItem = ({ name, character, profile_path, department, job, gender, onPress }: Props) => {
    const uri = ShowHelper.getProfileUri(profile_path, gender, 25);
    return (
        <TouchableOpacity onPress={onPress} style={styles.container}>
            <View style={styles.imageContainer}>
                <Image
                    {...{
                        uri,
                        width: PERSON_IMAGE_WIDTH,
                        height: PERSON_IMAGE_HEIGHT,
                        backgroundColor: common.white
                    }}
                />
            </View>
            <Text numberOfLines={2} centered textStyle="small">
                {name}
            </Text>
            {!!character && (
                <>
                    <Text centered>{i18n.t('AS')}</Text>
                    <Text numberOfLines={3} centered textStyle="small">
                        {character}
                    </Text>
                </>
            )}
            {!!department && job && (
                <>
                    <Text numberOfLines={1} centered textStyle="small">
                        {`${department} ${i18n.t('DPT')}`}
                    </Text>
                    <Text numberOfLines={1} centered textStyle="small">
                        {job}
                    </Text>
                </>
            )}
        </TouchableOpacity>
    );
};

export default React.memo(withExternalIdsFetch(PersonCardItem), _.isEqual);
