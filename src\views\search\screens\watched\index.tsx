import React, { useEffect } from 'react';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { watchedShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import Back from 'app/app/components/header/Back';
import ApiHelper from 'app/services/helpers/ApiHelper';
import MoreLoader from 'app/app/components/loaders/MoreLoader';
import EmptyResults from 'app/app/components/containers/EmptyResults';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Watchers from 'app/app/components/show/Watchers';
import _ from 'lodash';
import { FlatList } from 'react-native-gesture-handler';

const Watched = () => {
    const dispatch = useDispatch();

    const { result, headers, loading, loadingMore } = useSelector((state: MSState) => {
        return {
            headers: state.shows.watched.headers,
            loading: state.shows.watched.loading,
            loadingMore: state.shows.watched.loadingMore,
            result: state.shows.watched.result
        };
    }, _.isEqual);

    useEffect(() => {
        if (result.length === 0) {
            const params: RequestParams = {
                extended: 'full'
            };
            dispatch(watchedShows('weekly', params));
        }
    }, []);

    const renderItem = ({ item: trakt }: { item: number }) => {
        return (
            <ShowListItem
                {...{
                    key: trakt,
                    trakt,
                    rightElement: <Watchers {...{ trakt, watchKey: 'watcher_count', nFormatted: true }} />
                }}
            />
        );
    };
    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (!loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                extended: 'full',
                page: pagination.currentPage + 1
            };
            dispatch(watchedShows('weekly', params));
        }
    };

    return (
        <>
            <Header centerComponent={{ text: i18n.t('WATCHED_SHOWS_WEEK') }} leftComponent={<Back />} />
            <FlatList<number>
                data={result}
                renderItem={renderItem}
                keyExtractor={(item: number) => item.toString()}
                onEndReached={loadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={loadingMore && <MoreLoader />}
                ListEmptyComponent={<EmptyResults text={i18n.t('NO_SHOWS_FOUND')} />}
            />
            {loading && result.length === 0 && <ScreenLoader />}
        </>
    );
};

export default React.memo(Watched, _.isEqual);
