import { combineReducers } from 'redux';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import { RESET_STORE } from '../app/actions';
import {
    GET_EPISODE_DETAILS_REQUEST,
    GET_EPISODE_DETAILS_SUCCESS,
    GET_EPISODE_DETAILS_ERROR,
    EpisodeAction,
    GET_TRAKT_EPISODES_REQUEST,
    GET_TRAKT_EPISODES_SUCCESS,
    GET_TRAKT_EPISODES_ERROR
} from './actions';

/** DETAILS */
export interface EpisodeDetailsState {
    loading: boolean;
    [key: number]: {
        [key: number]: {
            [key: number]: {
                result: number;
                headers: Api.TraktHeaders;
            };
        };
    };
}
export const initialEpisodeDetailsState: EpisodeDetailsState = {
    loading: false
};

export function details(state: EpisodeDetailsState = initialEpisodeDetailsState, action: EpisodeAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialEpisodeDetailsState;
        case GET_EPISODE_DETAILS_REQUEST: {
            return { ...state, loading: true };
        }

        case GET_EPISODE_DETAILS_SUCCESS: {
            const { id, season, episode, normalized, headers } = action.payload;
            const currentSeries = state[id] || {};
            const currentSeason = state[id] && state[id][season] ? state[id][season] : {};
            return {
                ...state,
                [id]: {
                    ...currentSeries,
                    [season]: {
                        ...currentSeason,
                        [episode]: {
                            result: normalized.result,
                            headers
                        }
                    }
                },
                loading: false
            };
        }
        case GET_EPISODE_DETAILS_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

/** TRAKT EPISODES */
export interface EpisodeState {
    loading: boolean;
    [key: number]: {
        [key: number]: {
            fetched: number;
            result: number[];
            headers: Api.TraktHeaders;
        };
    };
}
export const initialEpisodeState: EpisodeState = {
    loading: false
};

export function trakt(state: EpisodeState = initialEpisodeState, action: EpisodeAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialEpisodeState;
        case GET_TRAKT_EPISODES_REQUEST: {
            return { ...state, loading: true };
        }

        case GET_TRAKT_EPISODES_SUCCESS: {
            const { id, season, normalized, headers } = action.payload;
            const currentSeries = state[id] || {};
            return {
                ...state,
                [id]: {
                    ...currentSeries,
                    [season]: {
                        result: normalized.result,
                        headers,
                        fetched: new Date().getTime()
                    }
                },
                loading: false
            };
        }
        case GET_TRAKT_EPISODES_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface EpisodesState {
    details: EpisodeDetailsState;
    trakt: EpisodeState;
}

export const initialEpisodesState: EpisodesState = {
    details: initialEpisodeDetailsState,
    trakt: initialEpisodeState
};
export default combineReducers({
    details,
    trakt
});
