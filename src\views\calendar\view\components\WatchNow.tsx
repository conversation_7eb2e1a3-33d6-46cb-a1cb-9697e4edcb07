import React from 'react';
import { FlatList } from 'react-native';
import NextEpisodeListItem from 'app/app/components/listItems/episode/NextEpisodeListItem';
import _ from 'lodash';
import EmptyResults from './EmptyResults';

export interface WatchNowProps {
    data: MS.NextEpisode[];
    refreshControl: JSX.Element;
}
const renderItem = ({ item: nextEpisode }: { item: MS.NextEpisode }) => {
    return <NextEpisodeListItem {...{ episode: nextEpisode }} />;
};
const empty = <EmptyResults {...{ type: 'watchnow' }} />;

const WatchNow = ({ data, refreshControl }: WatchNowProps) => {
    return (
        <FlatList<MS.NextEpisode>
            data={data}
            keyExtractor={item => item.episodeIds.trakt.toString()}
            renderItem={renderItem}
            refreshControl={refreshControl}
            ListEmptyComponent={empty}
        />
    );
};

export default React.memo(WatchNow, _.isEqual);
