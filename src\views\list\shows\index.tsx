import React, { useEffect } from 'react';
import { FlatList } from 'react-native';
import { NavParams } from 'app/app/navigation/Navigator';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { useNavigation } from 'react-navigation-hooks';
import getEntities from 'app/app/redux/entities/selectors';
import { getListItems } from 'app/app/redux/list/actions';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { Header } from 'react-native-elements';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import Back from 'app/app/components/header/Back';
import ApiHelper from 'app/services/helpers/ApiHelper';
import { UpdateThresholds } from 'app/app/Constants';
import RefreshControl from 'app/app/components/refresh/RefreshControl';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import ShowHelper from 'app/services/helpers/ShowHelper';

export interface ListShowNavParams extends NavParams {}

const defaulObject = { result: [], fetched: 0, refreshing: false, headers: null };
const ListShows = () => {
    const navigation = useNavigation();
    const id = navigation.getParam('id');
    const dispatch = useDispatch();
    const { ids, list, shows, fetched, refreshing, sorting, sortingDirection } = useSelector((state: MSState) => {
        const list = state.entities.list[id];
        const { result: ids, fetched, headers } = state.lists.listItems[id] || defaulObject;
        const { refreshing } = state.lists.listItems;
        const shows = getEntities(state, ids, 'show') as Api.Entity<Trakt.Show, Trakt.ShowMeta>[];
        const sorting = headers?.map['x-sort-by'];
        const sortingDirection = headers?.map['x-sort-how'];
        return { ids, list, shows, fetched, refreshing, sorting, sortingDirection };
    });
    useEffect(() => {
        if (ids.length !== shows.length && ApiHelper.shouldFetch(fetched, UpdateThresholds.HIGH)) {
            const params: RequestParams = {
                extended: 'full'
            };
            dispatch(getListItems(id, params));
        }
    }, []);
    if (!list) {
        return <ScreenLoader />;
    }
    const { name } = list.attributes;
    const renderItem = ({ item }: { item: Api.Entity<Trakt.Show> }) => {
        const {
            attributes: {
                ids: { trakt }
            }
        } = item;
        return <ShowListItem {...{ trakt, useSortTitle: true }} />;
    };
    const numOfShows = shows.length ? `- ${shows.length}` : '';
    const title = `${name} ${numOfShows}`;
    const sorted = ShowHelper.sortShows(shows, sorting || 'title', sortingDirection || 'asc');
    const onRefresh = () => {
        const params: RequestParams = {
            extended: 'full'
        };
        dispatch(getListItems(id, params, true));
    };
    const refreshControl = <RefreshControl {...{ refreshing, onRefresh }} />;
    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: title }} />
            <FlatList<Api.Entity<Trakt.Show>>
                {...{ data: sorted, keyExtractor: item => item.id.toString(), renderItem, refreshControl }}
            />
        </>
    );
};

export default React.memo(ListShows);
