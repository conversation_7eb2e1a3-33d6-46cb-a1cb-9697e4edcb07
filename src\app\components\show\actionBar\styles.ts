import { SPACING } from 'app/app/styles/sizes';
import { StyleSheet, Dimensions } from 'react-native';
import { ACTION_BAR_HEIGHT, RATE_HEIGHT } from './ActionsBar';

const { width } = Dimensions.get('window');
export default StyleSheet.create({
    container: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        paddingHorizontal: SPACING.normal,
        paddingTop: SPACING.normal,
        paddingBottom: SPACING.small,
        alignItems: 'flex-end',
        position: 'absolute',
        zIndex: 10,
        right: 0,
        bottom: 0,
        left: 0,
        height: ACTION_BAR_HEIGHT
    },
    overlay: {
        borderWidth: 0,
        position: 'absolute',
        height: RATE_HEIGHT,
        backgroundColor: 'transparent'
    },
    icon: {
        paddingHorizontal: SPACING.normal,
        zIndex: 2
    },
    right: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    disabled: { backgroundColor: 'transparent' },
    gradient: { zIndex: 0, ...StyleSheet.absoluteFillObject },
    listBadgeContainer: {
        position: 'absolute',
        top: 0,
        right: 0,
        zIndex: 3
    },
    listBadge: {
        width: SPACING.normal * 2,
        height: SPACING.normal * 2
    },
    listBadgeText: {
        fontWeight: 'bold'
    },
    rateContainer: {
        backgroundColor: 'transparent',
        width,
        height: RATE_HEIGHT,
        alignItems: 'center',
        justifyContent: 'center'
    },
    ratingBadgeContainer: {
        position: 'absolute',
        top: 3,
        right: 13,
        zIndex: 3
    },
    ratingBadge: {
        width: SPACING.normal * 2,
        height: SPACING.normal * 2,
        backgroundColor: 'transparent'
    },
    ratingBadgeText: {
        fontWeight: 'bold'
    }
});
