import React, { useContext } from 'react';
import { ListItem, ThemeContext, ThemeProps } from 'react-native-elements';
import ShowHelper from 'app/services/helpers/ShowHelper';
import i18n from 'i18n-js';
import useWatched from 'app/app/hooks/useWatched';
import { Theme } from 'app/app/styles/themes';
import DateHelper from 'app/services/helpers/DateHelper';
import useLocale from 'app/app/hooks/useLocale';
import Image from '../../elements/Image';

export interface SeasonListItemProps {
    show: number;
    season: number;
    name: string;
    episode_count: number;
    air_date: string;
    poster_path: string;
    onPress: () => void;
}

const SeasonListItem = ({ name, episode_count, air_date, poster_path, onPress, show, season }: SeasonListItemProps) => {
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const locale = useLocale();
    const uri = ShowHelper.getPosterUri(poster_path, 20, scheme);
    const title = `${name}`;
    const subtitle = `${episode_count} ${i18n.t('EPISODES')} ${DateHelper.format(air_date, locale, 'DD/MM/YYYY')} `;
    const { watched } = useWatched(show, season);
    return (
        <ListItem
            {...{
                leftElement: (
                    <Image
                        {...{
                            uri,
                            width: 92 * 0.6,
                            height: 140 * 0.6
                        }}
                    />
                ),
                title,
                subtitle,
                checkmark: watched,
                onPress: () => onPress()
            }}
            chevron
        />
    );
};

export default React.memo(SeasonListItem);
