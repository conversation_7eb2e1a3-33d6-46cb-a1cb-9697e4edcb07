import apiClient from 'app/services/api/client';
import { Action } from 'redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { normalize } from 'normalizr';
import { MSState } from '../redux';
import { UNATHORIZED } from '../app/actions';
import { TraktEpisodesRating } from '../schemas';

export const GET_USER_SETTINGS_REQUEST = 'GET_USER_SETTINGS_REQUEST';
export const GET_USER_SETTINGS_SUCCESS = 'GET_USER_SETTINGS_SUCCESS';
export const GET_USER_SETTINGS_ERROR = 'GET_USER_SETTINGS_ERROR';

export const GET_USER_STATS_REQUEST = 'GET_USER_STATS_REQUEST';
export const GET_USER_STATS_SUCCESS = 'GET_USER_STATS_SUCCESS';
export const GET_USER_STATS_ERROR = 'GET_USER_STATS_ERROR';

export const GET_LATEST_RATINGS_REQUEST = 'GET_LATEST_RATINGS_REQUEST';
export const GET_LATEST_RATINGS_SUCCESS = 'GET_LATEST_RATINGS_SUCCESS';
export const GET_LATEST_RATINGS_ERROR = 'GET_LATEST_RATINGS_ERROR';

export interface UserAction extends Action {
    payload: {
        settings: Trakt.UserSettings;
        stats: Trakt.UserStats;
        normalized: Api.NormalizedResponse<Trakt.EpisodeRating>;
        append: boolean;
        headers: Api.TraktHeaders;
    };
}
export const getUserSettings = (token: string = null) => (dispatch, getState) => {
    const access_token = token || (getState() as MSState).auth.trakt.token.access_token;

    return dispatch(
        apiClient(
            [
                GET_USER_SETTINGS_REQUEST,
                {
                    type: GET_USER_SETTINGS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const settings: Trakt.UserSettings = json;
                            return { settings };
                        });
                    }
                },
                GET_USER_SETTINGS_ERROR
            ],
            `/users/settings/`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const getUserStats = (token: string = null) => (dispatch, getState) => {
    const access_token = token || (getState() as MSState).auth.trakt.token.access_token;

    return dispatch(
        apiClient(
            [
                GET_USER_STATS_REQUEST,
                {
                    type: GET_USER_STATS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const stats: Trakt.UserStats = json;
                            return { stats };
                        });
                    }
                },
                GET_USER_STATS_ERROR
            ],
            `/users/me/stats/`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const getLatestRatings = (params: RequestParams = { page: 0, limit: 20 }) => (dispatch, getState) => {
    const append = params?.page > 1 || false;

    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                { type: GET_LATEST_RATINGS_REQUEST, payload: { append } },
                {
                    type: GET_LATEST_RATINGS_SUCCESS,
                    payload: (action, state, res) => {
                        const { headers }: { headers: Headers } = res;
                        return res.json().then(async json => {
                            const normalized = normalize(json, [TraktEpisodesRating]);
                            return { normalized, append, headers };
                        });
                    }
                },
                GET_LATEST_RATINGS_ERROR
            ],
            `/users/me/ratings/episodes`,
            'GET',
            'trakt',
            params,
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};
