import { RequestParams } from 'app/services/helpers/RequestHelper';
import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { Action } from 'redux';
import { TraktShowSchema, TmdbShowDetailsSchema } from '../schemas';
import { MSState } from '../redux';
import { UNATHORIZED } from '../app/actions';

export const GET_TRENDING_SHOWS_REQUEST = 'GET_TRENDING_SHOWS_REQUEST';
export const GET_TRENDING_SHOWS_SUCCESS = 'GET_TRENDING_SHOWS_SUCCESS';
export const GET_TRENDING_SHOWS_ERROR = 'GET_TRENDING_SHOWS_ERROR';

export const GET_POPULAR_SHOWS_REQUEST = 'GET_POPULAR_SHOWS_REQUEST';
export const GET_POPULAR_SHOWS_SUCCESS = 'GET_POPULAR_SHOWS_SUCCESS';
export const GET_POPULAR_SHOWS_ERROR = 'GET_POPULAR_SHOWS_ERROR';

export const GET_RECOMMENDED_SHOWS_REQUEST = 'GET_RECOMMENDED_SHOWS_REQUEST';
export const GET_RECOMMENDED_SHOWS_SUCCESS = 'GET_RECOMMENDED_SHOWS_SUCCESS';
export const GET_RECOMMENDED_SHOWS_ERROR = 'GET_RECOMMENDED_SHOWS_ERROR';

export const GET_WATCHED_SHOWS_REQUEST = 'GET_WATCHED_SHOWS_REQUEST';
export const GET_WATCHED_SHOWS_SUCCESS = 'GET_WATCHED_SHOWS_SUCCESS';
export const GET_WATCHED_SHOWS_ERROR = 'GET_WATCHED_SHOWS_ERROR';

export const HIDE_SHOW_REQUEST = 'HIDE_SHOW_REQUEST';
export const HIDE_SHOW_SUCCESS = 'HIDE_SHOW_SUCCESS';
export const HIDE_SHOW_ERROR = 'HIDE_SHOW_ERROR';

export const GET_SHOW_DETAILS_REQUEST = 'GET_SHOW_DETAILS_REQUEST';
export const GET_SHOW_DETAILS_SUCCESS = 'GET_SHOW_DETAILS_SUCCESS';
export const GET_SHOW_DETAILS_ERROR = 'GET_SHOW_DETAILS_ERROR';

export const GET_SIMILAR_SHOWS_REQUEST = 'GET_SIMILAR_SHOWS_REQUEST';
export const GET_SIMILAR_SHOWS_SUCCESS = 'GET_SIMILAR_SHOWS_SUCCESS';
export const GET_SIMILAR_SHOWS_ERROR = 'GET_SIMILAR_SHOWS_ERROR';

export const GET_ANTICIPATED_SHOWS_REQUEST = 'GET_ANTICIPATED_SHOWS_REQUEST';
export const GET_ANTICIPATED_SHOWS_SUCCESS = 'GET_ANTICIPATED_SHOWS_SUCCESS';
export const GET_ANTICIPATED_SHOWS_ERROR = 'GET_ANTICIPATED_SHOWS_ERROR';

export const GET_SHOW_REQUEST = 'GET_SHOW_REQUEST';
export const GET_SHOW_SUCCESS = 'GET_SHOW_SUCCESS';
export const GET_SHOW_ERROR = 'GET_SHOW_ERROR';

export interface ShowAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<Trakt.Show | Trakt.WatchedItem>;
        append: boolean;
        headers: Api.TraktHeaders;
        id: number;
        refreshing: boolean;
    };
}

export const trendingShows = (params: RequestParams = {}) => {
    const append = params?.page > 1;
    return apiClient(
        [
            { type: GET_TRENDING_SHOWS_REQUEST, payload: { append } },
            {
                type: GET_TRENDING_SHOWS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktShowSchema]);
                        return { normalized, headers, append };
                    });
                }
            },
            GET_TRENDING_SHOWS_ERROR
        ],
        `/shows/trending`,
        'GET',
        'trakt',
        params
    );
};

export const watchedShows = (period: Trakt.Period = 'weekly', params: RequestParams = {}) => {
    const append = params?.page > 1;
    return apiClient(
        [
            { type: GET_WATCHED_SHOWS_REQUEST, payload: { append } },
            {
                type: GET_WATCHED_SHOWS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktShowSchema]);
                        return { normalized, headers, append };
                    });
                }
            },
            GET_WATCHED_SHOWS_ERROR
        ],
        `/shows/watched/${period}`,
        'GET',
        'trakt',
        params
    );
};

export const anticipatedShows = (params: RequestParams = {}) => {
    const append = params?.page > 1;
    return apiClient(
        [
            { type: GET_ANTICIPATED_SHOWS_REQUEST, payload: { append } },
            {
                type: GET_ANTICIPATED_SHOWS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktShowSchema]);
                        return { normalized, headers, append };
                    });
                }
            },
            GET_ANTICIPATED_SHOWS_ERROR
        ],
        `/shows/anticipated`,
        'GET',
        'trakt',
        params
    );
};

export const popularShows = (params: RequestParams = {}) => {
    const append = params?.page > 1;
    return apiClient(
        [
            { type: GET_POPULAR_SHOWS_REQUEST, payload: { append } },
            {
                type: GET_POPULAR_SHOWS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktShowSchema]);
                        return { normalized, headers, append };
                    });
                }
            },
            GET_POPULAR_SHOWS_ERROR
        ],
        `/shows/popular`,
        'GET',
        'trakt',
        params
    );
};

export const recommendedShows = (params: RequestParams = {}) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const append = params?.page > 1;
    return dispatch(
        apiClient(
            [
                { type: GET_RECOMMENDED_SHOWS_REQUEST, payload: { append } },
                {
                    type: GET_RECOMMENDED_SHOWS_SUCCESS,
                    payload: (action, state, res) => {
                        const { headers }: { headers: Headers } = res;
                        return res.json().then(json => {
                            const normalized = normalize(json, [TraktShowSchema]);
                            return { normalized, headers, append };
                        });
                    }
                },
                GET_RECOMMENDED_SHOWS_ERROR
            ],
            `/recommendations/shows`,
            'GET',
            'trakt',
            params,
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const hideShow = (id: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                HIDE_SHOW_REQUEST,
                {
                    type: HIDE_SHOW_SUCCESS,
                    payload: () => {
                        return { id };
                    }
                },
                HIDE_SHOW_ERROR
            ],
            `/recommendations/shows/${id}`,
            'DELETE',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const getShowDetails = (id: number) => {
    const params = { append_to_response: 'credits,external_ids' };
    return apiClient(
        [
            GET_SHOW_DETAILS_REQUEST,
            {
                type: GET_SHOW_DETAILS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, TmdbShowDetailsSchema);
                        return { normalized, headers };
                    });
                }
            },
            GET_SHOW_DETAILS_ERROR
        ],
        `/tv/${id}`,
        'GET',
        'tmdb',
        params
    );
};

export const similarShows = (id: number, params: RequestParams = {}) => {
    const append = params?.page > 1;
    return apiClient(
        [
            { type: GET_SIMILAR_SHOWS_REQUEST, payload: { append } },
            {
                type: GET_SIMILAR_SHOWS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktShowSchema]);
                        return { normalized, headers, id };
                    });
                }
            },
            GET_SIMILAR_SHOWS_ERROR
        ],
        `/shows/${id}/related`,
        'GET',
        'trakt',
        params
    );
};

export const getShow = (id: number) => dispatch => {
    return dispatch(
        apiClient(
            [
                GET_SHOW_REQUEST,
                {
                    type: GET_SHOW_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json, TraktShowSchema);
                            return { normalized };
                        });
                    }
                },
                GET_SHOW_ERROR
            ],
            `/shows/${id}`,
            'GET',
            'trakt',
            { extended: 'full' }
        )
    );
};
