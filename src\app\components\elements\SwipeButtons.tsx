import React from 'react';
import { StyleSheet, View, Animated } from 'react-native';

import { RectButton, TouchableOpacity } from 'react-native-gesture-handler';
import { Icon, IconProps } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { common } from 'app/app/styles/themes';
import Text from './Text';

const styles = StyleSheet.create({
    button: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1
    }
});

export interface SwipeButton {
    backgroundColor?: string;
    onPress: () => void;
    iconProps: IconProps;
    label: string;
}

export interface SwipeButtonsProps {
    dragX: Animated.AnimatedInterpolation;
    buttonWidth?: number;
    right: boolean;
    buttons: SwipeButton[];
    onAfterPress: () => void;
}

const SwipeButtons = ({ right, dragX, buttonWidth, buttons, onAfterPress }: SwipeButtonsProps) => {
    const totalWidth = buttonWidth * buttons.length;
    const scale = dragX.interpolate({
        inputRange: right ? [-totalWidth, 0] : [0, totalWidth],
        outputRange: right ? [1, 0] : [0, 1]
    });

    return (
        <>
            {buttons.map(button => {
                const { iconProps, label, onPress, backgroundColor } = button;
                return (
                    <RectButton
                        key={label}
                        onPress={() => {
                            onPress();
                            onAfterPress();
                        }}
                    >
                        <View
                            accessible
                            style={{
                                ...styles.button,
                                backgroundColor,
                                width: buttonWidth
                            }}
                        >
                            <Animated.View style={{ transform: [{ scale }] }}>
                                <Icon
                                    Component={TouchableOpacity}
                                    type="font-awesome"
                                    size={ICON_SIZE.normal}
                                    onPress={() => {
                                        onPress();
                                        onAfterPress();
                                    }}
                                    color={common.white}
                                    {...iconProps}
                                />
                                <Text textStyle="nano" centered color={common.white}>
                                    {label}
                                </Text>
                            </Animated.View>
                        </View>
                    </RectButton>
                );
            })}
        </>
    );
};

SwipeButtons.defaultProps = {
    buttonWidth: 100,
    right: true
};

export default React.memo(SwipeButtons);
