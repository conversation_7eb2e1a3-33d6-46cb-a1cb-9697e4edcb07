import React, { useContext } from 'react';
import { StyleSheet, View, ImageURISource } from 'react-native';
import { ListItem, ThemeProps, ThemeContext } from 'react-native-elements';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ShowViewNavParams } from 'app/views/show/view';
import Constants from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import _ from 'lodash';
import { Theme } from 'app/app/styles/themes';

import Text from '../../elements/Text';
import { IMAGE_HEIGHT } from './ShowListItem';
import Image from '../../elements/Image';

const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: 'row'
    },
    subtitle: {
        flexDirection: 'row'
    },
    image: {
        height: IMAGE_HEIGHT,
        borderWidth: 1
    }
});

export interface ShowFavoriteListItemProps {
    ids: Trakt.Ids;
    poster: string;
    year: number;
    network: string;
    title_sort: string;
    status: Trakt.ShowStatus;
}
const leftElement = (uri: ImageURISource | number, width: number) => (
    <Image {...{ width, uri, height: IMAGE_HEIGHT }} />
);
/**
 *
 * @deprecated
 */
const ShowFavoriteListItem = ({ ids, poster, year, network, title_sort, status }: ShowFavoriteListItemProps) => {
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const uri = ShowHelper.getPosterUri(poster, 20, scheme);
    const { navigate } = useNavigation();
    const width = IMAGE_HEIGHT * 0.66;

    const titleYear = year ? year.toString() : '-';
    const titleNetwork = network ? ` / ${network}` : '';
    const [label, color] = ShowHelper.getStatus(status);

    const onPress = () => {
        const params: ShowViewNavParams = { ids };
        navigate(Constants.Navigation.Show.VIEW, params);
    };

    const subtitle = (
        <View style={styles.subtitle}>
            <Text>{`${titleYear} ${titleNetwork}`}</Text>
        </View>
    );

    return (
        <ListItem
            leftElement={leftElement(uri, width)}
            title={
                <View style={styles.titleContainer}>
                    <Text>{title_sort}</Text>
                </View>
            }
            subtitle={
                <View>
                    {subtitle}
                    <Text color={color}>{`${label}`}</Text>
                </View>
            }
            chevron
            onPress={onPress}
        />
    );
};

export default React.memo(ShowFavoriteListItem, _.isEqual);
