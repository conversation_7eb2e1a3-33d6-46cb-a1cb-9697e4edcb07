import React, { useEffect, useContext } from 'react';
import { View } from 'react-native';
import { MSState } from 'app/app/redux/redux';
import { useSelector, useDispatch } from 'react-redux';
import { getShowImages } from 'app/app/redux/image/actions';
import { ListItem, ThemeContext, ThemeProps } from 'react-native-elements';
import ShowHelper from 'app/services/helpers/ShowHelper';
import useFavorite from 'app/app/hooks/useFavorite';
import { Theme } from 'app/app/styles/themes';
import DateHelper from 'app/services/helpers/DateHelper';
import useLocale from 'app/app/hooks/useLocale';
import { EpisodeNavParams } from 'app/views/episode/view';
import Constants from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import Text from '../../elements/Text';
import { leftElement, IMAGE_HEIGHT } from '../show/ShowListItem';
import ActionRate from '../../show/actionBar/components/ActionRate';

export interface EpisodeRatingProps {
    id: number;
}

interface StateProps {
    rating: Api.Entity<Trakt.EpisodeRating>;
    tmdb: number;
    image: TMDB.Image;
}

const EpisodeRating = ({ id }: EpisodeRatingProps) => {
    // REDUX
    const dispatch = useDispatch();

    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    const { rating, tmdb, image } = useSelector<MSState, StateProps>(state => {
        const rating = state.entities.episodeRating[id];
        const { tmdb } = rating?.attributes.show.ids;
        return {
            rating,
            tmdb,
            image: tmdb ? state.entities.image[tmdb]?.attributes.posters?.[0] : null
        };
    });
    // @ts-ignore
    const { push } = useNavigation();
    const isFavorite = useFavorite(rating?.attributes.show.ids.trakt);
    const locale = useLocale();
    useEffect(() => {
        if (tmdb && !image) {
            dispatch(getShowImages(tmdb));
        }
    }, [tmdb]);
    if (!rating) {
        return null;
    }

    const {
        show: { title: showTitle, ids },
        episode: { season, number, title: episodeTitle },
        rated_at,
        rating: episodeRating
    } = rating.attributes;

    const subtitle = (
        <View>
            <Text textStyle="small" color={colors.discreet}>{`${season}x${number} ${episodeTitle}`}</Text>
            <Text textStyle="small" color={colors.discreet}>
                {DateHelper.format(rated_at, locale, 'DD MMM YYY HH:mm')}
            </Text>
        </View>
    );

    const uri = ShowHelper.getPosterUri(image?.file_path, 20, scheme);

    const width = IMAGE_HEIGHT * 0.66;
    const onPress = () => {
        const params: EpisodeNavParams = { ids, seasonNumber: season, episodeNumber: number };
        push(Constants.Navigation.Episode.VIEW, params);
    };
    return (
        <ListItem
            onPress={onPress}
            subtitle={subtitle}
            title={ShowHelper.getSortTitle(showTitle)}
            leftElement={leftElement(uri, width, isFavorite)}
            rightElement={<ActionRate {...{ rating: episodeRating, isGuest: false, onHeartPress: null }} />}
            chevron
        />
    );
};

export default React.memo(EpisodeRating);
