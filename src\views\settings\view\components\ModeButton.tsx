import React from 'react';
import { StyleSheet, View, ColorSchemeName } from 'react-native';

import { SPACING } from 'app/app/styles/sizes';
import { common } from 'app/app/styles/themes';

const styles = StyleSheet.create({
    button: {
        width: SPACING.large,
        height: SPACING.large,
        borderRadius: SPACING.large / 2,
        borderWidth: 1
    }
});

export interface ModeButtonProps {
    onPress?: () => void;
    mode: ColorSchemeName;
    disabled?: boolean;
}

const ModeButton = ({ onPress, mode, disabled }: ModeButtonProps) => {
    const opacity = disabled ? 0.3 : 1;
    const backgroundColor = mode === 'light' ? common.white : common.black;
    const borderColor = mode === 'dark' ? common.white : common.black;
    return (
        <View
            {...{
                style: {
                    opacity,
                    backgroundColor,
                    borderColor,
                    ...styles.button
                },
                onPress: disabled ? null : onPress
            }}
        />
    );
};

ModeButton.defaultProps = {
    disabled: false,
    onPress: () => null
};

export default React.memo(ModeButton);
