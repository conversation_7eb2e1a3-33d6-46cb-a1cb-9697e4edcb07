import React, { useContext, useState, useEffect } from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { common, Theme } from 'app/app/styles/themes';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { useDispatch } from 'react-redux';
import useGuest from 'app/app/hooks/useGuest';
import Toast from 'react-native-toast-message';
import { defaultOptions } from 'app/app/components/elements/Toast';
import i18n from 'i18n-js';
import useCollected from 'app/app/hooks/useCollected';
import {
    collectShow,
    unCollectShow,
    collectSeason,
    unCollectSeason,
    collectEpisode,
    unCollectEpisode
} from 'app/app/redux/sync/collection';
import styles from '../styles';

export interface ActionCollectProps {
    ids: Trakt.Ids;
    season: number;
    episode: number;
    totalEpisodes: number;
    type: Trakt.RateType;
}

const ActionCollect = ({ type, ids, season, episode, totalEpisodes }: ActionCollectProps) => {
    const { trakt: show } = ids;
    const isGuest = useGuest();
    const opacity = isGuest ? 0.3 : 1;

    const dispatch = useDispatch();
    const { collected: isCollected } = useCollected(ids.trakt, season, episode, totalEpisodes);
    const [collected, setCollected] = useState(false);

    useEffect(() => {
        setCollected(isCollected);
    }, [isCollected]);

    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    const onCollectPress = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('COLLECT_WARNING')
            });
        } else if (type === 'episodes') {
            if (!collected) {
                dispatch(collectEpisode(show, season, episode));
            } else {
                dispatch(unCollectEpisode(show, season, episode));
            }
            setCollected(!collected);
        } else if (type === 'seasons') {
            if (!collected) {
                dispatch(collectSeason(show, season));
            } else {
                dispatch(unCollectSeason(show, season));
            }
            setCollected(!collected);
        } else if (type === 'shows') {
            if (!collected) {
                dispatch(collectShow(show));
            } else {
                dispatch(unCollectShow(show));
            }
            setCollected(!collected);
        }
    };

    return (
        <Icon
            Component={TouchableOpacity}
            name={collected ? 'disc' : 'disc'}
            type="feather"
            color={collected ? common.green : colors.discreet}
            containerStyle={{ ...styles.icon, opacity }}
            size={ICON_SIZE.medium}
            onPress={onCollectPress}
        />
    );
};

export default React.memo(ActionCollect);
