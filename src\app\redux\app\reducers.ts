import { APP_STARTED, AppAction, SET_LOCALE, SET_SCHEME, SET_MARK_EPISODE_VIEWED } from 'app/app/redux/app/actions';
import { ColorSchemeName, Appearance } from 'react-native';

export interface ApplicationState {
    appStarted: boolean;
    locale: MS.Locales;
    scheme: ColorSchemeName;
    markEpisodeViewed: boolean;
}

export const initialAppState: ApplicationState = {
    appStarted: false,
    locale: 'en',
    scheme: Appearance.getColorScheme(),
    markEpisodeViewed: true
};

export function app(state: ApplicationState = initialAppState, action: AppAction) {
    switch (action.type) {
        // case SIGN_OUT_SUCCESS:
        // case RESET_STORE:
        //     return initialAppState;
        case APP_STARTED: {
            return { ...state, appStarted: action.payload.appStarted };
        }
        case SET_LOCALE: {
            return { ...state, locale: action.payload.locale };
        }
        case SET_SCHEME: {
            return { ...state, scheme: action.payload.scheme };
        }
        case SET_MARK_EPISODE_VIEWED: {
            return { ...state, markEpisodeViewed: action.payload.markEpisodeViewed };
        }
        default:
            return state;
    }
}
