import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Icon } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { common } from 'app/app/styles/themes';
import MaskedView from '@react-native-masked-view/masked-view';
import { useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import StringHelper from 'app/services/helpers/StringHelper';
import _ from 'lodash';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1
    },
    mask: {
        backgroundColor: 'transparent',
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export interface SmallRatingProps {
    trakt: number;
    max?: number;
    size?: number;
}

const SmallRating = ({ trakt, max, size }: SmallRatingProps) => {
    const { rating, votes } = useSelector((state: MSState) => {
        return {
            rating: state.entities.show[trakt]?.attributes.rating,
            votes: state.entities.show[trakt]?.attributes.votes
        };
    }, _.isEqual);
    if (!rating || !votes) {
        return null;
    }
    const width = (size * rating) / max;
    const color = common.red;
    return (
        <View style={styles.container}>
            <MaskedView
                style={{
                    width: size
                }}
                maskElement={
                    <View style={styles.mask}>
                        <Icon type="font-awesome" name="heart" size={size} />
                    </View>
                }
            >
                <View
                    style={{
                        backgroundColor: color,
                        width,
                        height: size
                    }}
                />
            </MaskedView>
            <Text textStyle="normal" color={color}>
                {rating.toFixed(1)}
            </Text>
            <Text textStyle="small">{StringHelper.nFormat(votes)}</Text>
        </View>
    );
};
SmallRating.defaultProps = {
    max: 10,
    size: ICON_SIZE.small
};
export default React.memo(SmallRating);
