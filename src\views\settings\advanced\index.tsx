import React, { useState } from 'react';
import Container from 'app/app/components/containers/Container';
import { Header, ListItem, Icon } from 'react-native-elements';
import Back from 'app/app/components/header/Back';
import i18n from 'i18n-js';
import useGuest from 'app/app/hooks/useGuest';
import Constants from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import FileHelper from 'app/services/helpers/FileHelper';
import Database from 'app/services/storage/DB';
import { useDispatch, useSelector } from 'react-redux';
import { getMyRatings } from 'app/app/redux/sync/rate';
import { getWatched } from 'app/app/redux/sync/watch';
import Toast from 'react-native-toast-message';
import { defaultOptions } from 'app/app/components/elements/Toast';
import { resetStore, AppAction } from 'app/app/redux/app/actions';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';

export interface AdvancedProps {}

const Advanced = () => {
    // STATE
    const [loading, setLoading] = useState(false);

    // REDUX
    const dispatch: MS.Dispatch<AppAction> = useDispatch();
    const stateSize = JSON.stringify(useSelector(state => state)).length;

    const isGuest = useGuest();
    const { navigate } = useNavigation();

    // EVENTS

    const clearStore = () => {
        dispatch(resetStore()).then(() => {
            navigate(Constants.Navigation.Auth.CHECK);
        });
    };

    const syncData = () => {
        setLoading(true);
        Promise.all([Database.createTables(false), dispatch(getMyRatings()), dispatch(getWatched())])
            .then(() => {
                setLoading(false);
                Toast.show({
                    ...defaultOptions,
                    text1: i18n.t('TRAKT_DATA_SUCCESS')
                });
            })
            .catch(() => {
                Toast.show({
                    ...defaultOptions,
                    type: 'error',
                    text1: i18n.t('TRAKT_DATA_ERROR')
                });
            });
    };
    return (
        <Container>
            <Header leftComponent={<Back />} centerComponent={{ text: i18n.t('ADVANCED') }} />
            {!isGuest && (
                <ListItem
                    onPress={syncData}
                    rightElement={<Icon name="cloud-sync" type="material-community" />}
                    title={i18n.t('SYNC_TRAKT_DATA')}
                />
            )}
            {__DEV__ && (
                <ListItem
                    rightElement={<Icon name="database" type="material-community" />}
                    onPress={() => {
                        navigate(Constants.Navigation.Database.VIEW);
                    }}
                    title="View Database"
                />
            )}
            <ListItem
                onPress={clearStore}
                rightElement={<Icon name="trash" type="feather" />}
                title={i18n.t('RESET_CACHE')}
                subtitle={FileHelper.bytesToSize(stateSize)}
            />
            {loading && <ScreenLoader />}
        </Container>
    );
};
export default React.memo(Advanced);
