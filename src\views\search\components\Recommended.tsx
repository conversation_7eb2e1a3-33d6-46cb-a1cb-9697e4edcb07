import React, { useEffect, useContext } from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { recommendedShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import Hide from 'app/app/components/show/Hide';
import ShowMore from 'app/app/components/elements/ShowMore';
import Text from 'app/app/components/elements/Text';
import { ThemeContext } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import Carousel from 'react-native-snap-carousel';
import { Dimensions } from 'react-native';
import _ from 'lodash';
import ShowCardItem from 'app/app/components/listItems/show/ShowCardItem';
import ApiHelper from 'app/services/helpers/ApiHelper';

const { width: screenWidth } = Dimensions.get('window');
export interface RecommendedProps {}

type Props = RecommendedProps & NavigationInjectedProps<{}>;
const Recommended = ({ navigation }: Props) => {
    const dispatch = useDispatch();
    const {
        theme: { colors }
    } = useContext(ThemeContext);
    const {
        result,
        resultLength,
        fetched
    }: {
        result: number[];
        resultLength: number;
        fetched: number;
    } = useSelector((state: MSState) => {
        return {
            result: state.shows.recommended.result.slice(0, Constants.App.CARD_TEASER_ITEMS),
            resultLength: state.shows.recommended.result.length,
            fetched: state.shows.recommended.fetched
        };
    }, _.isEqual);
    useEffect(() => {
        if (ApiHelper.shouldFetch(fetched, UpdateThresholds.LOW)) {
            const params: RequestParams = {
                extended: 'full',
                limit: 20
            };
            dispatch(recommendedShows(params));
        }
    }, []);

    useEffect(() => {
        if (result.length < Constants.App.CARD_TEASER_ITEMS) {
            const params: RequestParams = {
                extended: 'full',
                limit: 20
            };
            dispatch(recommendedShows(params));
        }
    }, [resultLength]);
    const cardWidth = screenWidth / 3;
    const renderItem = ({ item: trakt }: { item: number }) => {
        const rightElement = (
            <Hide
                {...{
                    id: trakt,
                    iconProps: {
                        name: 'times',
                        color: colors.error,
                        size: ICON_SIZE.normal
                    }
                }}
            />
        );
        return (
            <ShowCardItem
                {...{
                    trakt,
                    key: trakt,
                    rightElement,
                    square: true,
                    rounded: true,
                    width: cardWidth
                }}
            />
        );
    };
    const onPress = () => {
        navigation.navigate(Constants.Navigation.Search.RECOMMENDED);
    };
    return (
        <>
            <Text textStyle="doubleTitle" centered>
                {i18n.t('RECOMMENDED_SHOWS')}
            </Text>
            <Carousel<number>
                data={result}
                renderItem={renderItem}
                sliderWidth={screenWidth}
                itemWidth={cardWidth}
                inactiveSlideOpacity={0.7}
                inactiveSlideScale={0.7}
                firstItem={1}
                horizontal
                enableMomentum
                enableSnap={false}
            />

            <ShowMore onPress={onPress} />
        </>
    );
};

export default withNavigation(React.memo(Recommended, _.isEqual));
