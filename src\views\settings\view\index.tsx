import React, { useState, useEffect, memo } from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import Container from 'app/app/components/containers/Container';
import { ListItem, ThemeConsumer, Icon } from 'react-native-elements';
import i18n from 'i18n-js';
import { ThemeColors, Theme, createTheme } from 'app/app/styles/themes';
import { ColorSchemeName, Appearance, StyleSheet, Platform } from 'react-native';
import Storage from 'app/services/storage/Storage';
import { TabLabels } from 'app/app/navigation/AppStack';
import English from 'app/assets/images/languages/english.svg';
import Greek from 'app/assets/images/languages/greek.svg';
import { AuthAction, guestSignOut, signOut } from 'app/app/redux/auth/actions';
import {
    AppAction,
    setScheme,
    setLocale,
    setMarkEpisodeViewed as appSetEpisodeViewed
} from 'app/app/redux/app/actions';
import { useSelector, useDispatch } from 'react-redux';
import Constants from 'app/app/Constants';
import ExpoConstants from 'expo-constants';
import Header from 'app/app/components/header/Header';
import { MSState } from 'app/app/redux/redux';
import _ from 'lodash';
import { SPACING } from 'app/app/styles/sizes';

import LocaleHelper from 'app/services/helpers/LocaleHelper';
import Banner from 'app/app/components/containers/Banner';
import useGuest from 'app/app/hooks/useGuest';
import LanguageButton from './components/LanguageButton';

const styles = StyleSheet.create({
    themeIcon: {
        paddingHorizontal: SPACING.small * 0.5
    }
});
export interface SettingsProps {}

interface StateProps {
    user: Trakt.User;
    loggedIn: boolean;
    settingEpisodeMarkViewed: boolean;
}

type Props = SettingsProps & NavigationInjectedProps<{}>;
const Settings = ({ navigation }: Props) => {
    const isGuest = useGuest();

    const { user, loggedIn, settingEpisodeMarkViewed } = useSelector<MSState, StateProps>(state => {
        const { user } = state.user.settings;
        return {
            user,
            loggedIn: !!state.auth.trakt.token || isGuest,
            settingEpisodeMarkViewed: state.app.markEpisodeViewed
        };
    });
    const [currentLocale, setCurrentLocale] = useState(i18n.currentLocale());
    const [markEpisodeViewed, setMarkEpisodeViewed] = useState(settingEpisodeMarkViewed);

    const dispatch: MS.Dispatch<AuthAction | AppAction> = useDispatch();

    useEffect(() => {
        if (!loggedIn) {
            navigation.navigate(Constants.Navigation.Auth.SIGN_IN);
        }
    }, [loggedIn]);
    const changeTheme = replaceTheme => {
        Storage.getItem(Storage.KEY_THEME).then(scheme => {
            const prevScheme = scheme || Appearance.getColorScheme();
            const newScheme = prevScheme === 'dark' ? 'light' : 'dark';
            const theme = createTheme(newScheme);
            // Appearance.set({
            //    colorScheme: newScheme
            // });
            replaceTheme(theme);
            Storage.setItem(Storage.KEY_THEME, newScheme);
            dispatch(setScheme(newScheme));
        });
    };

    const changeLocale = (locale: MS.Locales) => {
        Storage.setItem(Storage.KEY_LOCALE, locale).then(() => {
            i18n.locale = locale;
            // LocaleHelper.setMomentLocale(locale);
            const labels: TabLabels = {
                settings: i18n.t('SETTINGS'),
                search: i18n.t('SEARCH'),
                calendar: i18n.t('CALENDAR'),
                lists: i18n.t('LISTS'),
                watching: i18n.t('WATCHING')
            };
            navigation.setParams({ labels });
            setCurrentLocale(locale);
            dispatch(setLocale(locale));
        });
    };

    const appVersion = ExpoConstants.manifest.version;
    const versionCode =
        Platform.OS === 'android' && !__DEV__
            ? `(${i18n.t('BUILD')} ${ExpoConstants.platform.android.versionCode})`
            : '';

    const showUser = () => {
        navigation.navigate(Constants.Navigation.Settings.USER);
    };

    const exit = () => {
        if (isGuest) {
            dispatch(guestSignOut());
        } else {
            dispatch(signOut());
        }
    };

    const onChangeMarkepisodeViewed = () => {
        setMarkEpisodeViewed(!markEpisodeViewed);
        dispatch(appSetEpisodeViewed(!markEpisodeViewed));
    };

    const advanced = () => {
        navigation.navigate(Constants.Navigation.Settings.ADVANCED);
    };
    return (
        <ThemeConsumer<Theme>>
            {({ theme, replaceTheme }) => {
                const { scheme, colors }: { colors: ThemeColors; scheme: ColorSchemeName } = theme;
                return (
                    <>
                        <Header
                            centerComponent={{
                                text: i18n.t('ACCOUNT_AND_SETTINGS')
                            }}
                        />
                        <Container withPadding={false}>
                            {user && (
                                <ListItem onPress={showUser} title={i18n.t('ACCOUNT')} subtitle={user.name} chevron />
                            )}
                            {isGuest && (
                                <ListItem
                                    title={i18n.t('GUEST')}
                                    leftAvatar={{
                                        icon: {
                                            name: 'user',
                                            type: 'font-awesome',
                                            color: scheme === 'dark' ? colors.primary : colors.primary
                                        }
                                    }}
                                />
                            )}

                            <ListItem
                                title={i18n.t('THEME')}
                                subtitle={scheme === 'dark' ? i18n.t('DARK') : i18n.t('LIGHT')}
                                rightElement={
                                    // <Switch value={scheme === 'dark'} onValueChange={() => changeTheme(replaceTheme)} />
                                    <>
                                        <Icon
                                            {...{
                                                disabled: scheme === 'light',
                                                type: scheme === 'dark' ? 'material-community' : 'font-awesome-5',
                                                name: 'circle',
                                                color: scheme === 'dark' ? 'white' : 'black',
                                                size: scheme === 'light' ? 38 : 40,
                                                containerStyle: { ...styles.themeIcon },
                                                onPress: () => changeTheme(replaceTheme)
                                            }}
                                        />
                                        <Icon
                                            {...{
                                                disabled: scheme === 'dark',
                                                type: scheme === 'dark' ? 'font-awesome-5' : 'material-community',
                                                name: 'circle',
                                                color: scheme === 'dark' ? 'white' : 'black',
                                                size: scheme === 'dark' ? 36 : 42,
                                                containerStyle: { ...styles.themeIcon },
                                                onPress: () => changeTheme(replaceTheme)
                                            }}
                                        />
                                    </>
                                }
                            />
                            <ListItem
                                title={i18n.t('LANGUAGE')}
                                subtitle={i18n.t(LocaleHelper.getLanguageLabel(currentLocale as MS.Locales))}
                                rightElement={
                                    <>
                                        <LanguageButton
                                            size={32}
                                            Element={Greek}
                                            disabled={currentLocale === 'el'}
                                            onPress={() => {
                                                changeLocale('el');
                                            }}
                                        />
                                        <LanguageButton
                                            size={32}
                                            Element={English}
                                            disabled={currentLocale === 'en'}
                                            onPress={() => {
                                                changeLocale('en');
                                            }}
                                        />
                                    </>
                                }
                            />
                            {!isGuest && (
                                <ListItem
                                    title={i18n.t('RATING_EPISODES')}
                                    subtitle={
                                        markEpisodeViewed
                                            ? i18n.t('MARK_RATING_AS_VIEWED')
                                            : i18n.t('NOT_MARK_RATING_AS_VIEWED')
                                    }
                                    switch={{
                                        value: markEpisodeViewed,
                                        onValueChange: onChangeMarkepisodeViewed
                                    }}
                                />
                            )}
                            <ListItem title={i18n.t('ADVANCED')} chevron onPress={advanced} />
                            <ListItem title={i18n.t('APP_VERSION')} rightTitle={`${appVersion} ${versionCode}`} />
                            {!isGuest && <ListItem title={i18n.t('SIGN_OUT')} onPress={() => exit()} />}
                            {isGuest && <ListItem title={i18n.t('SIGN_IN')} onPress={() => exit()} />}
                        </Container>
                        <Banner />
                    </>
                );
            }}
        </ThemeConsumer>
    );
};

export default withNavigation(memo(Settings, _.isEqual));
