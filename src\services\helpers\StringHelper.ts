import i18n from 'i18n-js';

export default class StringHelper {
    static CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    static createCode = (length: number = 6) => {
        const charactersLength = StringHelper.CHARS.length;
        let code = '';
        for (let i = 0; i < length; i += 1) {
            code += StringHelper.CHARS.charAt(Math.floor(Math.random() * charactersLength));
        }
        return code;
    };

    static nFormat = (count: number, digits: number = null): string => {
        const SI_SYMBOL = ['', 'k', 'M', 'G', 'T', 'P', 'E'];
        if (!count) {
            return '';
        }
        // what tier? (determines SI symbol)
        // eslint-disable-next-line no-bitwise
        const tier = (Math.log10(count) / 3) | 0;

        // if zero, we don't need a suffix
        if (tier === 0) return count.toFixed(0);

        // get suffix and determine scale
        const suffix = SI_SYMBOL[tier];
        // eslint-disable-next-line no-restricted-properties
        const scale = Math.pow(10, tier * 3);

        // scale the number
        const scaled = count / scale;

        // format number and add suffix
        const nDigits = digits || suffix === 'M' ? 3 : suffix === 'k' ? 0 : 0;
        return scaled.toFixed(nDigits) + suffix;
    };

    static isJSON = (text: string): boolean => {
        try {
            JSON.parse(text);
        } catch (e) {
            return false;
        }
        return true;
    };

    static sanitizeSearch(term: string) {
        const regex = /([.\-&!"~:/*+?^${}()|[\]\\])/gi;
        return term.replace(regex, '/$1');
    }

    static toSentenceCase(text: string): string {
        if (!text) {
            return text;
        }
        if (text.length === 1) {
            return text.toUpperCase();
        }
        return `${text.substr(0, 1).toUpperCase()}${text.substr(1)}`;
    }

    static formatNumber(value: number, decimals: number = 0): string {
        return i18n.toNumber(value, { precision: decimals, delimiter: '.', separator: ',' });
    }
}
