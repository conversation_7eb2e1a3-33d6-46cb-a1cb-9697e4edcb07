import React, { useContext } from 'react';
import { View, StyleSheet } from 'react-native';
import { withNavigation } from 'react-navigation';
import Text from 'app/app/components/elements/Text';
import ShowMore from 'app/app/components/elements/ShowMore';
import { SPACING } from 'app/app/styles/sizes';
import { ThemeProps, ThemeContext } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';

const styles = StyleSheet.create({
    container: {
        paddingBottom: SPACING.large
    }
});
export interface TeaserProps<P> {
    title: string;
    subtitle?: string;
    data: P[];
    renderItem: (item: P, index: number) => JSX.Element;
    showMore: boolean;
    onPress: () => void;
}

const Teaser = <P,>({ showMore, title, subtitle, data, renderItem, onPress }: TeaserProps<P>) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    return (
        <View style={styles.container}>
            <View>
                <Text centered textStyle="doubleTitle">
                    {title}
                </Text>
                {!!subtitle && (
                    <Text centered textStyle="small" color={colors.discreet}>
                        {subtitle}
                    </Text>
                )}
            </View>
            {data.map((item: P, index: number) => renderItem(item, index))}
            {showMore && <ShowMore onPress={onPress} />}
        </View>
    );
};

export default React.memo(withNavigation(Teaser));
