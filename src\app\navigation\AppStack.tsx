import React from 'react';
import { createBottomTabNavigator } from 'react-navigation-tabs';
import { Icon, ThemeConsumer } from 'react-native-elements';

import { View, StyleSheet } from 'react-native';
import { Theme } from 'styles/themes';
import i18n from 'i18n-js';
import ThemedTabBar from './ThemedTabBar';
import Text from '../components/elements/Text';
import Calendar from './stacks/Calendar';
import Settings from './stacks/Settings';
import Search from './stacks/Search';
import Lists from './stacks/Lists';
import Watching from './stacks/Watching';

export const isTabBarVisible = navigation => {
    const routes = navigation.state?.routes || [];
    const route = routes[routes.length - 1] || null;
    if (route && route.params?.noTabBar === true) {
        return false;
    }
    return true;
};
const styles = StyleSheet.create({
    tabBarIconContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 50,
        borderRadius: 25
    }
});
export interface TabLabels {
    settings: string;
    search: string;
    calendar: string;
    lists: string;
    watching: string;
}

const AppStack = createBottomTabNavigator(
    {
        Calendar: {
            screen: Calendar,
            navigationOptions: ({ navigation }) => {
                return {
                    tabBarVisible: isTabBarVisible(navigation)
                };
            }
        },
        Search: {
            screen: Search,
            navigationOptions: ({ navigation }) => {
                return {
                    tabBarVisible: isTabBarVisible(navigation)
                };
            }
        },
        Lists: {
            screen: Lists,
            navigationOptions: ({ navigation }) => {
                return {
                    tabBarVisible: isTabBarVisible(navigation)
                };
            }
        },
        Watching: {
            screen: Watching,
            navigationOptions: ({ navigation }) => {
                return {
                    tabBarVisible: isTabBarVisible(navigation)
                };
            }
        },
        Settings: {
            screen: Settings,
            navigationOptions: ({ navigation }) => {
                return {
                    tabBarVisible: isTabBarVisible(navigation)
                };
            }
        }
    },
    {
        initialRouteName: 'Search',
        tabBarComponent: ThemedTabBar,
        defaultNavigationOptions: ({ navigation }) => {
            return {
                tabBarIcon: ({ focused, tintColor }: { focused: boolean; tintColor: string }) => {
                    const { routeName } = navigation.state;
                    let iconName: string;
                    let type: string = 'feather';

                    if (routeName === 'Calendar') {
                        type = 'octicon';
                        iconName = 'calendar';
                    } else if (routeName === 'Settings') {
                        iconName = 'settings';
                    } else if (routeName === 'Search') {
                        iconName = 'search';
                    } else if (routeName === 'Lists') {
                        iconName = 'list';
                    } else if (routeName === 'Watching') {
                        type = 'ionicon';
                        iconName = 'ios-tv';
                    }

                    // You can return any component that you like here!

                    return (
                        <ThemeConsumer<Theme>>
                            {() => {
                                return (
                                    <View style={[styles.tabBarIconContainer, {}]}>
                                        <Icon type={type} name={iconName} size={focused ? 24 : 24} color={tintColor} />
                                    </View>
                                );
                            }}
                        </ThemeConsumer>
                    );
                },
                tabBarLabel: ({ tintColor }: { tintColor: string }) => {
                    const { routeName } = navigation.state;
                    const labels: TabLabels = navigation.getParam('labels') || {
                        calendar: i18n.t('CALENDAR'),
                        search: i18n.t('SEARCH'),
                        settings: i18n.t('SETTINGS'),
                        lists: i18n.t('LISTS'),
                        watching: i18n.t('WATCHING')
                    };
                    let label: string;
                    if (routeName === 'Settings') {
                        label = labels.settings;
                    } else if (routeName === 'Calendar') {
                        label = labels.calendar;
                    } else if (routeName === 'Search') {
                        label = labels.search;
                    } else if (routeName === 'Lists') {
                        label = labels.lists;
                    } else if (routeName === 'Watching') {
                        label = labels.watching;
                    }
                    return (
                        <Text centered color={tintColor} textStyle="nano">
                            {label}
                        </Text>
                    );
                }
            };
        }
    }
);
export default AppStack;
