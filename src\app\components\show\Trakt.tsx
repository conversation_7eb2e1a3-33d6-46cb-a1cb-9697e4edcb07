import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import { SPACING } from 'app/app/styles/sizes';
import { common, Theme } from 'app/app/styles/themes';
import { ThemeProps, ThemeContext } from 'react-native-elements';
import TraktLogo from 'app/assets/images/companies/trakt.svg';
import i18n from 'i18n-js';
import StringHelper from 'app/services/helpers/StringHelper';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    imdbContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' },
    rate: { padding: SPACING.small }
});

export interface IMDBProps {
    rate: number;
    votes: number;
    size?: number;
}

const Trakt = ({ rate, votes, size }: IMDBProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    return (
        <View style={{ ...styles.imdbContainer }}>
            <TraktLogo {...{ width: size, height: size / 2 }} />
            {rate && votes ? (
                <>
                    <Text style={{ ...styles.rate }} bold textStyle="title" color={colors.primary}>
                        {rate.toFixed(2)}
                    </Text>
                    <Text textStyle="nano" color={colors.discreet}>
                        {`${StringHelper.nFormat(votes)} ${i18n.t('VOTES')}`}
                    </Text>
                </>
            ) : (
                <Text style={{ ...styles.rate }} bold textStyle="title" color={common.yellow}>
                    -
                </Text>
            )}
        </View>
    );
};

Trakt.defaultProps = {
    size: 60
};

export default React.memo(Trakt);
