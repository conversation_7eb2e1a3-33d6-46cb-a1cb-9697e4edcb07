import { combineReducers } from 'redux';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import { RESET_STORE } from '../app/actions';
import {
    CREATE_LIST_REQUEST,
    CREATE_LIST_SUCCESS,
    CREATE_LIST_ERROR,
    DELETE_LIST_REQUEST,
    DELETE_LIST_SUCCESS,
    DELETE_LIST_ERROR,
    UPDATE_LIST_REQUEST,
    UPDATE_LIST_SUCCESS,
    UPDATE_LIST_ERROR
} from '../list/actions';
import {
    RESET_FORM_SUCCESS,
    GET_CERTIFICATIONS_SUCCESS,
    MetaAction,
    GET_GENRES_SUCCESS,
    GET_NETWORKS_SUCCESS,
    UPDATE_WATCHED,
    UPDATE_RATINGS,
    UPDATE_COLLECTION
} from './actions';
import {
    POST_COMMENT_ERROR,
    POST_COMMENT_REQUEST,
    POST_COMMENT_SUCCESS,
    CommentAction,
    POST_COMMENT_REPLY_REQUEST,
    POST_COMMENT_REPLY_SUCCESS,
    POST_COMMENT_REPLY_ERROR,
    UPDATE_COMMENT_REQUEST,
    UPDATE_COMMENT_SUCCESS,
    UPDATE_COMMENT_ERROR
} from '../comment/actions';
import { SyncAction } from '../sync/actions';
import {
    GET_WATCHED_HISTORY_FOR_FAVS_REQUEST,
    GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS,
    GET_WATCHED_HISTORY_FOR_FAVS_ERROR
} from '../sync/watch';

export interface FormState {
    loading: boolean;
    success: boolean;
    error: string;
}

export const initialFormState: FormState = {
    loading: false,
    success: false,
    error: null
};

export function form(state: FormState = initialFormState, action: MetaAction | CommentAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
        case RESET_FORM_SUCCESS:
            return initialFormState;
        case CREATE_LIST_REQUEST:
        case UPDATE_LIST_REQUEST:
        case DELETE_LIST_REQUEST:
        case POST_COMMENT_REQUEST:
        case POST_COMMENT_REPLY_REQUEST:
        case UPDATE_COMMENT_REQUEST: {
            return { loading: true, success: false, error: null };
        }
        case CREATE_LIST_SUCCESS:
        case UPDATE_LIST_SUCCESS:
        case DELETE_LIST_SUCCESS:
        case POST_COMMENT_SUCCESS:
        case POST_COMMENT_REPLY_SUCCESS:
        case UPDATE_COMMENT_SUCCESS: {
            return { loading: false, success: true, error: null };
        }
        case CREATE_LIST_ERROR:
        case UPDATE_LIST_ERROR:
        case DELETE_LIST_ERROR: {
            return { loading: false, success: false, error: 'An error occured' };
        }
        case POST_COMMENT_REPLY_ERROR:
        case POST_COMMENT_ERROR:
        case UPDATE_COMMENT_ERROR: {
            const {
                payload: { error }
            } = action as CommentAction;
            return { loading: false, success: false, error };
        }
        default:
            return state;
    }
}

export interface CertificationsState {
    result: string[];
    fetched: number;
}

export const initialCertificationsState: CertificationsState = {
    result: [],
    fetched: 0
};

export function certifications(state: CertificationsState = initialCertificationsState, action: MetaAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialCertificationsState;
        case GET_CERTIFICATIONS_SUCCESS: {
            const {
                normalized: { result },
                fetched
            } = action.payload;
            return { result, fetched };
        }
        default:
            return state;
    }
}

export interface GenreState {
    result: string[];
    fetched: number;
}

export const initialGenreState: GenreState = {
    result: [],
    fetched: 0
};

export function genres(state: GenreState = initialGenreState, action: MetaAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialGenreState;
        case GET_GENRES_SUCCESS: {
            const {
                normalized: { result },
                fetched
            } = action.payload;
            return { result, fetched };
        }
        default:
            return state;
    }
}

export interface NetworkState {
    result: string[];
    fetched: number;
}

export const initialNetworkState: NetworkState = {
    result: [],
    fetched: 0
};

export function networks(state: NetworkState = initialNetworkState, action: MetaAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialNetworkState;
        case GET_NETWORKS_SUCCESS: {
            const { networks: result, fetched } = action.payload;
            return { result, fetched };
        }
        default:
            return state;
    }
}

export interface DBUpdatesState {
    watched: number;
    ratings: number;
    collection: number;
}

export const initialDBUpdatesState: DBUpdatesState = {
    ratings: 0,
    watched: 0,
    collection: 0
};

export function dbUpdates(state: DBUpdatesState = initialDBUpdatesState, action: MetaAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialDBUpdatesState;
        case UPDATE_WATCHED: {
            return { ...state, watched: new Date().getTime() };
        }
        case UPDATE_RATINGS: {
            return { ...state, ratings: new Date().getTime() };
        }
        case UPDATE_COLLECTION: {
            return { ...state, collection: new Date().getTime() };
        }
        default:
            return state;
    }
}

export interface ImportState {
    loading: boolean;
    shows: number[];
}

export const initialImportState: ImportState = {
    loading: false,
    shows: []
};

export function importShows(state: ImportState = initialImportState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialImportState;
        case GET_WATCHED_HISTORY_FOR_FAVS_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS: {
            return {
                loading: false,
                shows: action.payload.normalized.result as number[]
            };
        }
        case GET_WATCHED_HISTORY_FOR_FAVS_ERROR: {
            return { ...state, loading: false, fetched: 0 };
        }
        default:
            return state;
    }
}

export interface MetaState {
    certifications: CertificationsState;
    dbUpdates: DBUpdatesState;
    importShows: ImportState;
    form: FormState;
    genres: GenreState;
    networks: NetworkState;
}

export const initialMetaState: MetaState = {
    certifications: initialCertificationsState,
    dbUpdates: initialDBUpdatesState,
    importShows: initialImportState,
    form: initialFormState,
    genres: initialGenreState,
    networks: initialNetworkState
};
export default combineReducers({
    certifications,
    dbUpdates,
    importShows,
    form,
    genres,
    networks
});
