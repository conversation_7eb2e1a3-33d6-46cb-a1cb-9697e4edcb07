import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Icon } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import StringHelper from 'app/services/helpers/StringHelper';
import { common } from 'app/app/styles/themes';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export interface ListedProps {
    trakt: number;
    size: number;
    nFormatted: boolean;
}

const Watchers = ({ trakt, size, nFormatted }: ListedProps) => {
    const locale = useSelector((state: MSState) => state.app.locale);
    const listed = useSelector((state: MSState) => state.entities.show[trakt].meta.list_count);
    const listedFormatted = !nFormatted
        ? listed
            ? listed.toLocaleString(locale)
            : null
        : StringHelper.nFormat(listed);
    return (
        <View style={styles.container}>
            <Icon name="list" type="font-awesome" color={common.blue} {...{ size }} />
            <Text>{listedFormatted}</Text>
        </View>
    );
};
Watchers.defaultProps = {
    size: ICON_SIZE.small,
    nFormatted: false
};
export default React.memo(Watchers);
