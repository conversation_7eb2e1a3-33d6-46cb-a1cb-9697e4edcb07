import React, { useContext, useEffect, useState } from 'react';
import { StyleSheet, Dimensions, View } from 'react-native';
import { NavParams } from 'app/app/navigation/Navigator';
import { NavigationInjectedProps, withNavigation } from 'react-navigation';
import Container from 'app/app/components/containers/Container';
import Text from 'app/app/components/elements/Text';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import AnimatedCover from 'app/app/components/header/AnimatedCover';
import Animated from 'react-native-reanimated';
import { useValues, onScrollEvent } from 'react-native-redash';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme, HEADER_HEIGHT } from 'app/app/styles/themes';
import { getSeasonDetails } from 'app/app/redux/season/actions';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import Episodes from 'app/app/components/show/Episodes';
import ActionsBar from 'app/app/components/show/actionBar/ActionsBar';
import People from 'app/app/components/show/People';
import useWatched from 'app/app/hooks/useWatched';
import AverageRating from 'app/app/components/show/AverageRating';
import { getSeasonComments } from 'app/app/redux/comment/actions';
import MainComment from 'app/app/components/show/MainComment';
import { CommentNavParams } from 'app/views/comment/list';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import { PostCommentNavParams } from 'app/views/comment/post';
import useGuest from 'app/app/hooks/useGuest';
import Toast from 'react-native-toast-message';
import { defaultOptions } from 'app/app/components/elements/Toast';
import { getTraktEpisodes } from 'app/app/redux/episode/actions';
import ApiHelper from 'app/services/helpers/ApiHelper';
import MoreStats from 'app/app/components/show/MoreStats';
import Help from 'app/app/components/containers/Help';
import showActionsLight from 'app/assets/images/help/show_actions_light.png';
import showActionsDark from 'app/assets/images/help/show_actions_dark.png';
import HelpHelper from 'app/services/helpers/HelpHelper';
import ShowHelp from 'components/show/actionBar/Help';

export interface SeasonNavParams extends NavParams {
    ids: Trakt.Ids;
    seasonNumber: number;
}
const styles = StyleSheet.create({
    section: {
        padding: SPACING.medium
    },
    subSection: {
        padding: SPACING.medium,
        paddingBottom: 0
    },
    postCommentContainer: {
        paddingLeft: SPACING.medium
    }
});
const { width } = Dimensions.get('window');
export interface SeasonProps {}

export type Props = SeasonProps & NavigationInjectedProps<SeasonNavParams>;
const defaultArray = [];
const Season = ({ navigation }: Props) => {
    const { seasonNumber, ids } = navigation.state.params;
    const [helpVisible, setHelpVisible] = useState(false);

    const dispatch = useDispatch();
    const isGuest = useGuest();
    const { percent } = useWatched(ids.trakt, seasonNumber);
    const { showDetails, show, season, seasonDetails, commentId, traktEpisodes, traktEpisodesFetch } = useSelector(
        (state: MSState) => {
            const showDetails = state.entities.showDetails[ids.tmdb];
            const show = state.entities.show[ids.trakt];
            const season = ShowHelper.getSeason(state.entities.showDetails[ids.tmdb].attributes.seasons, seasonNumber);
            const detailsId =
                seasonNumber !== null &&
                state.seasons.details[ids.tmdb] &&
                state.seasons.details[ids.tmdb][seasonNumber]
                    ? state.seasons.details[ids.tmdb][seasonNumber].result
                    : null;
            const seasonDetails = detailsId ? state.entities.seasonDetails[detailsId] : null;

            return {
                showDetails,
                show,
                season,
                seasonDetails,
                commentId: state.comments.season[ids.trakt]?.[seasonNumber]?.main[0],
                traktEpisodes: state.episodes.trakt[ids.trakt]?.[seasonNumber]?.result || defaultArray,
                traktEpisodesFetch: state.episodes.trakt[ids.trakt]?.[seasonNumber]?.fetched || 0
            };
        }
    );
    useEffect(() => {
        if (!seasonDetails) {
            dispatch(getSeasonDetails(ids.tmdb, seasonNumber));
        }
        if (traktEpisodes.length === 0 || ApiHelper.shouldFetch(traktEpisodesFetch, UpdateThresholds.HIGH)) {
            dispatch(getTraktEpisodes(ids.trakt, seasonNumber));
        }
        if (!commentId) {
            dispatch(getSeasonComments(ids.trakt, seasonNumber, { limit: 1 }, true));
        }
        HelpHelper.getHelp('show').then(value => {
            setHelpVisible(value);
        });
    }, []);
    const [y] = useValues([0], []);
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    if (!show || !showDetails || !season || !seasonDetails) {
        return <ScreenLoader />;
    }
    const { name, poster_path, overview } = season;
    const { title } = show.attributes;
    const {
        episodes,
        credits: { cast, crew }
    } = seasonDetails.attributes;
    const backdrop = ShowHelper.getPosterUri(poster_path, 100, scheme);
    const imageHeight = width;
    const onScroll = onScrollEvent({ y });

    const bar = (
        <ActionsBar
            {...{
                type: 'seasons',
                ids,
                season: seasonNumber,
                imageHeight,
                isWatched: percent === 100,
                totalEpisodes: season.episode_count
            }}
        />
    );

    const onMoreCommentsPress = () => {
        const params: CommentNavParams = { id: ids.trakt, seasonNumber, type: 'season' };
        navigation.navigate(Constants.Navigation.Comment.LIST, params);
    };
    const onPostComment = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('POST_COMMENT_WARNING')
            });
        } else {
            const params: PostCommentNavParams = {
                type: 'season',
                id: ids.trakt,
                seasonNumber
            };
            // @ts-ignore
            navigation.push(Constants.Navigation.Comment.POST, params);
        }
    };

    return (
        <>
            <AnimatedCover title={`${name}`} src={backdrop} y={y} height={imageHeight} backgroundImage bar={bar} />
            <Container fullPage useScrollView={false}>
                <Animated.ScrollView
                    scrollEventThrottle={16}
                    {...{ onScroll }}
                    contentContainerStyle={{
                        backgroundColor: colors.background,
                        paddingTop: imageHeight - HEADER_HEIGHT
                    }}
                >
                    <View style={{ ...styles.section }}>
                        <Text textStyle="doubleTitle">{`${name}`}</Text>
                        <Text color={colors.discreet} textStyle="larger">{`${title} - ${episodes.length} ${i18n.t(
                            'EPISODES_SMALL'
                        )}`}</Text>
                        <AverageRating {...{ type: 'seasons', show: ids.trakt, season: seasonNumber }} />
                        <MoreStats {...{ id: ids.trakt, seasonNumber, title: `${title} - ${name}` }} />
                    </View>

                    {!!overview && (
                        <View style={{ ...styles.section }}>
                            <Text>{overview}</Text>
                        </View>
                    )}
                    <MainComment {...{ id: commentId, onMoreCommentsPress, onPostComment }} />
                    <View style={{ ...styles.section }}>
                        <Text textStyle="title">{i18n.t('EPISODES')}</Text>
                        <Episodes {...{ ids, episodes, seasonNumber }} />
                    </View>
                    {cast?.length > 0 && (
                        <>
                            <View>
                                <Text style={{ ...styles.subSection }} textStyle="title">
                                    {i18n.t('CAST')}
                                </Text>
                            </View>
                            <People
                                {...{
                                    people: cast,
                                    type: 'cast',
                                    limit: 7
                                }}
                            />
                        </>
                    )}
                    {crew?.length > 0 && (
                        <>
                            <View>
                                <Text style={{ ...styles.subSection }} textStyle="title">
                                    {i18n.t('CREW')}
                                </Text>
                            </View>
                            <People
                                {...{
                                    people: crew,
                                    type: 'crew',
                                    limit: 5
                                }}
                            />
                        </>
                    )}
                </Animated.ScrollView>
            </Container>
            <Help
                {...{
                    title: i18n.t('SHOW_HELP_TITLE'),
                    isVisible: helpVisible,
                    image: scheme === 'dark' ? showActionsDark : showActionsLight,
                    imageRatio: 48 / 412,
                    textElement: <ShowHelp />
                }}
            />
        </>
    );
};

export default withNavigation(React.memo(Season));
