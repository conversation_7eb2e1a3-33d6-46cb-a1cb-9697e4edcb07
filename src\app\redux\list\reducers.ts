import { RESET_STORE } from 'app/app/redux/app/actions';
import { combineReducers } from 'redux';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import {
    ListsAction,
    GET_LISTS_REQUEST,
    GET_LISTS_SUCCESS,
    GET_LISTS_ERROR,
    GET_LIST_ITEMS_SUCCESS,
    GET_LIST_ITEMS_ERROR,
    GET_LIST_ITEMS_REQUEST
} from './actions';

export interface ListsListState {
    loading: boolean;
    result: number[];
    fetched: number;
    refreshing: boolean;
}

export const initialListsListState: ListsListState = {
    loading: false,
    result: [],
    fetched: 0,
    refreshing: false
};

export function lists(state: ListsListState = initialListsListState, action: ListsAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialListsListState;
        case GET_LISTS_REQUEST: {
            const { refreshing } = action.payload;
            return { ...state, loading: !refreshing };
        }
        case GET_LISTS_SUCCESS: {
            const {
                normalized: { result },
                fetched
            } = action.payload;
            return { loading: false, result, fetched, refreshing: false };
        }
        case GET_LISTS_ERROR: {
            return { ...state, loading: false, refreshing: false };
        }
        default:
            return state;
    }
}

export interface ListItemsState {
    loading: boolean;
    [key: number]: { result: number[]; fetched; headers: Api.TraktHeaders };
    fetched: number;
    refreshing: boolean;
}

export const initialListItemsState: ListItemsState = {
    loading: false,
    fetched: 0,
    refreshing: false
};

export function listItems(state: ListItemsState = initialListItemsState, action: ListsAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialListItemsState;
        case GET_LIST_ITEMS_REQUEST: {
            const { refreshing } = action.payload;
            return { ...state, loading: true, refreshing };
        }
        case GET_LIST_ITEMS_SUCCESS: {
            const {
                id,
                fetched,
                headers,
                normalized: { result }
            } = action.payload;
            return { ...state, [id]: { result, fetched, headers }, loading: false, fetched, refreshing: false };
        }
        case GET_LIST_ITEMS_ERROR: {
            return { ...state, loading: false, refreshing: false };
        }
        default:
            return state;
    }
}
export interface ListsState {
    listItems: ListItemsState;
    lists: ListsListState;
}

export const initialListsState: ListsState = {
    listItems: initialListItemsState,
    lists: initialListsListState
};
export default combineReducers({
    listItems,
    lists
});
