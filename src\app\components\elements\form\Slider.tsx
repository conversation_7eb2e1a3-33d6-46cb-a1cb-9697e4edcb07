import React, { useContext, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import MultiSlider, { MultiSliderProps } from '@ptomasroos/react-native-multi-slider';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import Text from '../Text';

const LABEL_WIDTH = 80;
const styles = StyleSheet.create({
    container: {
        marginVertical: SPACING.normal
    },
    compContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 20
    },
    sliderContainer: {
        paddingHorizontal: SPACING.normal,
        marginRight: SPACING.medium,
        alignItems: 'flex-start',
        justifyContent: 'center'
    },
    marker: {
        height: 20,
        width: 20,
        borderRadius: 10
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: LABEL_WIDTH
    },
    label: {
        paddingVertical: 10,
        paddingLeft: SPACING.normal
    }
});

export interface SliderProps extends MultiSliderProps {
    title: string;
    onChange: (values: number[]) => void;
}

const Slider = (props: SliderProps) => {
    const { title, values, onChange, min, max } = props;
    const [sliderValues, setSliderValues] = useState(values);
    const [sliderWidth, setSliderWidth] = useState(300);
    const [, setLabelsWidth] = useState(100);
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const Marker = () => <View style={{ backgroundColor: colors.primary, ...styles.marker }} />;
    const setValues = values => setSliderValues(values);
    const minLabel = sliderValues[0] !== min ? sliderValues[0] : min;
    const maxLabel = sliderValues[1] !== max ? sliderValues[1] : max;
    const hasMinOrMax = sliderValues[0] !== min || sliderValues[1] !== max;
    const separator = !!minLabel || !!maxLabel ? '-' : null;
    return (
        <View style={{ ...styles.container }}>
            <Text style={{ ...styles.label }}>{title}</Text>
            <View
                style={{ ...styles.compContainer }}
                onLayout={e => {
                    setSliderWidth(e.nativeEvent.layout.width);
                }}
            >
                <View>
                    {hasMinOrMax ? (
                        <View
                            style={{ ...styles.row }}
                            onLayout={e => {
                                setLabelsWidth(e.nativeEvent.layout.width);
                            }}
                        >
                            <Text color={colors.primary}>{minLabel}</Text>
                            <Text color={colors.primary}>{separator}</Text>
                            <Text color={colors.primary}>{maxLabel}</Text>
                        </View>
                    ) : (
                        <Text color={colors.primary} style={{ ...styles.row }} centered>
                            {i18n.t('ANY')}
                        </Text>
                    )}
                </View>

                <MultiSlider
                    values={sliderValues}
                    sliderLength={sliderWidth - LABEL_WIDTH - SPACING.medium * 3}
                    trackStyle={{ backgroundColor: colors.onBackground }}
                    selectedStyle={{ backgroundColor: colors.primary }}
                    containerStyle={{ ...styles.sliderContainer }}
                    customMarker={Marker}
                    enabledTwo
                    onValuesChangeFinish={values => {
                        setValues(values);
                        onChange(values);
                    }}
                    onValuesChange={setValues}
                    {...props}
                />
            </View>
        </View>
    );
};

export default React.memo(Slider);
