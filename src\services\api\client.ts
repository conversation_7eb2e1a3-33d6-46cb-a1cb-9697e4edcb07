import { RSAA } from 'redux-api-middleware';
import queryString from 'query-string';
import RequestHelper from 'services/helpers/RequestHelper';
import Constants from 'app/app/Constants';
import _ from 'lodash';

export interface AdditionalParams {
    headers?: { [key: string]: string };
    body?: any;
}

export interface AdditionalQueryOptions {
    [key: string]: string;
}

export const buildApiUrl = (optionalQueryObject = {}) => {
    const queryParams = RequestHelper.createParamsObject(optionalQueryObject);
    return queryString.stringify({
        ...queryParams
    });
};

const getApiHeaders = (api: Api.Hosts): { [key: string]: string | number } => {
    switch (api) {
        case 'trakt':
        default:
            return {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                'trakt-api-version': 2,
                'trakt-api-key': Constants.Api.Trakt.CLIENT_ID
            };
        case 'tmdb': {
            return {
                Accept: 'application/json',
                'Content-Type': 'application/json'
            };
        }
        case 'omdb': {
            return {
                Accept: 'application/json',
                'Content-Type': 'application/json'
            };
        }
    }
};
const getQueryObject = (api: Api.Hosts) => {
    switch (api) {
        case 'trakt':
        default:
            return {};
        case 'tmdb':
            return { api_key: Constants.Api.Tmdb.API_KEY, language: 'en' };
        case 'omdb':
            return { apiKey: Constants.Api.Omdb.API_KEY };
    }
};
const getApiHost = (api: Api.Hosts): string => {
    switch (api) {
        case 'trakt':
        default:
            return Constants.Api.Trakt.ENDPOINT;
        case 'tmdb':
            return Constants.Api.Tmdb.ENDPOINT;
        case 'omdb':
            return Constants.Api.Omdb.ENDPOINT;
    }
};

/**
 * Universal async PPH API client
 * @param types
 * @param endpoint
 * @param method
 * @param optionalQueryObject
 * @param additionalParams
 * @param clientOptions {toFakeServer: The host of the API will be switched to fake server's host (set in .env)}
 * @returns {{}}
 */
const apiClient = (
    types,
    endpoint: string,
    method: 'GET' | 'HEAD' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'OPTIONS' = 'GET',
    api: Api.Hosts = 'trakt',
    optionalQueryObject: { [key: string]: string } = {},
    additionalParams: AdditionalParams = { body: {}, headers: {} }
) => {
    const queryParams: string = buildApiUrl(_.merge({}, getQueryObject(api), optionalQueryObject));
    const apiHost = getApiHost(api);
    // eslint-disable-next-line no-param-reassign
    additionalParams.headers = _.merge({}, getApiHeaders(api), additionalParams.headers);
    const options = {
        timeout: 10000 // API Request timeout, throws RequestError to the next redux middleware
    };
    return {
        [RSAA]: {
            types,
            endpoint: `${apiHost}${endpoint}?${queryParams}`,
            method,
            credentials: 'same-origin',
            options,
            fetch: async (...args) => {
                //if (__DEV__) {
                    console.info(`${method}:${apiHost}${endpoint}?${queryParams}`, additionalParams);
                //}
                const result = await fetch(args[0], {
                    ...args[1],
                    headers: additionalParams.headers
                });
                return result;
            },
            headers: additionalParams.headers,
            body: method !== 'GET' ? JSON.stringify(additionalParams.body) : null
        }
    };
};

export default apiClient;
