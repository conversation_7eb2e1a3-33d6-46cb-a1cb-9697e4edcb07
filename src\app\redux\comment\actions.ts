import { Action } from 'redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { CommentsSchema } from '../schemas';
import { MSState } from '../redux';
import { UNATHORIZED } from '../app/actions';

export const GET_SHOW_COMMENTS_REQUEST = 'GET_SHOW_COMMENTS_REQUEST';
export const GET_SHOW_COMMENTS_SUCCESS = 'GET_SHOW_COMMENTS_SUCCESS';
export const GET_SHOW_COMMENTS_ERROR = 'GET_SHOW_COMMENTS_ERROR';

export const GET_SEASON_COMMENTS_REQUEST = 'GET_SEASON_COMMENTS_REQUEST';
export const GET_SEASON_COMMENTS_SUCCESS = 'GET_SEASON_COMMENTS_SUCCESS';
export const GET_SEASON_COMMENTS_ERROR = 'GET_SEASON_COMMENTS_ERROR';

export const GET_EPISODE_COMMENTS_REQUEST = 'GET_EPISODE_COMMENTS_REQUEST';
export const GET_EPISODE_COMMENTS_SUCCESS = 'GET_EPISODE_COMMENTS_SUCCESS';
export const GET_EPISODE_COMMENTS_ERROR = 'GET_EPISODE_COMMENTS_ERROR';

export const GET_REPLIES_REQUEST = 'GET_REPLIES_REQUEST';
export const GET_REPLIES_SUCCESS = 'GET_REPLIES_SUCCESS';
export const GET_REPLIES_ERROR = 'GET_REPLIES_ERROR';

export const POST_COMMENT_REQUEST = 'POST_COMMENT_REQUEST';
export const POST_COMMENT_SUCCESS = 'POST_COMMENT_SUCCESS';
export const POST_COMMENT_ERROR = 'POST_COMMENT_ERROR';

export const POST_COMMENT_REPLY_REQUEST = 'POST_COMMENT_REPLY_REQUEST';
export const POST_COMMENT_REPLY_SUCCESS = 'POST_COMMENT_REPLY_SUCCESS';
export const POST_COMMENT_REPLY_ERROR = 'POST_COMMENT_REPLY_ERROR';

export const UPDATE_COMMENT_REQUEST = 'UPDATE_COMMENT_REQUEST';
export const UPDATE_COMMENT_SUCCESS = 'UPDATE_COMMENT_SUCCESS';
export const UPDATE_COMMENT_ERROR = 'UPDATE_COMMENT_ERROR';

export const DELETE_COMMENT_REQUEST = 'DELETE_COMMENT_REQUEST';
export const DELETE_COMMENT_SUCCESS = 'DELETE_COMMENT_SUCCESS';
export const DELETE_COMMENT_ERROR = 'DELETE_COMMENT_ERROR';

export interface CommentAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<Trakt.Comment>;
        headers: Api.TraktHeaders;
        id: number;
        seasonNUmber: number;
        episodeNumber: number;
        append: boolean;
        main: boolean;
        error: string;
        seasonNumber: number;
    };
}

export const getReplies = (id: number, params?: RequestParams) => {
    const append = params?.page > 1 || false;

    return apiClient(
        [
            { type: GET_REPLIES_REQUEST, payload: { append } },
            {
                type: GET_REPLIES_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [CommentsSchema]);
                        return { normalized, headers, id, append };
                    });
                }
            },
            GET_REPLIES_ERROR
        ],
        `/comments/${id}/replies`,
        'GET',
        'trakt',
        params
    );
};

export const getShowComments = (id: number, params?: RequestParams, main: boolean = false) => {
    const sort = params?.sort || 'highest';
    const append = params?.page > 1 || false;
    return apiClient(
        [
            { type: GET_SHOW_COMMENTS_REQUEST, payload: { append } },
            {
                type: GET_SHOW_COMMENTS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [CommentsSchema]);
                        return { normalized, headers, id, append, main };
                    });
                }
            },
            GET_SHOW_COMMENTS_ERROR
        ],
        `/shows/${id}/comments/${sort}`,
        'GET',
        'trakt',
        params
    );
};

export const getSeasonComments = (id: number, seasonNumber: number, params?: RequestParams, main: boolean = false) => {
    const sort = params?.sort || 'highest';
    const append = params?.page > 1 || false;
    return apiClient(
        [
            { type: GET_SEASON_COMMENTS_REQUEST, payload: { append } },
            {
                type: GET_SEASON_COMMENTS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [CommentsSchema]);
                        return { normalized, headers, id, append, main, seasonNumber };
                    });
                }
            },
            GET_SEASON_COMMENTS_ERROR
        ],
        `/shows/${id}/seasons/${seasonNumber}/comments/${sort}`,
        'GET',
        'trakt',
        params
    );
};

export const getEpisodeComments = (
    id: number,
    seasonNumber: number,
    episodeNumber: number,
    params?: RequestParams,
    main: boolean = false
) => {
    const sort = params?.sort || 'highest';
    const append = params?.page > 1 || false;
    return apiClient(
        [
            { type: GET_EPISODE_COMMENTS_REQUEST, payload: { append } },
            {
                type: GET_EPISODE_COMMENTS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [CommentsSchema]);
                        return { normalized, headers, id, append, main, seasonNumber, episodeNumber };
                    });
                }
            },
            GET_EPISODE_COMMENTS_ERROR
        ],
        `/shows/${id}/seasons/${seasonNumber}/episodes/${episodeNumber}/comments/${sort}`,
        'GET',
        'trakt',
        params
    );
};

export const postComment = (data: Trakt.CommentForm) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                POST_COMMENT_REQUEST,
                {
                    type: POST_COMMENT_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json, CommentsSchema);
                            return { normalized };
                        });
                    }
                },
                {
                    type: POST_COMMENT_ERROR,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const error = json.errors?.comment?.[0] || null;
                            return { error };
                        });
                    }
                }
            ],
            `/comments`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: data
            }
        )
    );
};

export const updateComment = (id: number, data: Trakt.CommentForm) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UPDATE_COMMENT_REQUEST,
                {
                    type: UPDATE_COMMENT_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json, CommentsSchema);
                            return { normalized };
                        });
                    }
                },
                {
                    type: UPDATE_COMMENT_ERROR,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const error = json.errors?.comment?.[0] || null;
                            return { error };
                        });
                    }
                }
            ],
            `/comments/${id}`,
            'PUT',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: data
            }
        )
    );
};

export const postCommentReply = (id: number, data: Trakt.CommentForm) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                POST_COMMENT_REPLY_REQUEST,
                {
                    type: POST_COMMENT_REPLY_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json, CommentsSchema);
                            return { normalized };
                        });
                    }
                },
                {
                    type: POST_COMMENT_REPLY_ERROR,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            dispatch(getReplies(id));
                            const error = json.errors?.comment?.[0] || null;
                            return { error };
                        });
                    }
                }
            ],
            `/comments/${id}/replies`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: data
            }
        )
    );
};

export const deleteComment = (id: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                DELETE_COMMENT_REQUEST,
                {
                    type: DELETE_COMMENT_SUCCESS,
                    payload: () => {
                        return { id };
                    }
                },
                DELETE_COMMENT_ERROR
            ],
            `/comments/${id}`,
            'DELETE',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};
