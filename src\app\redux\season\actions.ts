import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { Action } from 'redux';
import { TmdbSeasonDetailsSchema } from '../schemas';

export const GET_SEASON_DETAILS_REQUEST = 'GET_SEASON_DETAILS_REQUEST';
export const GET_SEASON_DETAILS_SUCCESS = 'GET_SEASON_DETAILS_SUCCESS';
export const GET_SEASON_DETAILS_ERROR = 'GET_SEASON_DETAILS_ERROR';

export const GET_TRAKT_SEASONS_REQUEST = 'GET_TRAKT_SEASONS_REQUEST';
export const GET_TRAKT_SEASONS_SUCCESS = 'GET_TRAKT_SEASONS_SUCCESS';
export const GET_TRAKT_SEASONS_ERROR = 'GET_TRAKT_SEASONS_ERROR';

export interface SeasonAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<TMDB.SeasonDetails>;
        headers: Api.TraktHeaders;
        id: number;
        season: number;
        seasons: Trakt.BasicSeason[];
    };
}
export const getSeasonDetails = (id: number, season: number) => {
    const params = { append_to_response: 'credits' };
    return apiClient(
        [
            GET_SEASON_DETAILS_REQUEST,
            {
                type: GET_SEASON_DETAILS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, TmdbSeasonDetailsSchema);
                        return { normalized, headers, id, season };
                    });
                }
            },
            GET_SEASON_DETAILS_ERROR
        ],
        `/tv/${id}/season/${season}`,
        'GET',
        'tmdb',
        params
    );
};

export const getTraktSeasons = (id: number) => {
    return apiClient(
        [
            GET_TRAKT_SEASONS_REQUEST,
            {
                type: GET_TRAKT_SEASONS_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        return { seasons: json };
                    });
                }
            },
            GET_TRAKT_SEASONS_ERROR
        ],
        `/shows/${id}/seasons/`,
        'GET',
        'trakt'
    );
};
