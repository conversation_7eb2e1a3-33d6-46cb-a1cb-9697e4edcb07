// Polyfill for ViewPropTypes which was removed from React Native 0.68+
import { ViewPropTypes } from 'deprecated-react-native-prop-types';
import { View } from 'react-native';

// Add ViewPropTypes back to React Native for compatibility with older libraries
if (!View.propTypes) {
  View.propTypes = ViewPropTypes;
}

// Also add it to the react-native module for libraries that import it directly
const ReactNative = require('react-native');
if (!ReactNative.ViewPropTypes) {
  ReactNative.ViewPropTypes = ViewPropTypes;
}
