/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from 'react';
import i18n from 'i18n-js';
import { Header } from 'react-native-elements';
import Back from 'app/app/components/header/Back';

import ClearButton from 'app/app/components/elements/ClearButton';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Container from 'app/app/components/containers/Container';
import { postComment } from 'app/app/redux/comment/actions';
import { useDispatch } from 'react-redux';
import { SeasonAction, getTraktSeasons, GET_TRAKT_SEASONS_SUCCESS } from 'app/app/redux/season/actions';
import ShowHelper from 'app/services/helpers/ShowHelper';
import Form from '../components/Form';

export interface PostSeasonCommentProps {
    id: number;
    error: string;
    seasonNumber: number;
    loading: boolean;
}

const PostSeasonComment = ({ id, loading, error, seasonNumber }: PostSeasonCommentProps) => {
    const dispatch: MS.Dispatch<SeasonAction> = useDispatch();
    const placeholder = i18n.t('COMMENT');
    const [seasonId, setSeasonId] = useState(null);
    const [data, setData] = useState<Trakt.CommentForm>({ comment: '', spoiler: false });
    const onChange = (data: Trakt.CommentForm) => {
        const postData = { ...data };
        postData.season = { ids: { trakt: seasonId } };
        setData(postData);
    };

    useEffect(() => {
        dispatch(getTraktSeasons(id)).then(action => {
            if (action.type === GET_TRAKT_SEASONS_SUCCESS) {
                const { seasons } = action.payload;
                const season =
                    seasons
                        .map(season => {
                            if (season.number === seasonNumber) {
                                return season;
                            }
                            return null;
                        })
                        .filter(v => !!v)?.[0] || null;
                setSeasonId(season?.ids.trakt || null);
            }
        });
    }, []);

    const onSubmit = () => {
        dispatch(postComment(data));
    };
    const rightComponent = <ClearButton small title={i18n.t('POST')} onPress={onSubmit} />;

    return (
        <>
            <Header
                {...{
                    leftComponent: <Back />,
                    rightComponent,
                    centerComponent: { text: i18n.t('POST_COMMENT') }
                }}
            />
            <Container keyboardShouldPersistTaps="handled">
                {seasonId && <Form {...{ onChange, placeholder, error }} />}
                {(loading || !seasonId) && <ScreenLoader />}
            </Container>
        </>
    );
};

export default React.memo(PostSeasonComment);
