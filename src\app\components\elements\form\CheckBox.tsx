import React from 'react';
import { CheckBoxProps, ListItem } from 'react-native-elements';
import styles from './styles';

export interface Props extends CheckBoxProps {
    label: string;
    subtitle: string;
}

const CheckBox = (props: Props) => {
    const { label, subtitle, onPress } = props;
    return (
        <ListItem
            onPress={onPress}
            title={label}
            subtitle={subtitle}
            checkBox={{
                ...props
            }}
            containerStyle={{ ...styles.fieldContainer, ...styles.checkBox }}
        />
    );
};

export default React.memo(CheckBox);
