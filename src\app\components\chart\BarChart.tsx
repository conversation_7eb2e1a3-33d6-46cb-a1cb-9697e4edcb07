import React from 'react';
import { View } from 'react-native';
import i18n from 'i18n-js';
import _ from 'lodash';
import StringHelper from 'app/services/helpers/StringHelper';
import Text from '../elements/Text';
import styles from './styles';
import Bar from './Bar';

export interface BarChartValue {
    label: string;
    value: number;
}
export interface BarChartProps {
    values: BarChartValue[];
    total: number;
}

const BarChart = ({ values, total }: BarChartProps) => {
    return (
        <View>
            <Text style={{ ...styles.title }} textStyle="title" centered>
                {`${StringHelper.formatNumber(total)} ${i18n.t('RATINGS')}`}
            </Text>
            {values.map(value => {
                const perc = ((value.value / total) * 100).toFixed(2);
                return <Bar {...{ value, perc, key: value.label }} />;
            })}
        </View>
    );
};

export default React.memo(<PERSON><PERSON><PERSON>, _.isEqual);
