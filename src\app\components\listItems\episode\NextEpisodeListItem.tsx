import React, { useEffect, useContext } from 'react';
import { ImageURISource, View } from 'react-native';
import { ListItem, ThemeContext, ThemeProps } from 'react-native-elements';
import { MSState } from 'app/app/redux/redux';
import { useSelector, useDispatch } from 'react-redux';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { getEpisodeDetails } from 'app/app/redux/episode/actions';
import { Theme } from 'app/app/styles/themes';
import { EpisodeNavParams } from 'app/views/episode/view';
import Constants from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import _ from 'lodash';
import { getShowDetails } from 'app/app/redux/show/actions';
import DateHelper from 'app/services/helpers/DateHelper';
import useLocale from 'app/app/hooks/useLocale';
import Image from '../../elements/Image';
import { IMAGE_HEIGHT } from '../show/ShowListItem';
import Text from '../../elements/Text';

export interface NextEpisodeListItemProps {
    episode: MS.NextEpisode;
}
const leftElement = (uri: ImageURISource | number, width: number) => (
    <Image {...{ width, uri, height: IMAGE_HEIGHT }} />
);
const NextEpisodeListItem = ({ episode }: NextEpisodeListItemProps) => {
    const locale = useLocale();
    const { airedDate, episode: number, episodeIds, episodeTitle, season, showIds, sortTitle } = episode;
    const { showDetails, show, episodeDetails } = useSelector((state: MSState) => {
        const show = state.entities.show[showIds?.trakt];
        const showDetails = state.entities.showDetails[showIds?.tmdb];
        const episodeDetails = state.entities.episodeDetails[episodeIds.tmdb];
        return { showDetails, show, episodeDetails };
    }, _.isEqual);
    // @ts-ignore
    const { push } = useNavigation();
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const dispatch = useDispatch();
    useEffect(() => {
        if (!showDetails && showIds.tmdb) {
            dispatch(getShowDetails(showIds.tmdb));
        }
        if (!episodeDetails && showIds.tmdb) {
            dispatch(getEpisodeDetails(showIds.tmdb, season, number));
        }
    }, []);

    const uri = ShowHelper.getPosterUri(showDetails?.attributes.poster_path, 20, scheme);
    const width = IMAGE_HEIGHT * 0.66;
    const subtitle = (
        <View>
            <Text textStyle="small" color={colors.discreet}>{`${season}x${number} ${episodeTitle}`}</Text>
            <Text textStyle="small" color={colors.discreet}>
                {DateHelper.format(airedDate, locale)}
            </Text>
        </View>
    );
    const onPress = () => {
        const params: EpisodeNavParams = { ids: show.attributes.ids, seasonNumber: season, episodeNumber: number };
        push(Constants.Navigation.Episode.VIEW, params);
    };

    return (
        <ListItem
            leftElement={leftElement(uri, width)}
            title={sortTitle}
            subtitle={subtitle}
            chevron
            onPress={onPress}
        />
    );
};

export default React.memo(NextEpisodeListItem, _.isEqual);
