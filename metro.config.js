const { getDefaultConfig } = require('@expo/metro-config');

module.exports = (async () => {
    const config = await getDefaultConfig(__dirname);
    
    // Extend the default config instead of replacing it
    const { resolver } = config;
    
    // Add SVG support
    config.transformer = {
        ...config.transformer,
        babelTransformerPath: require.resolve('react-native-svg-transformer')
    };
    
    config.resolver = {
        ...resolver,
        assetExts: resolver.assetExts.filter(ext => ext !== 'svg'),
        sourceExts: [...resolver.sourceExts, 'svg']
    };
    
    return config;
})();

