import React, { useEffect } from 'react';
import Container from 'app/app/components/containers/Container';
import { MSState } from 'app/app/redux/redux';
import { useSelector, useDispatch } from 'react-redux';
import { ListItem, Header } from 'react-native-elements';
import i18n from 'i18n-js';
import DateHelper from 'app/services/helpers/DateHelper';
import useLocale from 'app/app/hooks/useLocale';
import Back from 'app/app/components/header/Back';
import { getUserSettings } from 'app/app/redux/user/actions';
import { useNavigation } from 'react-navigation-hooks';
import Constants from 'app/app/Constants';
import Connections from './components/Connections';

export interface UserProps {}
const User = () => {
    const dispatch = useDispatch();
    const { navigate } = useNavigation();
    const { settings } = useSelector((state: MSState) => {
        return {
            settings: state.user.settings,
            loggedIn: !!state.auth.trakt.token
        };
    });
    const {
        user: { about, age, gender, location, name, private: privacy, vip, vip_ep, images, joined_at },
        account,
        connections
    } = settings;

    useEffect(() => {
        dispatch(getUserSettings());
    }, []);
    const locale = useLocale();
    const userAge = age
        ? i18n.t('[AGE]_YEARS_OLD', {
              age
          })
        : '';
    const userGender = gender ? i18n.t(gender) : '';
    const userLocation = location
        ? i18n.t('FROM_[LOCATION]', {
              location
          })
        : '';
    const subtitle = `${userGender} ${userAge} ${userLocation}`;
    const onStatsPress = () => {
        navigate(Constants.Navigation.Settings.STATS);
    };
    const onUserRatingsPress = () => {
        navigate(Constants.Navigation.Settings.RATINGS);
    };
    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: name }} />
            <Container withPadding={false}>
                <ListItem
                    leftAvatar={{
                        source: { uri: images.avatar.full }
                    }}
                    title={name}
                    subtitle={subtitle}
                />
                <ListItem title={i18n.t('USER_STATS')} chevron onPress={onStatsPress} />
                <ListItem title={i18n.t('USER_RATINGS')} chevron onPress={onUserRatingsPress} />
                {vip && <ListItem title={i18n.t('VIP')} checkmark={vip} />}
                {vip_ep && <ListItem title={i18n.t('VIP_EP')} checkmark={vip_ep} />}
                <ListItem title={i18n.t('PRIVACY')} rightTitle={privacy ? i18n.t('PRIVATE') : i18n.t('PUBLIC')} />
                {!!about && <ListItem title={i18n.t('ABOUT')} subtitle={about} />}
                <ListItem title={i18n.t('TIMEZONE')} rightTitle={account.timezone} />
                <ListItem
                    title={i18n.t('JOINED_TRAKT')}
                    rightTitle={DateHelper.format(joined_at, locale, 'DD MMMM YYYY')}
                />
                <ListItem title={i18n.t('CONNECTIONS')} subtitle={<Connections {...{ connections }} />} />
            </Container>
        </>
    );
};

export default React.memo(User);
