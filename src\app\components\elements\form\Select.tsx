import React, { useState, useContext, useEffect } from 'react';
import { ListItem, Overlay, ThemeContext, ThemeProps } from 'react-native-elements';
import { SPACING } from 'app/app/styles/sizes';
import { Dimensions, View, Platform } from 'react-native';
import { Theme, alpha, common } from 'app/app/styles/themes';
import { ScrollView } from 'react-native-gesture-handler';
import _ from 'lodash';
import i18n from 'i18n-js';
import useKeyboard from 'app/app/hooks/useKeyboard';
import styles from './styles';
import Text from '../Text';
import ClearButton from '../ClearButton';
import TextInput from './TextInput';

const OPTION_HEIGHT = Platform.OS === 'ios' ? 51 : 65;
const { width, height } = Dimensions.get('window');
export interface SelectOption<P> {
    label: string;
    value: P;
}
export interface SelectProps<P> {
    title: string;
    subtitle?: string;
    options: SelectOption<P>[];
    selected: P | P[];
    onSelect: (selected: P | P[]) => void;
    multiple?: boolean;
    maxOptions?: number;
}

const getLabel = <P,>(selected: P[] | P, options: SelectOption<P>[]): string => {
    const option = options.filter(option =>
        _.isArray(selected) ? selected.includes(option.value) : selected === option.value
    );
    return option.map(o => o.label).join();
};

const Select = <P,>({ title, subtitle, options, maxOptions, selected, onSelect, multiple }: SelectProps<P>) => {
    const [isVisible, setIsVisible] = useState(false);
    const [values, setValues] = useState<P | P[]>(selected);
    const [searchTerm, setSearchterm] = useState(null);
    const { height: keyboardHeight } = useKeyboard();
    useEffect(() => {
        setValues(selected);
    }, [selected]);
    const onClose = () => {
        setIsVisible(false);
    };
    const onPress = () => {
        setIsVisible(true);
    };
    const onChange = (selected: P) => {
        if (!multiple && !_.isArray(values)) {
            setValues(selected);
            onSelect(selected);
            setIsVisible(false);
        } else if (multiple && _.isArray(values)) {
            if (values.includes(selected)) {
                const newValues = values.filter(v => v !== selected);
                setValues(newValues);
            } else {
                const newValues = [...values];
                newValues.push(selected);
                setValues(newValues);
            }
        }
    };
    const showTextField = options.length > maxOptions;
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const selectedLabel = getLabel<P>(values, options);
    const overlayHeight = showTextField
        ? height - SPACING.large * 4 - (Platform.OS === 'ios' ? 0 : keyboardHeight / 2)
        : Math.min(OPTION_HEIGHT * options.length + SPACING.large * 2, height - SPACING.large * 4);

    const onMultiselect = () => {
        onSelect(values);
        setIsVisible(false);
    };

    const filtered = showTextField
        ? options.filter(
              option =>
                  (searchTerm && option.label.toLowerCase().startsWith(searchTerm.toLowerCase())) ||
                  !!(values as P[]).includes(option.value)
          )
        : options;
    return (
        <>
            <ListItem
                title={title}
                subtitle={subtitle}
                chevron
                onPress={onPress}
                containerStyle={{ ...styles.fieldContainer, ...styles.chevron, ...styles.select }}
                rightTitle={
                    <Text
                        color={colors.primary}
                        style={{ width: width - 200, ...styles.selectLabel }}
                        numberOfLines={2}
                    >
                        {selectedLabel || i18n.t('ANY')}
                    </Text>
                }
            />
            <Overlay
                {...{
                    animationType: 'slide',
                    onBackdropPress: onClose,
                    isVisible,
                    onClose,
                    backdropStyle: {
                        backgroundColor: alpha(common.black, 0.85)
                    },
                    overlayStyle: {
                        height: overlayHeight + SPACING.large,
                        width,
                        marginTop: height - overlayHeight,
                        borderWidth: 0,
                        backgroundColor: colors.background,
                        borderRadius: 5
                    }
                }}
            >
                <>
                    <View
                        style={{
                            ...styles.row,
                            ...styles.selectTitleContainer,
                            borderTopColor: alpha(colors.primary, 1)
                        }}
                    >
                        <Text style={{ ...styles.selectTitle }} textStyle="doubleTitle" centered>
                            {title}
                        </Text>
                        {multiple && (
                            <ClearButton
                                title={i18n.t('OK')}
                                containerStyle={{ ...styles.multiselectOk }}
                                onPress={onMultiselect}
                            />
                        )}
                    </View>
                    {showTextField && (
                        <TextInput
                            containerStyle={{ ...styles.inputField }}
                            placeholder={title}
                            value={searchTerm}
                            onChangeText={setSearchterm}
                        />
                    )}
                    <ScrollView
                        keyboardShouldPersistTaps="handled"
                        contentContainerStyle={{ paddingBottom: keyboardHeight + SPACING.large }}
                    >
                        {filtered.map(option => {
                            const { label, value } = option;
                            return (
                                <ListItem
                                    key={value.toString()}
                                    title={label}
                                    onPress={() => onChange(value)}
                                    checkBox={{
                                        checked: _.isArray(values) ? values.includes(value) : values === value,
                                        onPress: () => onChange(value)
                                    }}
                                />
                            );
                        })}
                    </ScrollView>
                </>
            </Overlay>
        </>
    );
};
Select.defaultProps = {
    multiple: false,
    maxOptions: 100
};
export default React.memo(Select);
