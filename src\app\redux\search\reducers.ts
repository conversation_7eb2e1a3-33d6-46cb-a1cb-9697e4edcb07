import { combineReducers } from 'redux';
import _ from 'lodash';
import { SIGN_OUT_SUCCESS } from '../auth/actions';

import { RESET_STORE } from '../app/actions';
import { SearchAction, SEARCH_REQUEST, SEARCH_SUCCESS, SEARCH_ERROR, CLEAR_SEARCH_SUCCESS } from './actions';

export interface SearchShowsState {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
}
export const initialSearchShowsState: SearchShowsState = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null
};

export function shows(state: SearchShowsState = initialSearchShowsState, action: SearchAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialSearchShowsState;
        case SEARCH_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case SEARCH_SUCCESS: {
            const { append, headers, normalized } = action.payload;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, normalized.result),
                    headers,
                    loading: false,
                    loadingMore: false
                };
            }
            return {
                ...state,
                result: normalized.result,
                headers,
                loading: false,
                loadingMore: false
            };
        }
        case CLEAR_SEARCH_SUCCESS: {
            return initialSearchShowsState;
        }
        case SEARCH_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

export interface SearchState {
    shows: SearchShowsState;
}

export const initialSearchState: SearchState = {
    shows: initialSearchShowsState
};
export default combineReducers({ shows });
