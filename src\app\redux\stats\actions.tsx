import apiClient from 'app/services/api/client';
import { Action } from 'redux';

export const GET_STATS_REQUEST = 'GET_STATS_REQUEST';
export const GET_STATS_SUCCESS = 'GET_STATS_SUCCESS';
export const GET_STATS_ERROR = 'GET_STATS_ERROR';

export const CLEAR_STATS_SUCCESS = 'CLEAR_STATS_SUCCESS';

export interface StatsAction extends Action {
    payload: {
        stats: Trakt.Stats;
    };
}

export const getShowStats = (id: number) => {
    return apiClient(
        [
            GET_STATS_REQUEST,
            {
                type: GET_STATS_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        return { stats: json };
                    });
                }
            },
            GET_STATS_ERROR
        ],
        `/shows/${id}/stats/`,
        'GET',
        'trakt'
    );
};

export const getSeasonStats = (id: number, season: number) => {
    return apiClient(
        [
            GET_STATS_REQUEST,
            {
                type: GET_STATS_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        return { stats: json };
                    });
                }
            },
            GET_STATS_ERROR
        ],
        `/shows/${id}/seasons/${season}/stats/`,
        'GET',
        'trakt'
    );
};

export const getEpisodeStats = (id: number, season: number, episode: number) => {
    return apiClient(
        [
            GET_STATS_REQUEST,
            {
                type: GET_STATS_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        return { stats: json };
                    });
                }
            },
            GET_STATS_ERROR
        ],
        `/shows/${id}/seasons/${season}/episodes/${episode}/stats/`,
        'GET',
        'trakt'
    );
};

export const clearStats = () => dispatch => {
    return new Promise(resolve => resolve(dispatch({ type: CLEAR_STATS_SUCCESS, payload: {} })));
};
