import { Action } from 'redux';
import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { TmdbImageSchema } from '../schemas';

export const GET_SHOW_IMAGES_REQUEST = 'GET_SHOW_IMAGES_REQUEST';
export const GET_SHOW_IMAGES_SUCCESS = 'GET_SHOW_IMAGES_SUCCESS';
export const GET_SHOW_IMAGES_ERROR = 'GET_SHOW_IMAGES_ERROR';

export interface ImageAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<TMDB.ShowImages>;
    };
}
export const getShowImages = (tmdb: number) => {
    return apiClient(
        [
            GET_SHOW_IMAGES_REQUEST,
            {
                type: GET_SHOW_IMAGES_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, TmdbImageSchema);
                        return { normalized, headers };
                    });
                }
            },
            GET_SHOW_IMAGES_ERROR
        ],
        `/tv/${tmdb}/images`,
        'GET',
        'tmdb'
    );
};
