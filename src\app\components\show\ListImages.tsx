import React, { useEffect, useContext } from 'react';
import { View, StyleSheet } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import getEntities from 'app/app/redux/entities/selectors';
import _ from 'lodash';
import { getShowImages } from 'app/app/redux/image/actions';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import Image from '../elements/Image';

const styles = StyleSheet.create({
    container: { flexDirection: 'row', width: 100, height: 80 }
});
export interface ListImagesProps {
    ids: Trakt.Ids[];
}

const ListImages = ({ ids }: ListImagesProps) => {
    const tmdbIds = ids.map(id => id.tmdb);
    const dispatch = useDispatch();
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const { images }: { images: Api.Entity<TMDB.ShowImages>[] } = useSelector((state: MSState) => {
        const entities = getEntities(state, tmdbIds, 'image') as Api.Entity<TMDB.ShowImages>[];
        const images = entities.filter(entity => entity.attributes.posters[0]?.file_path);
        return { images };
    }, _.isEqual);

    const updateImages = () => {
        if (images.length < 3) {
            const existing = images.map(image => image.id);
            const remaining = _.difference(tmdbIds, existing);
            const need = 3 - existing.length;
            const shuffled = _.shuffle(remaining);
            const fetch = shuffled.slice(0, need);
            fetch.forEach((id: number) => dispatch(getShowImages(id)));
        }
    };
    useEffect(() => {
        updateImages();
    }, []);

    useEffect(() => {
        updateImages();
    }, [ids]);

    const posters = _.shuffle(images)
        .slice(0, 3)
        .map(image => {
            const poster = images ? image.attributes.posters[0]?.file_path || null : null;
            return ShowHelper.getPosterUri(poster, 20, scheme);
        });
    while (posters.length < 3) {
        posters.push(ShowHelper.getPosterUri(null, 20, scheme));
    }
    return (
        <View style={{ ...styles.container }}>
            {posters.map((poster, index) => {
                return (
                    <View
                        {...{
                            key: `${poster}_${index}`,
                            style: {
                                width: 80 * 0.66,
                                height: 80,
                                position: 'absolute',
                                left: index * 30
                            }
                        }}
                    >
                        <Image {...{ uri: poster, width: 80 * 0.66, height: 80 }} />
                    </View>
                );
            })}
        </View>
    );
};

export default React.memo(ListImages, _.isEqual);
