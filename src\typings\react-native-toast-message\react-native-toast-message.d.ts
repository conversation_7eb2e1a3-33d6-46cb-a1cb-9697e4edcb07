import * as React from 'react';

declare module 'react-native-toast-message' {
    export interface ToastProps {
        config?: { [key: string]: () => JSX.Element };
    }
    // eslint-disable-next-line react/prefer-stateless-function
    export default class Toast extends React.Component<ToastProps> {}
    export type ToastType = 'success' | 'error' | 'info' | string;
    export type ToastPosition = 'bottom' | 'top';
    export interface ToastOptions {
        type?: ToastType;
        position?: ToastPosition;
        text1: string;
        text2?: string;
        visibilityTime?: number;
        autoHide?: boolean;
        topOffset?: number;
        bottomOffset?: number;
        onShow?: () => void;
        onHide?: () => void;
    }
    function show(options: ToastOptions): void;
}
