import { StyleSheet } from 'react-native';
import { SPACING } from 'app/app/styles/sizes';

export const LABEL_WIDTH = 1;
export const BAR_WIDTH = 15;
export const VALUE_WIDTH = 3;
export const TOTAL_WIDTH = LABEL_WIDTH + BAR_WIDTH + VALUE_WIDTH;

export default StyleSheet.create({
    title: {
        marginBottom: SPACING.medium
    },
    barContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    label: {
        flex: LABEL_WIDTH,
        textAlign: 'right'
    },
    value: {
        flex: VALUE_WIDTH,
        textAlign: 'right'
    },
    bar: {
        flexDirection: 'row',
        marginHorizontal: SPACING.normal,
        flex: BAR_WIDTH,
        borderRadius: SPACING.small,
        marginVertical: SPACING.small,
        justifyContent: 'space-between',
        borderWidth: 1,
        height: SPACING.medium,
        alignItems: 'center'
    },
    barContent: {
        borderRadius: SPACING.small
    },
    perc: { position: 'absolute', right: 10 }
});
