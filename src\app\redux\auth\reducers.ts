import { combineReducers } from 'redux';
import {
    AuthAction,
    GET_TOKEN_REQUEST,
    GET_TOKEN_SUCCESS,
    GET_TOKEN_ERROR,
    SIGN_OUT_SUCCESS,
    REFRESH_TOKEN_REQUEST,
    REFRESH_TOKEN_SUCCESS,
    REFRESH_TOKEN_ERROR,
    GUEST_SIGN_IN_SUCCESS
} from './actions';

export interface TraktState {
    loading: boolean;
    token: Trakt.Token;
}

export const initialTraktState: TraktState = {
    loading: false,
    token: null
};

export function trakt(state: TraktState = initialTraktState, action: AuthAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
            return initialTraktState;
        case GET_TOKEN_REQUEST:
        case REFRESH_TOKEN_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_TOKEN_SUCCESS: {
            console.log('get token success', action.payload.token);
            return { ...state, token: action.payload.token };
        }
        case REFRESH_TOKEN_SUCCESS: {
            console.log('get refresh token success', action.payload.token);
            return { ...state, token: action.payload.token };
        }
        case GET_TOKEN_ERROR:
        case REFRESH_TOKEN_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface GuestState {
    isGuest: boolean;
}

export const initialGuestState: GuestState = {
    isGuest: false
};

export function guest(state: GuestState = initialGuestState, action: AuthAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
            return initialGuestState;
        case GUEST_SIGN_IN_SUCCESS: {
            return { ...state, isGuest: true };
        }
        default:
            return state;
    }
}

export interface AuthState {
    guest: GuestState;
    trakt: TraktState;
}

export const initialAuthState: AuthState = {
    guest: initialGuestState,
    trakt: initialTraktState
};

export default combineReducers({ guest, trakt });
