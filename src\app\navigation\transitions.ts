import { Easing, Animated } from 'react-native';

const DEFAULT_DURATION = 350;
const SLOW_DURATION = 500;

const SlideFromRight = (index, position, width) => {
    const translateX = position.interpolate({
        inputRange: [index - 1, index, index + 1],
        outputRange: [width, 0, 0]
    });
    const scale = position.interpolate({
        inputRange: [index - 1, index, index + 1],
        outputRange: [0.9, 1, 0.9]
    });
    return { transform: [{ translateX }, { scale }] };
};

const SlideUp = (index, position, height) => {
    const inputRange = [index - 1, index, index + 1];
    const translateY = position.interpolate({
        inputRange,
        outputRange: [height, 0, 0]
    });
    const opacity = position.interpolate({
        inputRange,
        outputRange: [0, 1, 1]
    });

    return { opacity, transform: [{ translateY }] };
};
const SlideDown = (index, position, height) => {
    const inputRange = [index - 1, index, index + 1];
    const translateY = position.interpolate({
        inputRange,
        outputRange: [height, 0, 0]
    });
    const scale = position.interpolate({
        inputRange: [index - 1, index, index + 1],
        outputRange: [0.9, 1, 0.9]
    });
    return { transform: [{ translateY }, { scale }] };
};

const CollapseExpand = (index, position) => {
    const inputRange = [index - 1, index, index + 1];
    const opacity = position.interpolate({
        inputRange,
        outputRange: [0, 1, 1]
    });

    const scaleY = position.interpolate({
        inputRange,
        outputRange: [0, 1, 1]
    });

    return {
        opacity,
        transform: [{ scaleY }]
    };
};

const TransitionConfiguration = (transitionProps, prevTransitionProps) => {
    const { index: trIndex } = transitionProps;
    const prevTrIndex = prevTransitionProps ? prevTransitionProps.index : -1;
    const leaving = trIndex < prevTrIndex;
    const duration = leaving ? SLOW_DURATION : DEFAULT_DURATION;
    return {
        transitionSpec: {
            duration,
            easing: Easing.out(Easing.poly(4)),
            timing: Animated.timing,
            useNativeDriver: true
        },
        screenInterpolator: sceneProps => {
            const { layout, position, scene } = sceneProps;
            const width = layout.initWidth;
            const height = layout.initHeight;
            const { index, route } = scene;
            const params = route.params || {};
            const transition = leaving ? 'slideDown' : params.transition || 'default';
            return {
                collapseExpand: CollapseExpand(index, position),
                slideUp: SlideUp(index, position, height),
                slideDown: SlideDown(index, position, height),
                default: SlideFromRight(index, position, width)
            }[transition];
        }
    };
};

export default TransitionConfiguration;
