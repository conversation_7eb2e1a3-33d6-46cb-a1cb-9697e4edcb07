import { useState, useEffect } from 'react';
import Database from 'app/services/storage/DB';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { MSState } from '../redux/redux';

const useAverageRating = (type: Trakt.RateType, show: number, season: number) => {
    const [average, setAverage] = useState(0);
    const [count, setCount] = useState(0);
    const updated = useSelector<MSState, number>(state => state.meta.dbUpdates.ratings);

    const calculate = () => {
        let query = 'SELECT AVG(rating) as average, COUNT(1) as count FROM ratings WHERE show=? ';
        const values = [show];
        if (type === 'seasons') {
            query += ' AND season =? AND episode !=-1';
            values.push(season);
        } else if (type === 'shows') {
            query += ' AND episode !=-1';
        }
        Database.query(query, values).then(r => {
            if (_.isArray(r) && r.length === 1) {
                setAverage(r[r.length - 1].average);
                setCount(r[r.length - 1].count);
            }
        });
    };

    useEffect(() => {
        calculate();
    }, []);

    useEffect(() => {
        calculate();
    }, [updated]);

    return { average, count };
};
export default useAverageRating;
