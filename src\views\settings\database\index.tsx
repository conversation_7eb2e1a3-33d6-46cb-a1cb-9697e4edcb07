import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Container from 'app/app/components/containers/Container';
import { Header, Button } from 'react-native-elements';
import Back from 'app/app/components/header/Back';
import DB from 'app/services/storage/DB';
import Text from 'app/app/components/elements/Text';

const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    }
});
export interface DatadaseProps {}

const Database = () => {
    const [tables, setTables] = useState([]);
    const [table, setTable] = useState('collection');
    const [result, setResult] = useState([]);
    useEffect(() => {
        DB.query("SELECT name FROM sqlite_master WHERE type ='table' AND name NOT LIKE 'sqlite_%'").then((r: any[]) => {
            const tables = r.map(row => row.name);
            setTables(tables);
        });
    }, []);

    useEffect(() => {
        if (table) {
            DB.query(`SELECT COUNT(1) as total FROM ${table}`).then((r: any[]) => {
                setResult(r);
            });
        }
    }, [table]);

    const headers = result.length > 0 ? Object.keys(result[0]).map(key => key) : [];

    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: 'Database' }} />
            <Container>
                <View style={{ ...styles.row }}>
                    {tables.map(table => {
                        return (
                            <Button
                                key={table}
                                title={table}
                                onPress={() => {
                                    setTable(table);
                                }}
                            />
                        );
                    })}
                </View>
                <Text centered textStyle="title">
                    {result?.length}
                </Text>
                <View style={{ ...styles.row }}>
                    {headers.map(header => (
                        <Text key={header}>{header}</Text>
                    ))}
                </View>
                {result.map((row, i) => {
                    const val = Object.keys(row).map(key => row[key]);
                    return (
                        <View key={i.toString()} style={{ ...styles.row }}>
                            {val.map((v, i) => (
                                <Text key={i.toString()}>{v}</Text>
                            ))}
                        </View>
                    );
                })}
            </Container>
        </>
    );
};

export default React.memo(Database);
