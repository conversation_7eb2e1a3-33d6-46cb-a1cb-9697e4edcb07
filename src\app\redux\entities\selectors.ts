import { createSelector } from 'reselect';
import { StateEntities, EntitiesState } from './reducers';
import { MSState } from '../redux';

const entities = (state: Partial<MSState>, ids: Array<string> | Array<number>, type: string): StateEntities =>
    state.entities ? state.entities[type] : {}; // Get the entities
const ids = (state: MSState, ids: Array<string>): Array<string> => ids; // Get the ids

const getEntities = createSelector([entities, ids], (entities, ids): Array<EntitiesState> | Array<any> => {
    if (!ids || !entities) {
        return [];
    }
    const ent = ids
        .map(key => {
            return entities ? entities[key] : null;
        })
        .filter(item => !!item);
    return ent;
});

export default getEntities;
