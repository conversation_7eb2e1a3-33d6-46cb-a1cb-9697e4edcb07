{"main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "prettier:write": "npx prettier --write **/*.{js,jsx,ts,tsx,json} && npx prettier --write *.{js,jsx,ts,tsx,json}", "lint": "tsc --noEmit && eslint --ext .js,.jsx,.ts,.tsx ./", "build-beta": "expo build:android  --release-channel beta", "build": "expo build:android", "publish": "expo publish", "publish-beta": "expo publish  --release-channel beta"}, "dependencies": {"@expo/react-native-action-sheet": "^3.13.0", "@expo/vector-icons": "^13.0.0", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-community/netinfo": "9.3.0", "@react-native-community/viewpager": "5.0.11", "@react-native-masked-view/masked-view": "0.2.7", "@welldone-software/why-did-you-render": "^4.1.2", "expo": "^46.0.0", "expo-ads-admob": "~13.0.0", "expo-asset": "~8.6.3", "expo-auth-session": "~3.7.4", "expo-blur": "~11.2.0", "expo-constants": "~13.2.4", "expo-dev-client": "~1.3.1", "expo-device": "~4.3.0", "expo-fast-image": "^1.1.3", "expo-file-system": "~14.1.0", "expo-linear-gradient": "~11.4.0", "expo-linking": "~3.2.4", "expo-localization": "~13.1.0", "expo-random": "~12.3.0", "expo-sqlite": "~10.3.0", "expo-updates": "~0.14.7", "i18n-js": "^3.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "node-emoji": "^1.10.0", "normalizr": "^3.6.0", "query-string": "^6.12.1", "react": "18.0.0", "react-dom": "18.0.0", "react-native": "0.69.9", "react-native-elements": "^3.4.3", "react-native-expo-image-cache": "^4.1.0", "react-native-gesture-handler": "~2.5.0", "react-native-reanimated": "~2.9.1", "react-native-redash": "12.1.0", "react-native-safe-area-context": "4.3.1", "react-native-screens": "~3.15.0", "react-native-snap-carousel": "^3.9.1", "react-native-star-rating": "^1.1.0", "react-native-status-bar-height": "^2.5.0", "react-native-svg": "12.3.0", "react-native-tab-view": "2.16.0", "react-native-toast-message": "^1.3.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.18.7", "react-navigation": "^4.0.10", "react-navigation-hooks": "^1.1.0", "react-navigation-stack": "1.9.3", "react-navigation-tabs": "^2.7.0", "react-redux": "^7.1.3", "redux": "^4.0.5", "redux-api-middleware": "^3.1.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "sentry-expo": "~5.0.0", "tinycolor2": "^1.4.1", "use-memo-one": "^1.1.1"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.9.0", "@react-native-community/eslint-config": "^0.0.6", "@react-native-community/eslint-plugin": "^1.0.0", "@types/i18n-js": "^3.0.1", "@types/react": "~18.0.0", "@types/react-native": "~0.69.1", "@types/react-native-snap-carousel": "^3.8.1", "@types/react-redux": "^7.1.5", "@types/redux-api-middleware": "^3.0.7", "@types/redux-logger": "^3.0.7", "@types/tinycolor2": "^1.4.2", "@typescript-eslint/eslint-plugin": "^2.14.0", "@typescript-eslint/parser": "^2.14.0", "babel-plugin-module-resolver": "^4.0.0", "babel-preset-expo": "~9.2.0", "eslint": "^6.8.0", "eslint-config-airbnb-typescript": "^6.3.1", "eslint-config-prettier": "^6.9.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.19.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.17.0", "husky": "^3.1.0", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "react-native-svg-transformer": "^0.14.3", "typescript": "^4.6.3"}, "private": true}