import React, { useContext, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { ThemeContext, ThemeProps, Icon } from 'react-native-elements';
import { SPACING, ICON_SIZE } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import DateHelper from 'app/services/helpers/DateHelper';
import useLocale from 'app/app/hooks/useLocale';
import { Theme, common } from 'app/app/styles/themes';
import { RepliesNavParams } from 'app/views/comment/replies';
import { useNavigation } from 'react-navigation-hooks';
import Constants from 'app/app/Constants';
import { PostCommentNavParams } from 'app/views/comment/post';
import useUser from 'app/app/hooks/useUser';
import { deleteComment } from 'app/app/redux/comment/actions';
import useGuest from 'app/app/hooks/useGuest';
import Toast from 'react-native-toast-message';
import Text from '../../elements/Text';
import MoreText from '../../elements/MoreText';
import Divider from '../../containers/Divider';
import Confirm from '../../containers/Confirm';
import { defaultOptions } from '../../elements/Toast';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: SPACING.medium
    },
    contentContainer: { flexDirection: 'row' },
    content: { flex: 1 },
    textContainer: {
        paddingVertical: SPACING.normal
    },

    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: SPACING.normal
    },
    rowInline: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: SPACING.medium
    },
    icon: {
        marginRight: SPACING.small
    },
    spoiler: {
        ...StyleSheet.absoluteFillObject,
        width: '100%',
        height: '100%',
        backgroundColor: '#111111',
        borderRadius: 5,
        opacity: 0.8
    }
});

export interface CommentListItemProps {
    id: number;
    showBorder: boolean;
    isReply: boolean;
    basic: boolean;
}

const CommentListItem = ({ id, showBorder, isReply, basic }: CommentListItemProps) => {
    const dispatch = useDispatch();
    // @ts-ignore
    const { push } = useNavigation();
    const [showSpoiler, setShowSpoiler] = useState(false);
    const [confirm, setConfirm] = useState(false);
    const [deleting, setDeleting] = useState(false);
    const comment = useSelector((state: MSState) => {
        return state.entities.comment[id];
    });
    const locale = useLocale();
    const { slug } = useUser();
    const isGuest = useGuest();
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    if (!comment) {
        return null;
    }
    const {
        comment: text,
        user,
        user_rating,
        created_at,
        updated_at,
        likes,
        replies,
        review,
        spoiler
    } = comment.attributes;
    const name = !user.private ? user.name || user.username : i18n.t('ANONYMOUS_USER');
    const date = updated_at ? DateHelper.format(updated_at, locale) : DateHelper.format(created_at, locale);
    const spoilerText = showSpoiler ? i18n.t('HIDE_SPOILER') : i18n.t('SHOW_SPOILER');
    const onSpoilerPress = () => {
        setShowSpoiler(!showSpoiler);
    };
    const onRepliesPress = () => {
        const params: RepliesNavParams = { id };
        push(Constants.Navigation.Comment.REPLIES, params);
    };
    const onAddReply = () => {
        if (!isGuest) {
            const params: PostCommentNavParams = { id, type: 'comment', replyTo: comment.attributes };
            push(Constants.Navigation.Comment.POST, params);
        } else {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('POST_COMMENT_WARNING')
            });
        }
    };
    const onEdit = () => {
        const params: PostCommentNavParams = { id, type: 'comment', action: 'update' };
        push(Constants.Navigation.Comment.POST, params);
    };
    const onDelete = () => {
        setConfirm(false);
        setDeleting(true);
        dispatch(deleteComment(id));
    };
    const isOwner = !isGuest && comment.attributes.user.ids.slug === slug;
    const rating = user_rating ? `${user_rating}/10` : '';
    const paddingLeft = isReply ? SPACING.normal : SPACING.medium;
    const spoiledOpacity = spoiler && !showSpoiler ? 0.1 : 1;
    const canDelete =
        isOwner && replies === 0 && !DateHelper.isOlder(comment.attributes.created_at, { amount: 2, unit: 'weeks' });

    if (deleting) {
        return null;
    }
    return (
        <>
            <View style={{ ...styles.container, paddingLeft }}>
                <View style={{ ...styles.contentContainer }}>
                    {isReply && (
                        <Icon
                            {...{
                                name: 'reply',
                                size: ICON_SIZE.large,
                                iconStyle: { transform: [{ rotate: '180deg' }] },
                                containerStyle: {
                                    paddingHorizontal: SPACING.normal,
                                    paddingTop: SPACING.normal
                                }
                            }}
                        />
                    )}
                    <View style={{ ...styles.content }}>
                        <View style={{ ...styles.row }}>
                            <Text bold>{`${name} ${rating}`}</Text>
                            {spoiler && !basic && <Text onPress={onSpoilerPress}>{spoilerText}</Text>}
                            {review && !basic && (
                                <View style={{ ...styles.row }}>
                                    <Icon {...{ name: 'star', size: ICON_SIZE.small, color: common.yellow }} />
                                    <Text>{i18n.t('REVIEW')}</Text>
                                </View>
                            )}
                        </View>
                        <View style={{ ...styles.textContainer }}>
                            <View style={{ opacity: spoiledOpacity }}>
                                {text.length > 500 ? (
                                    <MoreText {...{ initialHeight: 200 }}>
                                        <Text hasEmoji>{text}</Text>
                                    </MoreText>
                                ) : (
                                    <Text hasEmoji>{text}</Text>
                                )}
                            </View>
                            {spoiler && !showSpoiler && !basic && (
                                <View
                                    style={{
                                        ...styles.spoiler
                                    }}
                                />
                            )}
                        </View>
                        {!basic && (
                            <View style={{ ...styles.row }}>
                                <View style={{ ...styles.rowInline }}>
                                    <View style={{ ...styles.rowInline }}>
                                        <Icon
                                            {...{
                                                type: 'antdesign',
                                                name: 'like2',
                                                size: ICON_SIZE.small,
                                                containerStyle: styles.icon
                                            }}
                                        />
                                        <Text textStyle="large">{likes}</Text>
                                    </View>
                                    {replies > 0 && (
                                        <View style={{ ...styles.rowInline }}>
                                            <Icon
                                                {...{
                                                    type: 'feather',
                                                    name: 'message-square',
                                                    size: ICON_SIZE.small,
                                                    containerStyle: styles.icon,
                                                    onPress: onRepliesPress
                                                }}
                                            />
                                            <Text textStyle="large">{replies}</Text>
                                        </View>
                                    )}
                                    <View style={{ ...styles.rowInline }}>
                                        <Icon
                                            {...{
                                                name: 'reply',
                                                size: ICON_SIZE.small,
                                                containerStyle: styles.icon,
                                                onPress: onAddReply
                                            }}
                                        />
                                    </View>
                                    {isOwner && (
                                        <View style={{ ...styles.rowInline }}>
                                            <Icon
                                                {...{
                                                    name: 'edit',
                                                    size: ICON_SIZE.small,
                                                    containerStyle: styles.icon,
                                                    onPress: onEdit
                                                }}
                                            />
                                        </View>
                                    )}
                                    {canDelete && (
                                        <View style={{ ...styles.rowInline }}>
                                            <Icon
                                                {...{
                                                    name: 'close',
                                                    size: ICON_SIZE.small,
                                                    containerStyle: styles.icon,
                                                    onPress: () => setConfirm(true)
                                                }}
                                            />
                                        </View>
                                    )}
                                </View>
                                <Text color={colors.discreet} textStyle="small">
                                    {date}
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
            </View>
            <Divider {...{ size: SPACING.medium, border: showBorder, color: colors.divider }} />
            <Confirm
                {...{
                    confirmation: i18n.t('DELETE_COMMENT_CONFIRM'),
                    title: i18n.t('DELETE_COMMENT'),
                    onCancel: () => setConfirm(false),
                    isVisible: confirm,
                    onConfirm: onDelete
                }}
            />
        </>
    );
};

CommentListItem.defaultProps = {
    showBorder: false,
    isReply: false,
    basic: false
};

export default React.memo(CommentListItem);
