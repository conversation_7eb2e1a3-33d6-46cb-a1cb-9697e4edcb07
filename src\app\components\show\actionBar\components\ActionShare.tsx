import React from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Icon } from 'react-native-elements';
import { common } from 'app/app/styles/themes';
import { ICON_SIZE } from 'app/app/styles/sizes';
import styles from '../styles';

export interface ActionShareProps {
    isImdbAvail: boolean;
    onSharePress: () => void;
}

const ActionShare = ({ isImdbAvail, onSharePress }: ActionShareProps) => {
    return (
        <Icon
            Component={TouchableOpacity}
            name="share"
            color={common.green}
            containerStyle={{ ...styles.icon }}
            size={ICON_SIZE.medium}
            disabled={!isImdbAvail}
            disabledStyle={{ ...styles.disabled }}
            onPress={onSharePress}
        />
    );
};

export default React.memo(ActionShare);
