import * as SQLite from 'expo-sqlite';
import StringHelper from '../helpers/StringHelper';

export interface Field {
    name: string;
    type: string;
    extra: string;
}
export interface TableConfig {
    fields: Field[];
    extra: string;
}

export const FavoritesTableConfig: TableConfig = {
    fields: [
        {
            name: 'trakt',
            type: 'integer',
            extra: 'PRIMARY KEY NOT NULL'
        },
        {
            name: 'ids',
            type: 'string',
            extra: 'NOT NULL'
        },
        {
            name: 'poster',
            type: 'string',
            extra: 'DEFAULT NULL'
        },
        {
            name: 'title',
            type: 'string',
            extra: 'NOT NULL'
        },
        {
            name: 'title_sort',
            type: 'string',
            extra: 'NOT NULL'
        },
        {
            name: 'status',
            type: 'string',
            extra: 'DEFAULT NULL'
        },
        {
            name: 'year',
            type: 'integer',
            extra: 'NOT NULL'
        },
        {
            name: 'network',
            type: 'string',
            extra: 'DEFAULT NULL'
        },
        {
            name: 'created',
            type: 'integer',
            extra: 'NOT NULL DEFAULT 0'
        },
        {
            name: 'updated',
            type: 'integer',
            extra: 'NOT NULL DEFAULT 0'
        }
    ],
    extra: ''
};
export const RatingsTableConfig = {
    fields: [
        {
            name: 'type',
            type: 'string',
            extra: 'NOT NULL'
        },
        {
            name: 'show',
            type: 'integer',
            extra: 'NOT NULL'
        },
        {
            name: 'season',
            type: 'integer',
            extra: 'DEFAULT -1'
        },
        {
            name: 'episode',
            type: 'integer',
            extra: 'DEFAULT -1'
        },
        {
            name: 'rating',
            type: 'integer',
            extra: 'NOT NULL'
        }
    ],
    extra: ' PRIMARY KEY (show, season,episode)'
};

export const WatchedTableConfig = {
    fields: [
        {
            name: 'show',
            type: 'integer',
            extra: 'NOT NULL'
        },
        {
            name: 'season',
            type: 'integer',
            extra: 'DEFAULT -1'
        },
        {
            name: 'episode',
            type: 'integer',
            extra: 'DEFAULT -1'
        },
        {
            name: 'percent',
            type: 'integer',
            extra: 'NOT NULL DEFAULT 0'
        }
    ],
    extra: ' PRIMARY KEY (show, season,episode)'
};

export const CollectionTableConfig = {
    fields: [
        {
            name: 'show',
            type: 'integer',
            extra: 'NOT NULL'
        },
        {
            name: 'season',
            type: 'integer',
            extra: 'DEFAULT -1'
        },
        {
            name: 'episode',
            type: 'integer',
            extra: 'DEFAULT -1'
        }
    ],
    extra: ' PRIMARY KEY (show, season,episode)'
};

export default class Database {
    private static db: SQLite.WebSQLDatabase;

    private static getDb() {
        if (!Database.db) {
            Database.db = SQLite.openDatabase('myseries');
        }
        return Database.db;
    }

    static async createTables(drop: boolean) {
        return Promise.all([
            Database.createTable('favorites', FavoritesTableConfig, drop),
            Database.createTable('ratings', RatingsTableConfig, drop),
            Database.createTable('watched', WatchedTableConfig, drop),
            Database.createTable('collection', CollectionTableConfig, drop)
        ]);
    }

    static async createTable(
        tableName: string,
        config: TableConfig,
        drop: boolean = false
    ): Promise<SQLite.SQLResultSet> {
        if (drop) {
            await Database.query(`DROP TABLE IF EXISTS ${tableName}`);
        }
        const fields = config.fields.map(field => {
            const { name, type, extra } = field;
            return `${name} ${type} ${extra}`;
        });
        const extra = config.extra ? `, ${config.extra}` : ``;
        const query = `CREATE TABLE IF NOT EXISTS ${tableName} ( ${fields.join(', ')} ${extra})`;
        return Database.query(query) as Promise<SQLite.SQLResultSet>;
    }

    static query(query: string, params?: Array<any>): Promise<any[] | SQLite.SQLResultSet> {
        const db = Database.getDb();
        return new Promise((resolve, reject) => {
            db.transaction(tx => {
                tx.executeSql(
                    query,
                    params,
                    (tx, success) => {
                        // console.info({ query, params, success });
                        if (success.rowsAffected === 0 && query.startsWith('SELECT')) {
                            const result: any[] = [];
                            for (let index = 0; index < success.rows.length; index += 1) {
                                const row = success.rows.item(index);
                                const set = {};
                                Object.keys(row).forEach(key => {
                                    const value = row[key];
                                    if (StringHelper.isJSON(value)) {
                                        set[key] = JSON.parse(value);
                                    } else {
                                        set[key] = value;
                                    }
                                });
                                result.push(set);
                            }
                            resolve(result);
                        } else {
                            resolve(success);
                        }
                    },
                    (tx, error) => {
                        reject(error);
                        return false;
                    }
                );
            });
        });
    }
}
