import React, { useState, useContext, useEffect } from 'react';
import { withNavigation } from 'react-navigation';
import Container from 'app/app/components/containers/Container';
import { SearchBar, Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { getStatusBarHeight } from 'react-native-status-bar-height';
import { Theme } from 'styles/themes';
import { useDispatch, useSelector } from 'react-redux';
import { searchShows, clearSearch } from 'app/app/redux/search/actions';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import { MSState } from 'app/app/redux/redux';
import _ from 'lodash';
import { View, StyleSheet, Platform } from 'react-native';
import { SPACING, ICON_SIZE } from 'app/app/styles/sizes';
import { TouchableOpacity } from 'react-native-gesture-handler';
import StringHelper from 'app/services/helpers/StringHelper';
import Banner from 'app/app/components/containers/Banner';
import Help from 'app/app/components/containers/Help';
import swipeShowLight from 'app/assets/images/help/swipe_show_light.png';
import swipeShowDark from 'app/assets/images/help/swipe_show_dark.png';
import i18n from 'i18n-js';
import HelpHelper from 'app/services/helpers/HelpHelper';
import SearchResults from '../components/SearchResults';
import NoSearch from '../components/NoSearch';
import Filters from '../filters/Filters';

const styles = StyleSheet.create({
    searchBarContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'baseline'
    },
    searchBar: {
        flex: 1,
        paddingBottom: 0
    }
});
const Search = () => {
    const paddingTop = getStatusBarHeight();
    const [value, setValue] = useState('');
    const [filters, setFilters] = useState<RequestParams>({});
    const [hasSearch, setHasSearch] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const [searchParams, setSearchParams] = useState(null);
    const [helpVisible, setHelpVisible] = useState(false);
    const dispatch = useDispatch();
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    const { loading, hasData, searching } = useSelector((state: MSState) => {
        return {
            loading:
                state.search.shows.loading ||
                state.shows.trending.loading ||
                state.shows.popular.loading ||
                state.shows.watched.loading,
            hasData:
                state.shows.recommended.result.length ||
                state.shows.popular.result.length ||
                state.shows.trending.result.length ||
                state.shows.watched.result.length,
            searching: state.search.shows.loading
        };
    }, _.isEqual);

    useEffect(() => {
        HelpHelper.getHelp('swipeList').then(value => {
            setHelpVisible(value);
        });
    }, []);

    const onChangeText = (text: string) => {
        setValue(text);
    };

    const search = (searchParams: RequestParams) => {
        const params: RequestParams = { query: value, ...filters, ...searchParams, extended: 'full' };
        dispatch(clearSearch());
        setSearchParams(params);
        dispatch(searchShows(params));
        setHasSearch(true);
    };

    const onEndEditing = () => {
        const query = StringHelper.sanitizeSearch(value);
        search({ query });
    };

    const clearFilters = () => {
        setFilters({});
    };

    const onClear = () => {
        setHasSearch(false);
        clearFilters();
        dispatch(clearSearch());
    };

    const onApply = (params: RequestParams) => {
        setFilters(params);
        setShowFilters(false);
        setHasSearch(true);
        search(params);
    };

    const onCancel = () => {
        setShowFilters(false);
    };

    return (
        <>
            {!showFilters && (
                <View
                    style={{
                        ...styles.searchBarContainer,
                        paddingTop
                    }}
                >
                    <SearchBar
                        {...{
                            onChangeText,
                            onClear,
                            value,
                            onEndEditing,
                            returnKeyType: 'search',
                            keyboardAppearance: scheme as 'light' | 'dark' | 'default',
                            selectTextOnFocus: true,
                            rightIconContainerStyle: { paddingHorizontal: SPACING.normal },
                            clearIcon: { size: ICON_SIZE.medium }
                        }}
                        containerStyle={{ ...styles.searchBar }}
                    />

                    {hasSearch && !value && (
                        <Icon
                            {...{
                                Component: TouchableOpacity,
                                onPress: onClear,
                                name: 'close',
                                size: ICON_SIZE.medium,
                                containerStyle: {
                                    paddingRight: SPACING.medium
                                }
                            }}
                        />
                    )}
                    <Icon
                        {...{
                            Component: TouchableOpacity,
                            onPress: () => setShowFilters(!showFilters),
                            onLongPress: clearFilters,
                            name: 'filter',
                            type: 'font-awesome',
                            size: ICON_SIZE.medium,
                            color: !_.isEmpty(filters) ? colors.primary : colors.discreet,
                            iconStyle: { paddingBottom: Platform.OS === 'ios' ? SPACING.small : 0 },
                            containerStyle: {
                                paddingRight: SPACING.medium
                            }
                        }}
                    />
                </View>
            )}
            <Container useScrollView={!hasSearch} withPadding={false}>
                {!showFilters && (
                    <>
                        <SearchResults {...{ hasSearch, searchParams }} />
                        <NoSearch {...{ hasSearch }} />
                    </>
                )}
                {showFilters && <Filters {...{ onCancel, onApply, filters }} />}
                {loading && !hasData && <ScreenLoader />}
                {searching && <ScreenLoader />}
            </Container>
            <Banner />
            <Help
                {...{
                    title: i18n.t('SWIPE_SHOW_HELP_TITLE'),
                    isVisible: helpVisible,
                    image: scheme === 'dark' ? swipeShowDark : swipeShowLight,
                    imageRatio: 115 / 413,
                    text: i18n.t('SWIPE_SHOW_HELP')
                }}
            />
        </>
    );
};

export default withNavigation(React.memo(Search, _.isEqual));
