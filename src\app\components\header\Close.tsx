import React from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import HeaderIcon from './HeaderIcon';

export interface CloseProps {
    onPress?: () => void;
}

type Props = CloseProps & NavigationInjectedProps;
const Close = ({ navigation, onPress }: Props) => {
    return (
        <HeaderIcon
            name="x"
            onPress={() => {
                if (onPress) {
                    onPress();
                } else {
                    navigation.goBack(null);
                }
            }}
        />
    );
};

export default withNavigation(Close);
