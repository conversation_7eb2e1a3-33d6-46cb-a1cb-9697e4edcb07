import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import useAverageRating from 'app/app/hooks/useAverageRating';
import { Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        paddingVertical: SPACING.normal
    },
    icon: { marginHorizontal: SPACING.normal },
    rate: { paddingHorizontal: SPACING.normal }
});

export interface AverageRatingProps {
    type: Trakt.RateType;
    show: number;
    season?: number;
    centeredIcon?: boolean;
}

const AverageRating = ({ type, show, season, centeredIcon }: AverageRatingProps) => {
    const { average, count } = useAverageRating(type, show, season);
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const iconStyle = centeredIcon ? { marginHorizontal: SPACING.normal } : { marginRight: SPACING.medium };

    return (
        !!average && (
            <View style={{ ...styles.container }}>
                <Icon
                    {...{
                        type: 'font-awesome',
                        name: 'heart',
                        color: colors.primary,
                        containerStyle: iconStyle
                    }}
                />
                <>
                    <Text style={{ ...styles.rate }} bold textStyle="title" color={colors.primary}>
                        {average.toFixed(2)}
                    </Text>
                    <Text textStyle="nano" color={colors.discreet}>
                        {i18n.t('EPISODES_[NUMBER]_RATINGS', { number: count })}
                    </Text>
                </>
            </View>
        )
    );
};

export default React.memo(AverageRating);
