import React, { useEffect, useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import { useNavigation } from 'react-navigation-hooks';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { getShowDetails } from 'app/app/redux/show/actions';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { EpisodeNavParams } from 'app/views/episode/view';
import Constants from 'app/app/Constants';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { ICON_SIZE, FONT_SIZE, SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import ClearButton from '../elements/ClearButton';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: SPACING.normal
    }
});

export interface EpisodeNavigationProps {
    ids: Trakt.Ids;
    season: number;
    episode: number;
}

const EpisodeNavigation = ({ ids, season, episode }: EpisodeNavigationProps) => {
    // @ts-ignore
    const { replace } = useNavigation();
    const dispatch = useDispatch();
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const showDetails = useSelector((state: MSState) => {
        return state.entities.showDetails[ids.tmdb];
    });

    useEffect(() => {
        if (!showDetails && ids.tmdb) {
            dispatch(getShowDetails(ids.tmdb));
        }
    }, []);

    const nextEpisode = ShowHelper.getNextEpisode(showDetails, season, episode);
    const prevEpisode = ShowHelper.getPreviousEpisode(showDetails, season, episode);

    const goToNext = () => {
        const { episode, season } = nextEpisode;
        const params: EpisodeNavParams = { ids, seasonNumber: season, episodeNumber: episode };
        replace(Constants.Navigation.Episode.VIEW, params);
    };
    const goToPrevious = () => {
        const { episode, season } = prevEpisode;
        const params: EpisodeNavParams = { ids, seasonNumber: season, episodeNumber: episode };
        replace(Constants.Navigation.Episode.VIEW, params);
    };
    const nextTitle = nextEpisode ? `${i18n.t('EPISODE')} ${nextEpisode.season}x${nextEpisode.episode}` : null;
    const prevTitle = prevEpisode ? `${i18n.t('EPISODE')} ${prevEpisode.season}x${prevEpisode.episode}` : null;
    return (
        <View style={{ ...styles.container }}>
            <ClearButton
                icon={
                    prevEpisode
                        ? {
                              name: 'chevron-left',
                              color: colors.primary,
                              size: ICON_SIZE.small
                          }
                        : null
                }
                onPress={goToPrevious}
                title={prevTitle}
                titleStyle={{ fontSize: FONT_SIZE.larger }}
            />
            <ClearButton
                iconRight
                icon={nextEpisode ? { name: 'chevron-right', color: colors.primary, size: ICON_SIZE.small } : null}
                onPress={goToNext}
                titleStyle={{ fontSize: FONT_SIZE.larger }}
                title={nextTitle}
            />
        </View>
    );
};

export default React.memo(EpisodeNavigation);
