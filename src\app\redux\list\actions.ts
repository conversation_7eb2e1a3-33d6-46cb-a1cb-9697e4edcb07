import { Action } from 'redux';
import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { MSState } from '../redux';
import { TraktListsSchema, TraktShowSchema } from '../schemas';
import { updateLastActivity } from '../sync/actions';
import { UNATHORIZED } from '../app/actions';

export const GET_LISTS_REQUEST = 'GET_LISTS_REQUEST';
export const GET_LISTS_SUCCESS = 'GET_LISTS_SUCCESS';
export const GET_LISTS_ERROR = 'GET_LISTS_ERROR';

export const GET_LIST_ITEMS_REQUEST = 'GET_LIST_ITEMS_REQUEST';
export const GET_LIST_ITEMS_SUCCESS = 'GET_LIST_ITEMS_SUCCESS';
export const GET_LIST_ITEMS_ERROR = 'GET_LIST_ITEMS_ERROR';

export const CREATE_LIST_REQUEST = 'CREATE_LIST_REQUEST';
export const CREATE_LIST_SUCCESS = 'CREATE_LIST_SUCCESS';
export const CREATE_LIST_ERROR = 'CREATE_LIST_ERROR';

export const UPDATE_LIST_REQUEST = 'UPDATE_LIST_REQUEST';
export const UPDATE_LIST_SUCCESS = 'UPDATE_LIST_SUCCESS';
export const UPDATE_LIST_ERROR = 'UPDATE_LIST_ERROR';

export const DELETE_LIST_REQUEST = 'DELETE_LIST_REQUEST';
export const DELETE_LIST_SUCCESS = 'DELETE_LIST_SUCCESS';
export const DELETE_LIST_ERROR = 'DELETE_LIST_ERROR';

export const ADD_TO_LIST_REQUEST = 'ADD_TO_LIST_REQUEST';
export const ADD_TO_LIST_SUCCESS = 'ADD_TO_LIST_SUCCESS';
export const ADD_TO_LIST_ERROR = 'ADD_TO_LIST_ERROR';

export const REMOVE_FROM_LIST_REQUEST = 'REMOVE_FROM_LIST_REQUEST';
export const REMOVE_FROM_LIST_SUCCESS = 'REMOVE_FROM_LIST_SUCCESS';
export const REMOVE_FROM_LIST_ERROR = 'REMOVE_FROM_LIST_ERROR';

export interface ListsAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<Trakt.CustomList>;
        id: number;
        fetched: number;
        refreshing: boolean;
        headers: Api.TraktHeaders;
    };
}

export const getLists = (refreshing: boolean = false) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                { type: GET_LISTS_REQUEST, payload: { refreshing } },
                {
                    type: GET_LISTS_SUCCESS,
                    payload: (action, state, res) => {
                        const { headers }: { headers: Headers } = res;
                        return res.json().then(json => {
                            const normalized = normalize(json, [TraktListsSchema]);
                            return { normalized, headers, fetched: new Date().getTime() };
                        });
                    }
                },
                GET_LISTS_ERROR
            ],
            `/users/${slug}/lists`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const getListItems = (id: number, params: RequestParams, refreshing: boolean = false) => (
    dispatch,
    getState
) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                { type: GET_LIST_ITEMS_REQUEST, payload: { refreshing } },
                {
                    type: GET_LIST_ITEMS_SUCCESS,
                    payload: (action, state, res) => {
                        const { headers }: { headers: Headers } = res;
                        return res.json().then(json => {
                            const normalized = normalize(json, [TraktShowSchema]);
                            return { normalized, headers, id, fetched: new Date().getTime() };
                        });
                    }
                },
                GET_LIST_ITEMS_ERROR
            ],
            `/users/${slug}/lists/${id}/items/show`,
            'GET',
            'trakt',
            params,
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const createList = (data: Trakt.CustomListForm) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                CREATE_LIST_REQUEST,
                {
                    type: CREATE_LIST_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            dispatch(getLists());
                            dispatch(updateLastActivity());
                            const normalized = normalize(json, TraktListsSchema);
                            return { normalized };
                        });
                    }
                },
                CREATE_LIST_ERROR
            ],
            `/users/${slug}/lists`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: data
            }
        )
    );
};

export const updateList = (id: number, data: Trakt.CustomListForm) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                UPDATE_LIST_REQUEST,
                {
                    type: UPDATE_LIST_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            dispatch(getLists());
                            const params: RequestParams = {
                                extended: 'full'
                            };
                            dispatch(updateLastActivity());
                            dispatch(getListItems(id, params));
                            const normalized = normalize(json, TraktListsSchema);
                            return { normalized };
                        });
                    }
                },
                UPDATE_LIST_ERROR
            ],
            `/users/${slug}/lists/${id}`,
            'PUT',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: data
            }
        )
    );
};

export const deleteList = (id: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                DELETE_LIST_REQUEST,
                {
                    type: DELETE_LIST_SUCCESS,
                    payload: () => {
                        dispatch(getLists());
                        dispatch(updateLastActivity());
                        return {};
                    }
                },
                DELETE_LIST_ERROR
            ],
            `/users/${slug}/lists/${id}`,
            'DELETE',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const addToList = (listId: number, show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                ADD_TO_LIST_REQUEST,
                {
                    type: ADD_TO_LIST_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            const params: RequestParams = {
                                extended: 'full'
                            };
                            dispatch(getListItems(listId, params));
                            dispatch(updateLastActivity());
                            return {};
                        });
                    }
                },
                ADD_TO_LIST_ERROR
            ],
            `/users/${slug}/lists/${listId}/items`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: { shows: [{ ids: { trakt: show } }] }
            }
        )
    );
};

export const removeFromList = (listId: number, show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const { slug } = state.user.settings.user.ids;
    return dispatch(
        apiClient(
            [
                REMOVE_FROM_LIST_REQUEST,
                {
                    type: REMOVE_FROM_LIST_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            const params: RequestParams = {
                                extended: 'full'
                            };
                            dispatch(getListItems(listId, params));
                            dispatch(updateLastActivity());
                            return {};
                        });
                    }
                },
                REMOVE_FROM_LIST_ERROR
            ],
            `/users/${slug}/lists/${listId}/items/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: { shows: [{ ids: { trakt: show } }] }
            }
        )
    );
};
