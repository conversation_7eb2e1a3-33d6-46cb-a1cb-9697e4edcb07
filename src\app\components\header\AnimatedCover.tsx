import React, { useContext } from 'react';
import { StyleSheet, Dimensions, ImageURISource, View } from 'react-native';
import Animated from 'react-native-reanimated';
import Back from 'app/app/components/header/Back';
import Header from 'app/app/components/header/Header';
import { HEADER_HEIGHT, Theme } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import Image from '../elements/Image';
import Text from '../elements/Text';

const SCROLL_RATIO = 1;
const styles = StyleSheet.create({
    container: {
        zIndex: 2,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0
    },
    background: { position: 'absolute' },
    headerContainer: {
        zIndex: 10,
        backgroundColor: 'transparent',
        borderBottomWidth: 0
    },
    header: {
        backgroundColor: 'transparent',
        borderBottomWidth: 0
    },
    leftContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    coverIcon: { marginLeft: SPACING.normal },
    gradient: { zIndex: 3, ...StyleSheet.absoluteFillObject }
});
const { width } = Dimensions.get('window');
export const COVER_HEIGHT = (width * 231) / 260; // 411;
export interface AnimatedCoverProps {
    y: Animated.Value<number>;
    src: ImageURISource | number;
    title: string;
    height: number;
    backgroundImage?: boolean;
    watching?: JSX.Element;
    bar?: JSX.Element;
}

const AnimatedCover = ({ y, src, title, height, backgroundImage, bar, watching }: AnimatedCoverProps) => {
    const translateY = Animated.interpolateNode(y, {
        inputRange: [0, height * SCROLL_RATIO],
        outputRange: [0, -height + HEADER_HEIGHT],
        extrapolate: Animated.Extrapolate.CLAMP
    });
    const scale = Animated.interpolateNode(y, {
        inputRange: [-200, 0],
        outputRange: [1.5, 1],
        extrapolate: Animated.Extrapolate.CLAMP
    });
    const opacity = Animated.interpolateNode(y, {
        inputRange: [0, height * SCROLL_RATIO],
        outputRange: [1, 0.1],
        extrapolate: Animated.Extrapolate.CLAMP
    });

    const headerOpacity = Animated.interpolateNode(y, {
        inputRange: [0, (height * SCROLL_RATIO) / 2, height * SCROLL_RATIO],
        outputRange: [0, 0.2, 1],
        extrapolate: Animated.Extrapolate.CLAMP
    });
    const scaleY = Animated.interpolateNode(y, {
        inputRange: [0, 50],
        outputRange: [1, 0],
        extrapolate: Animated.Extrapolate.CLAMP
    });
    const barOpacity = Animated.interpolateNode(y, {
        inputRange: [0, 50],
        outputRange: [1, 0],
        extrapolate: Animated.Extrapolate.CLAMP
    });
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const gradient =
        scheme === 'dark'
            ? ['#000000AA', '#00000055', 'transparent', 'transparent']
            : ['#FFFFFFFF', '#FFFFFF55', 'transparent', 'transparent'];
    return (
        <>
            <Animated.View
                // @ts-ignore
                style={{
                    ...styles.container,
                    transform: [{ translateY }, { scale }],
                    opacity,
                    width,
                    height
                }}
            >
                <LinearGradient
                    pointerEvents="none"
                    colors={gradient}
                    locations={[0, 0.1, 0.4, 1]}
                    style={styles.gradient}
                />
                {backgroundImage && (
                    <View style={styles.background}>
                        <Image
                            {...{
                                uri: src,
                                border: false,
                                width,
                                height,
                                cache: false,
                                resizeMode: 'cover'
                            }}
                        />
                        <BlurView tint="dark" intensity={75} style={{ ...StyleSheet.absoluteFillObject }} />
                    </View>
                )}
                <Image
                    {...{
                        uri: src,
                        border: false,
                        width,
                        height,
                        cache: false,
                        backgroundColor: 'transparent',
                        resizeMode: backgroundImage ? 'contain' : 'cover'
                    }}
                />

                <Animated.View
                    // @ts-ignore
                    style={{ opacity: barOpacity, transform: [{ scaleY }] }}
                >
                    {bar}
                </Animated.View>
            </Animated.View>

            <Header
                containerStyle={{ ...styles.headerContainer }}
                leftComponent={
                    <View style={styles.leftContainer}>
                        <Back />
                        <Animated.View style={{ ...styles.coverIcon, opacity: headerOpacity }}>
                            <Image
                                {...{
                                    uri: src,
                                    width: 32,
                                    height: 32,
                                    rounded: true
                                }}
                            />
                        </Animated.View>
                    </View>
                }
                centerComponent={
                    <Animated.View style={{ opacity: headerOpacity }}>
                        <Text bold textStyle="larger">
                            {title}
                        </Text>
                    </Animated.View>
                }
                rightComponent={watching}
            />
        </>
    );
};

AnimatedCover.defaultProps = {
    height: COVER_HEIGHT,
    backgroundImage: false
};

export default React.memo(AnimatedCover);
