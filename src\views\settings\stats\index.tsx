import React, { useEffect, useContext } from 'react';
import { Header, ListItem, ThemeContext, ThemeProps } from 'react-native-elements';
import i18n from 'i18n-js';
import Close from 'app/app/components/header/Close';
import { useDispatch, useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { getUserStats } from 'app/app/redux/user/actions';
import Container from 'app/app/components/containers/Container';
import Text from 'app/app/components/elements/Text';
import StringHelper from 'app/services/helpers/StringHelper';

import { StyleSheet, Dimensions, View } from 'react-native';
import { Theme } from 'app/app/styles/themes';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import { SPACING } from 'app/app/styles/sizes';
import DateHelper from 'app/services/helpers/DateHelper';

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
    header: {
        paddingVertical: SPACING.normal
    },
    section: {
        marginBottom: SPACING.medium
    },
    rightBig: {
        width: width - 100 - SPACING.medium,
        textAlign: 'right'
    }
});
interface StateProps {
    stats: Trakt.UserStats;
}
const UserStats = () => {
    const dispatch = useDispatch();
    const { stats } = useSelector<MSState, StateProps>(state => {
        return {
            stats: state.user.stats
        };
    });
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    useEffect(() => {
        dispatch(getUserStats());
    }, []);

    if (!stats) {
        return <ScreenLoader />;
    }

    const {
        shows: { watched: showsWatched, collected: showsCollected, ratings: showsRatings, comments: showComments },
        seasons: { comments: seasonComments, ratings: seasonRatings },
        episodes: {
            watched: episodesWatched,
            collected: episodesCollected,
            ratings: episodesRatings,
            comments: episodesComments,
            minutes: episodesMinutes,
            plays: episodesPlays
        },
        network: { friends, followers, following }
    } = stats;
    const converted = DateHelper.convertToDays(episodesMinutes);
    const totalTime = i18n.t('TOTAL_DAYS_WATCH', {
        days: converted.days,
        hours: converted.hours,
        mins: converted.mins
    });
    return (
        <>
            <Header leftComponent={<Close />} centerComponent={{ text: i18n.t('USER_STATS') }} />
            <Container withPadding={false} showsVerticalScrollIndicator={false}>
                <Text style={{ ...styles.header }} centered color={colors.primary} textStyle="title">
                    {i18n.t('SHOWS')}
                </Text>
                <View style={{ ...styles.section }}>
                    <ListItem title={i18n.t('WATCHED')} rightTitle={StringHelper.formatNumber(showsWatched)} />
                    <ListItem title={i18n.t('COLLECTED')} rightTitle={StringHelper.formatNumber(showsCollected)} />
                    <ListItem title={i18n.t('RATINGS')} rightTitle={StringHelper.formatNumber(showsRatings)} />
                    <ListItem title={i18n.t('COMMENTS')} rightTitle={StringHelper.formatNumber(showComments)} />
                </View>
                <Text style={{ ...styles.header }} centered color={colors.primary} textStyle="title">
                    {i18n.t('SEASONS')}
                </Text>
                <View style={{ ...styles.section }}>
                    <ListItem title={i18n.t('RATINGS')} rightTitle={StringHelper.formatNumber(seasonRatings)} />
                    <ListItem title={i18n.t('COMMENTS')} rightTitle={StringHelper.formatNumber(seasonComments)} />
                </View>
                <Text style={{ ...styles.header }} centered color={colors.primary} textStyle="title">
                    {i18n.t('EPISODES')}
                </Text>
                <View style={{ ...styles.section }}>
                    <ListItem title={i18n.t('WATCHED')} rightTitle={StringHelper.formatNumber(episodesWatched)} />
                    <ListItem title={i18n.t('COLLECTED')} rightTitle={StringHelper.formatNumber(episodesCollected)} />
                    <ListItem title={i18n.t('RATINGS')} rightTitle={StringHelper.formatNumber(episodesRatings)} />
                    <ListItem title={i18n.t('COMMENTS')} rightTitle={StringHelper.formatNumber(episodesComments)} />
                    <ListItem title={i18n.t('TIME')} rightTitle={totalTime} rightTitleStyle={{ ...styles.rightBig }} />
                    <ListItem title={i18n.t('PLAYS')} rightTitle={StringHelper.formatNumber(episodesPlays)} />
                </View>
                <Text style={{ ...styles.header }} centered color={colors.primary} textStyle="title">
                    {i18n.t('NETWORK')}
                </Text>
                <View style={{ ...styles.section }}>
                    <ListItem title={i18n.t('FRIENDS')} rightTitle={StringHelper.formatNumber(friends)} />
                    <ListItem title={i18n.t('FOLLOWERS')} rightTitle={StringHelper.formatNumber(followers)} />
                    <ListItem title={i18n.t('FOLLOWING')} rightTitle={StringHelper.formatNumber(following)} />
                </View>
            </Container>
        </>
    );
};

export default React.memo(UserStats);
