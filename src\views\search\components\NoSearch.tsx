import React from 'react';
import useGuest from 'app/app/hooks/useGuest';
import Recommended from './Recommended';
import Watched from './Watched';
import Trending from './Trending';
import Popular from './Popular';
import Anticipated from './Anticipated';

export interface NoSearchProps {
    hasSearch: boolean;
}

const NoSearch = ({ hasSearch }: NoSearchProps) => {
    const isGuest = useGuest();
    return (
        <>
            {!hasSearch && !isGuest && <Recommended />}
            {!hasSearch && <Watched />}
            {!hasSearch && <Trending />}
            {!hasSearch && <Popular />}
            {!hasSearch && <Anticipated />}
        </>
    );
};

export default React.memo(NoSearch);
