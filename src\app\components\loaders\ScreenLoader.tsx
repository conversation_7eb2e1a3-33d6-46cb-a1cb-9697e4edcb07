import React, { useContext } from 'react';
import { StyleSheet, ActivityIndicator, View, Dimensions } from 'react-native';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme, alpha } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import Text from '../elements/Text';

const { height } = Dimensions.get('screen');
const styles = StyleSheet.create({
    container: {
        ...StyleSheet.absoluteFillObject,
        opacity: 1,
        alignItems: 'center',
        justifyContent: 'center',
        height
    },
    message: {
        padding: SPACING.medium
    }
});

export interface ScreenLoaderProps {
    size?: 'large' | 'small';
    message?: string;
}

const ScreenLoader = ({ size, message }: ScreenLoaderProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    return (
        <View style={[styles.container, { backgroundColor: alpha(colors.background, 0.2) }]}>
            <ActivityIndicator size={size} color={colors.primary} />
            {!!message && (
                <Text centered style={{ ...styles.message }} textStyle="title" color={colors.discreet}>
                    {message}
                </Text>
            )}
        </View>
    );
};
ScreenLoader.defaultProps = {
    size: 'large'
};
export default ScreenLoader;
