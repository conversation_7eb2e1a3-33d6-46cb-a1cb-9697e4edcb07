import React, { useState, useEffect } from 'react';
import i18n from 'i18n-js';
import { Header } from 'react-native-elements';
import Back from 'app/app/components/header/Back';

import ClearButton from 'app/app/components/elements/ClearButton';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Container from 'app/app/components/containers/Container';
import { updateComment } from 'app/app/redux/comment/actions';
import { useDispatch, useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import Form from '../components/Form';

export interface PostReplyProps {
    id: number;
    error: string;
    loading: boolean;
}

const PostReply = ({ id, loading, error }: PostReplyProps) => {
    const dispatch = useDispatch();
    const placeholder = i18n.t('COMMENT');

    const { comment } = useSelector((state: MSState) => {
        return {
            comment: state.entities.comment[id]
        };
    });

    const [data, setData] = useState<Trakt.CommentForm>({
        comment: comment.attributes.comment,
        spoiler: comment.attributes.spoiler
    });

    useEffect(() => {
        setData({ comment: comment.attributes.comment, spoiler: comment.attributes.spoiler });
    }, [comment]);

    const onChange = (data: Trakt.CommentForm) => {
        const postData = { ...data };
        setData(postData);
    };
    const onSubmit = () => {
        dispatch(updateComment(id, data));
    };
    const rightComponent = <ClearButton small title={i18n.t('UPDATE')} onPress={onSubmit} />;

    return (
        <>
            <Header
                {...{
                    leftComponent: <Back />,
                    rightComponent,
                    centerComponent: { text: i18n.t('UPDATE_COMMENT') }
                }}
            />
            <>
                <Container keyboardShouldPersistTaps="handled">
                    <Form {...{ onChange, placeholder, error, data }} />
                    {loading && <ScreenLoader />}
                </Container>
            </>
        </>
    );
};

export default React.memo(PostReply);
