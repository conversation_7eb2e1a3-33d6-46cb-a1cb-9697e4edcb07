import React from 'react';
import { StyleSheet } from 'react-native';
import { StatsNavParams } from 'app/views/stats/view';
import { NavigationInjectedProps, withNavigation } from 'react-navigation';
import Constants from 'app/app/Constants';
import { TouchableOpacity } from 'react-native-gesture-handler';
import i18n from 'i18n-js';
import { SPACING } from 'app/app/styles/sizes';
import Text from '../elements/Text';
import Icon from '../elements/Icon';

const styles = StyleSheet.create({
    moreStats: {
        paddingTop: SPACING.medium,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    }
});

export interface MoreStatsProps {
    title: string;
    id: number;
    seasonNumber?: number;
    episodeNumber?: number;
}

const MoreStats = ({
    id,
    title,
    seasonNumber,
    episodeNumber,
    navigation
}: MoreStatsProps & NavigationInjectedProps<{}>) => {
    const moreStatsPress = () => {
        const params: StatsNavParams = { id, title, seasonNumber, episodeNumber };
        navigation.navigate(Constants.Navigation.Stats.VIEW, params);
    };
    return (
        <TouchableOpacity onPress={moreStatsPress} style={{ ...styles.moreStats }}>
            <Text textStyle="large">{i18n.t('MORE_STATS')}</Text>
            <Icon {...{ name: 'chevron-right' }} />
        </TouchableOpacity>
    );
};
MoreStats.defaultProps = {
    seasonNumber: 0,
    episodeNumber: 0
};
export default withNavigation(React.memo(MoreStats));
