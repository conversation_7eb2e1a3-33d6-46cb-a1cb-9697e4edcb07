import React, { useEffect } from 'react';
import { FlatList } from 'react-native';
import { NavParams } from 'app/app/navigation/Navigator';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import Close from 'app/app/components/header/Close';
import { useDispatch, useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import Container from 'app/app/components/containers/Container';
import CommentListItem from 'app/app/components/listItems/comment/CommentListItem';
import { useNavigation } from 'react-navigation-hooks';
import { getShowComments, getSeasonComments, getEpisodeComments } from 'app/app/redux/comment/actions';
import ApiHelper from 'app/services/helpers/ApiHelper';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import MoreLoader from 'app/app/components/loaders/MoreLoader';
import { ShowCommnetsState, SeasonCommentsState } from 'app/app/redux/comment/reducers';

export type CommentsType = 'show' | 'season' | 'episode';
export interface CommentNavParams extends NavParams {
    type: CommentsType;
    id: number;
    seasonNumber?: number;
    episodeNumber?: number;
}

const Comments = () => {
    const { getParam } = useNavigation();
    const id = getParam('id');
    const type: CommentsType = getParam('type');
    const seasonNumber: number = getParam('seasonNumber');
    const episodeNumber: number = getParam('episodeNumber');
    const dispatch = useDispatch();

    useEffect(() => {
        switch (type) {
            case 'show':
            default:
                dispatch(getShowComments(id, { limit: 20, sort: 'newest' }));
                break;
            case 'season':
                dispatch(getSeasonComments(id, seasonNumber, { limit: 20, sort: 'newest' }));
                break;
            case 'episode':
                dispatch(getEpisodeComments(id, seasonNumber, episodeNumber, { limit: 20, sort: 'newest' }));
                break;
        }
    }, []);
    const {
        ids,
        headers,
        loadingMore
    }: { ids: number[]; headers: Api.TraktHeaders; loadingMore: boolean } = useSelector((state: MSState) => {
        switch (type) {
            case 'show':
            default: {
                const show = state.comments[type] as ShowCommnetsState;
                return {
                    ids: show[id].result,
                    headers: show[id].headers,
                    loadingMore: show.loadingMore
                };
            }
            case 'season': {
                const season = state.comments[type] as SeasonCommentsState;
                return {
                    ids: season[id][seasonNumber].result,
                    headers: season[id][seasonNumber].headers,
                    loadingMore: season.loadingMore
                };
            }
            case 'episode': {
                const episode = state.comments[type] as SeasonCommentsState;
                return {
                    ids: episode[id][seasonNumber][episodeNumber].result,
                    headers: episode[id][seasonNumber][episodeNumber].headers,
                    loadingMore: episode.loadingMore
                };
            }
        }
    });

    const renderItem = ({ item }: { item: number }) => {
        return <CommentListItem {...{ id: item, showBorder: true }} />;
    };
    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (!loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                page: pagination.currentPage + 1
            };

            switch (type) {
                case 'show':
                default:
                    dispatch(getShowComments(id, params));
                    break;
                case 'season':
                    dispatch(getSeasonComments(id, seasonNumber, params));
                    break;
                case 'episode':
                    dispatch(getEpisodeComments(id, seasonNumber, episodeNumber, params));
                    break;
            }
        }
    };
    return (
        <>
            <Header leftComponent={<Close />} centerComponent={{ text: i18n.t('COMMENTS') }} />
            <Container useScrollView={false}>
                <FlatList<number>
                    {...{ data: ids, keyExtractor: item => item.toString(), renderItem }}
                    onEndReached={loadMore}
                    onEndReachedThreshold={0.1}
                    ListFooterComponent={loadingMore && <MoreLoader />}
                />
            </Container>
        </>
    );
};

export default React.memo(Comments);
