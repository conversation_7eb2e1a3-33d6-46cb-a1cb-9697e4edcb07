import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button } from 'react-native-elements';
import Text from 'app/app/components/elements/Text';
import i18n from 'i18n-js';
import { StyleSheet, Image, View } from 'react-native';
import * as AuthSession from 'expo-auth-session';
import Constants from 'app/app/Constants';
import { getToken, guestSignIn } from 'app/app/redux/auth/actions';
import { MSState } from 'app/app/redux/redux';
import { SPACING } from 'app/app/styles/sizes';
import { common } from 'app/app/styles/themes';
import cover from 'app/assets/images/show/poster_big_dark.png';
import { BlurView } from 'expo-blur';
import Divider from 'app/app/components/containers/Divider';
import ClearButton from 'app/app/components/elements/ClearButton';
import { useNavigation } from 'react-navigation-hooks';
import Database from 'app/services/storage/DB';
import Storage from 'app/services/storage/Storage';
import styles from './styles';

const SignIn = () => {
    const { navigate } = useNavigation();
    const dispatch = useDispatch();
    const { token, isGuest } = useSelector((state: MSState) => {
        const { token } = state.auth.trakt;
        const { isGuest } = state.auth.guest;
        return {
            token,
            isGuest
        };
    });

    const navigateToApp = () => {
        navigate(Constants.Navigation.Auth.INIT);
    };

    useEffect(() => {
        Storage.getItem(Storage.KEY_BASIC_SETTINGS).then(settings => {
            if (!settings || settings !== 1) {
                navigate(Constants.Navigation.Auth.SELECT_SETTINGS);
            }
        });
        if (token || isGuest) {
            navigateToApp();
        }
    }, []);

    useEffect(() => {
        Database.createTables(false);
        if (token !== null || isGuest) {
            navigateToApp();
        }
    }, [token, isGuest]);

    const signIn = () => {
        const redirectUrl = AuthSession.getRedirectUrl();        
        const authUrl =
            `${Constants.Api.Trakt.AUTH}` +
            `?response_type=${Constants.Api.Trakt.RESPONSE_TYPE}` +
            `&client_id=${Constants.Api.Trakt.CLIENT_ID}` +
            `&redirect_uri=${encodeURIComponent(redirectUrl)}`;        
        AuthSession.startAsync({ authUrl }).then(result => {
            if (result.type === 'success') {
                const { code } = result.params;
                dispatch(getToken(code));
            } else {
                console.error({ result });
            }
        });
    };

    const signInGuest = () => {
        dispatch(guestSignIn());
    };

    return (
        <>
            <View
                style={{
                    ...StyleSheet.absoluteFillObject,
                    ...styles.imageContainer
                }}
            >
                <Image
                    source={cover}
                    style={{
                        ...styles.background
                    }}
                    resizeMode="stretch"
                />
                <BlurView tint="dark" intensity={50} style={{ ...StyleSheet.absoluteFillObject }} />
            </View>
            <View style={styles.container}>
                <Text textStyle="larger" color={common.white} centered>
                    {i18n.t('SIGN_IN_TEXT')}
                </Text>
                <Button title={i18n.t('SIGN_IN')} onPress={signIn} />
                <Text textStyle="large" color={common.white} centered>
                    {i18n.t('TRAKT_SIGN_IN_EXPL')}
                </Text>
                <Divider border size={SPACING.large} />
                <Text color={common.white} centered>
                    {i18n.t('GUEST_TEXT')}
                </Text>
                <ClearButton title={i18n.t('GUEST_SIGN_IN')} onPress={signInGuest} />
            </View>
        </>
    );
};
export default SignIn;
