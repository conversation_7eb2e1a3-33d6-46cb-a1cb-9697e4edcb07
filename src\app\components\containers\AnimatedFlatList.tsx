import React from 'react';
import { FlatListProps, Animated, FlatList, ListRenderItemInfo, Dimensions, Platform } from 'react-native';

const { height } = Dimensions.get('window');

export interface AnimatedFlatListProps<T> extends FlatListProps<T> {
    itemHeight: number;
}

const AnimatedFlatList = <T,>(props: AnimatedFlatListProps<T>) => {
    const AnFlatList = Animated.createAnimatedComponent(FlatList);
    const y = new Animated.Value(0);
    const onScroll = Animated.event([{ nativeEvent: { contentOffset: { y } } }], { useNativeDriver: true });
    const { renderItem: _renderItem, itemHeight } = props;
    const renderItem = (info: ListRenderItemInfo<T>) => {
        const fix = Platform.OS === 'ios' ? itemHeight * 1.5 : itemHeight;
        const position = Animated.subtract(info.index * itemHeight, y);
        const isDisappearing = -itemHeight;
        const isTop = 0;
        const isBottom = height - itemHeight - fix;
        const isAppearing = height - fix;
        const translateY = Animated.add(
            y,
            y.interpolate({
                inputRange: [0, 0.00001 + info.index * itemHeight],
                outputRange: [0, -info.index * itemHeight],
                extrapolateRight: 'clamp'
            })
        );
        const scale = position.interpolate({
            inputRange: [isDisappearing, isTop, isBottom, isAppearing],
            outputRange: [0.7, 1, 1, 0.7],
            extrapolate: 'clamp'
        });
        const opacity = position.interpolate({
            inputRange: [isDisappearing, isTop, isBottom, isAppearing],
            outputRange: [0.3, 1, 1, 0.3],
            extrapolate: 'clamp'
        });
        return (
            <Animated.View style={{ opacity, transform: [{ translateY }, { scale }] }}>
                {_renderItem(info)}
            </Animated.View>
        );
    };
    return <AnFlatList<T> {...props} renderItem={renderItem} {...{ onScroll }} scrollEventThrottle={16} />;
};

export default React.memo(AnimatedFlatList) as typeof AnimatedFlatList;
