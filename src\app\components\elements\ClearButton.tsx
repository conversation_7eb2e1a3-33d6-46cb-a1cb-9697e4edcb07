import React from 'react';
import { ButtonProps, Button, ThemeConsumer } from 'react-native-elements';
import { Theme } from 'styles/themes';
import { StyleSheet } from 'react-native';
import { FONT_SIZE } from 'app/app/styles/sizes';
import _ from 'lodash';

const styles = StyleSheet.create({
    disabled: {
        opacity: 0.4,
        backgroundColor: 'transparent'
    }
});
export interface ClearButtonProps extends ButtonProps {
    small?: boolean;
}

const ClearButton = (props: ClearButtonProps) => {
    const backgroundColor = 'transparent';
    const { buttonStyle, titleStyle, small } = props;
    const title = small ? { fontSize: FONT_SIZE.normal } : titleStyle;
    return (
        <ThemeConsumer<Theme>>
            {({ theme: { colors } }) => {
                return (
                    <Button
                        {...props}
                        titleStyle={[{ color: colors.primary }, title]}
                        disabledStyle={{ ...styles.disabled }}
                        disabledTitleStyle={{ color: colors.primary }}
                        buttonStyle={[
                            {
                                backgroundColor
                            },
                            buttonStyle
                        ]}
                    />
                );
            }}
        </ThemeConsumer>
    );
};

export default React.memo(ClearButton, _.isEqual);
