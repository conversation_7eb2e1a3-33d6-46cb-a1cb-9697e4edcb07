import React, { useState } from 'react';
import i18n from 'i18n-js';
import { Header } from 'react-native-elements';
import Back from 'app/app/components/header/Back';

import ClearButton from 'app/app/components/elements/ClearButton';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Container from 'app/app/components/containers/Container';
import { postCommentReply } from 'app/app/redux/comment/actions';
import CommentListItem from 'app/app/components/listItems/comment/CommentListItem';
import { useDispatch } from 'react-redux';
import Form from '../components/Form';

export interface PostReplyProps {
    id: number;
    error: string;
    loading: boolean;
}

const PostReply = ({ id, loading, error }: PostReplyProps) => {
    const dispatch = useDispatch();
    const placeholder = i18n.t('REPLY');
    const [data, setData] = useState<Trakt.CommentForm>({ comment: '', spoiler: false });

    const onChange = (data: Trakt.CommentForm) => {
        const postData = { ...data };
        setData(postData);
    };
    const onSubmit = () => {
        dispatch(postCommentReply(id, data));
    };
    const rightComponent = <ClearButton small title={i18n.t('REPLY')} onPress={onSubmit} />;

    return (
        <>
            <Header
                {...{
                    leftComponent: <Back />,
                    rightComponent,
                    centerComponent: { text: i18n.t('REPLY_TO_COMMENT') }
                }}
            />
            <>
                <CommentListItem {...{ id, basic: true, showBorder: true }} />
                <Container keyboardShouldPersistTaps="handled">
                    <Form {...{ onChange, placeholder, error }} />
                    {loading && <ScreenLoader />}
                </Container>
            </>
        </>
    );
};

export default React.memo(PostReply);
