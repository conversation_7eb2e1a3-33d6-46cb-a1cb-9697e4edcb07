import { RequestParams } from 'app/services/helpers/RequestHelper';
import apiClient from 'app/services/api/client';
import Database from 'app/services/storage/DB';
import _ from 'lodash';
import { normalize } from 'normalizr';
import { getEpisodeDetails } from '../episode/actions';
import { UNATHORIZED } from '../app/actions';
import { MSState } from '../redux';
import { nextEpisodeSchema, TraktShowSchema } from '../schemas';
import { UPDATE_WATCHED } from '../meta/actions';
import { updateLastActivity } from './actions';
import { getShow } from '../show/actions';
import { getShowImages } from '../image/actions';

export const CLEAR_WATCHED_REQUEST = 'CLEAR_WATCHED_REQUEST';
export const CLEAR_WATCHED_SUCCESS = 'CLEAR_WATCHED_SUCCESS';
export const CLEAR_WATCHED_ERROR = 'CLEAR_WATCHED_ERROR';

export const WATCH_SHOW_REQUEST = 'WATCH_SHOW_REQUEST';
export const WATCH_SHOW_SUCCESS = 'WATCH_SHOW_SUCCESS';
export const WATCH_SHOW_ERROR = 'WATCH_SHOW_ERROR';

export const UNWATCH_SHOW_REQUEST = 'UNWATCH_SHOW_REQUEST';
export const UNWATCH_SHOW_SUCCESS = 'UNWATCH_SHOW_SUCCESS';
export const UNWATCH_SHOW_ERROR = 'UNWATCH_SHOW_ERROR';

export const GET_WATCHED_REQUEST = 'GET_WATCHED_REQUEST';
export const GET_WATCHED_SUCCESS = 'GET_WATCHED_SUCCESS';
export const GET_WATCHED_ERROR = 'GET_WATCHED_ERROR';

export const WATCH_EPISODE_REQUEST = 'WATCH_EPISODE_REQUEST';
export const WATCH_EPISODE_SUCCESS = 'WATCH_EPISODE_SUCCESS';
export const WATCH_EPISODE_ERROR = 'WATCH_EPISODE_ERROR';

export const UNWATCH_EPISODE_REQUEST = 'UNWATCH_EPISODE_REQUEST';
export const UNWATCH_EPISODE_SUCCESS = 'UNWATCH_EPISODE_SUCCESS';
export const UNWATCH_EPISODE_ERROR = 'UNWATCH_EPISODE_ERROR';

export const WATCH_SEASON_REQUEST = 'WATCH_SEASON_REQUEST';
export const WATCH_SEASON_SUCCESS = 'WATCH_SEASON_SUCCESS';
export const WATCH_SEASON_ERROR = 'WATCH_SEASON_ERROR';

export const UNWATCH_SEASON_REQUEST = 'UNWATCH_SEASON_REQUEST';
export const UNWATCH_SEASON_SUCCESS = 'UNWATCH_SEASON_SUCCESS';
export const UNWATCH_SEASON_ERROR = 'UNWATCH_SEASON_ERROR';

export const GET_SHOW_PROGRESS_REQUEST = 'GET_SHOW_PROGRESS_REQUEST';
export const GET_SHOW_PROGRESS_SUCCESS = 'GET_SHOW_PROGRESS_SUCCESS';
export const GET_SHOW_PROGRESS_ERROR = 'GET_SHOW_PROGRESS_ERROR';

export const GET_WATCHED_HISTORY_FOR_FAVS_REQUEST = 'GET_WATCHED_HISTORY_FOR_FAVS_REQUEST';
export const GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS = 'GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS';
export const GET_WATCHED_HISTORY_FOR_FAVS_ERROR = 'GET_WATCHED_HISTORY_FOR_FAVS_ERROR';

export const GET_NEXT_EPISODE_REQUEST = 'GET_NEXT_EPISODE_REQUEST';
export const GET_NEXT_EPISODE_SUCCESS = 'GET_NEXT_EPISODE_SUCCESS';
export const GET_NEXT_EPISODE_ERROR = 'GET_NEXT_EPISODE_ERROR';

const extractWatchedHistory = (data: any[]): { [key: number]: Trakt.BasicShow } => {
    const shows = {};
    data.forEach(row => {
        shows[row.show.ids.trakt] = row.show;
    });
    return shows;
};

const extractWatchedData = (data: any[]) => {
    const rows = [];
    data.forEach(value => {
        const { show, seasons } = value;
        const showId = show.ids.trakt;
        rows.push({ show: showId, season: -1, episode: -1, percent: 0 });
        seasons.forEach(season => {
            const seasonNumber = season.number;
            rows.push({ show: showId, season: seasonNumber, episode: -1, percent: 0 });
            season.episodes.forEach(episode => {
                const episodeNumber = episode.number;
                rows.push({ show: showId, season: seasonNumber, episode: episodeNumber, percent: 100 });
            });
        });
    });
    return rows;
};

const extractProgressData = (show: number, data: any) => {
    const { aired, completed, seasons, next_episode } = data;
    const rows = [];
    const progress = Math.round((100 * completed) / aired);
    rows.push({ show, season: -1, episode: -1, progress });
    seasons.forEach(season => {
        const progress = Math.round((100 * season.completed) / season.aired);
        rows.push({ show, season: season.number, episode: -1, progress });
        season.episodes.forEach(episode => {
            const progress = episode.completed ? 100 : 0;
            rows.push({ show, season: season.number, episode: episode.number, progress });
        });
    });
    return { rows, next_episode };
};

export const getNextEpisode = (show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }

    return dispatch(
        apiClient(
            [
                GET_NEXT_EPISODE_REQUEST,
                {
                    type: GET_NEXT_EPISODE_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const {
                                next_episode
                            }: { rows: any[]; next_episode: Trakt.NextEpisode } = extractProgressData(show, json);
                            return { show, nextEpisode: next_episode };
                        });
                    }
                },
                GET_NEXT_EPISODE_ERROR
            ],
            `/shows/${show}/progress/watched`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const getShowProgress = (show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const params: RequestParams = { limit: 2000 };
    const traktShow = state.entities.show[show];
    const title = traktShow ? traktShow.attributes.title : null;
    const showIds = traktShow ? traktShow.attributes.ids : null;
    const year = traktShow ? traktShow.attributes.year : null;

    return dispatch(
        apiClient(
            [
                GET_SHOW_PROGRESS_REQUEST,
                {
                    type: GET_SHOW_PROGRESS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const {
                                rows,
                                next_episode
                            }: { rows: any[]; next_episode: Trakt.NextEpisode } = extractProgressData(show, json);
                            Database.query('DELETE FROM watched where show = ?', [show]);
                            const sql = 'INSERT OR REPLACE INTO watched (show,season,episode,percent) VALUES ';
                            _.chunk(rows, 50).forEach(progress => {
                                const queries = progress.map(row => {
                                    const { show, season, episode, progress } = row;
                                    return {
                                        q: '(?,?,?,?)',
                                        p: [show, season, episode, progress]
                                    };
                                });
                                const placeholders = queries.map(q => q.q).join(',');
                                const values = queries.map(q => q.p).flat(1);
                                const query = `${sql}${placeholders}`;
                                Database.query(query, values);
                            });
                            let normalized = { entities: {}, result: null };
                            const isFavorite = state.favorites.result.includes(show);
                            if (isFavorite) {
                                if (next_episode) {
                                    const {
                                        season,
                                        number,
                                        ids: { tmdb }
                                    } = next_episode;
                                    const episodeDetails = state.entities.episodeDetails[tmdb];
                                    if (traktShow && !episodeDetails) {
                                        dispatch(getEpisodeDetails(traktShow.attributes.ids.tmdb, season, number));
                                    }
                                }
                                normalized = next_episode
                                    ? normalize(
                                          { ...next_episode, ...{ show: { ids: showIds, title, year } } },
                                          nextEpisodeSchema
                                      )
                                    : { entities: {}, result: null };
                            }
                            dispatch({ type: UPDATE_WATCHED });
                            return { show, normalized };
                        });
                    }
                },
                GET_SHOW_PROGRESS_ERROR
            ],
            `/shows/${show}/progress/watched`,
            'GET',
            'trakt',
            params,
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const updateShow = (show: number, dispatch) => {
    dispatch(getShowProgress(show));
};

export const clearWatched = () => dispatch => {
    dispatch({ type: CLEAR_WATCHED_REQUEST });
    return Database.query('DROP TABLE watched')
        .then(() => {
            dispatch({ type: UPDATE_WATCHED });
            return dispatch({ type: CLEAR_WATCHED_SUCCESS });
        })
        .catch(error => {
            dispatch({ type: CLEAR_WATCHED_ERROR, error });
        });
};

export const getWatched = () => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                GET_WATCHED_REQUEST,
                {
                    type: GET_WATCHED_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async json => {
                            const watched = extractWatchedData(json);
                            await Database.query('DELETE FROM watched');
                            const sql = 'INSERT INTO watched (show,season,episode,percent) VALUES ';
                            _.chunk(watched, 50).forEach(watch => {
                                const queries = watch.map(row => {
                                    const { show, season, episode, percent } = row;
                                    return {
                                        q: '(?,?,?,?)',
                                        p: [show, season, episode, percent]
                                    };
                                });
                                const placeholders = queries.map(q => q.q).join(',');
                                const values = queries.map(q => q.p).flat(1);
                                const query = `${sql}${placeholders}`;
                                Database.query(query, values);
                            });
                            dispatch({ type: UPDATE_WATCHED });
                            return {};
                        });
                    }
                },
                GET_WATCHED_ERROR
            ],
            `/sync/watched/shows`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const watchEpisode = (show: number, season: number, episode: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                WATCH_EPISODE_REQUEST,
                {
                    type: WATCH_EPISODE_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            updateShow(show, dispatch);
                            dispatch(updateLastActivity());
                            dispatch({ type: UPDATE_WATCHED });
                            return {};
                        });
                    }
                },
                WATCH_EPISODE_ERROR
            ],
            `/sync/history`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season, episodes: [{ number: episode }] }] }]
                }
            }
        )
    );
};

export const unWatchEpisode = (show: number, season: number, episode: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UNWATCH_EPISODE_REQUEST,
                {
                    type: UNWATCH_EPISODE_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            updateShow(show, dispatch);
                            dispatch(updateLastActivity());
                            dispatch({ type: UPDATE_WATCHED });
                            return {};
                        });
                    }
                },
                UNWATCH_EPISODE_ERROR
            ],
            `/sync/history/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season, episodes: [{ number: episode }] }] }]
                }
            }
        )
    );
};

export const watchSeason = (show: number, season: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                WATCH_SEASON_REQUEST,
                {
                    type: WATCH_SEASON_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            updateShow(show, dispatch);
                            dispatch(updateLastActivity());
                            dispatch({ type: UPDATE_WATCHED });
                            return { json };
                        });
                    }
                },
                WATCH_SEASON_ERROR
            ],
            `/sync/history`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season }] }]
                }
            }
        )
    );
};

export const unWatchSeason = (show: number, season: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UNWATCH_SEASON_REQUEST,
                {
                    type: UNWATCH_SEASON_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            updateShow(show, dispatch);
                            dispatch(updateLastActivity());
                            dispatch({ type: UPDATE_WATCHED });
                            return {};
                        });
                    }
                },
                UNWATCH_SEASON_ERROR
            ],
            `/sync/history/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season }] }]
                }
            }
        )
    );
};

export const watchShow = (show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                WATCH_SHOW_REQUEST,
                {
                    type: WATCH_SHOW_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            updateShow(show, dispatch);
                            dispatch(updateLastActivity());
                            dispatch({ type: UPDATE_WATCHED });
                            return {};
                        });
                    }
                },
                WATCH_SHOW_ERROR
            ],
            `/sync/history`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: { shows: [{ ids: { trakt: show } }] }
            }
        )
    );
};

export const unWatchShow = (show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UNWATCH_SHOW_REQUEST,
                {
                    type: UNWATCH_SHOW_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(() => {
                            updateShow(show, dispatch);
                            dispatch(updateLastActivity());
                            dispatch({ type: UPDATE_WATCHED });
                            return {};
                        });
                    }
                },
                UNWATCH_SHOW_ERROR
            ],
            `/sync/history/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: { shows: [{ ids: { trakt: show } }] }
            }
        )
    );
};

export const getWatchedHistoryForFavs = (params: RequestParams) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                GET_WATCHED_HISTORY_FOR_FAVS_REQUEST,
                {
                    type: GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async json => {
                            const shows = extractWatchedHistory(json);
                            const promises = [];
                            Object.keys(shows).forEach(key => {
                                promises.push(dispatch(getNextEpisode(parseInt(key))));
                            });
                            return Promise.all(promises).then(results => {
                                const importShows = results
                                    .map(r => {
                                        const has = !!r.payload.nextEpisode;
                                        if (has) {
                                            return shows[r.payload.show];
                                        }
                                        return null;
                                    })
                                    .filter(v => !!v);
                                importShows.forEach(show => {
                                    dispatch(getShowImages(show.ids.tmdb));
                                    dispatch(getShow(show.ids.trakt));
                                });
                                const normalized = normalize(importShows, [TraktShowSchema]);
                                return { normalized };
                            });
                        });
                    }
                },
                GET_WATCHED_HISTORY_FOR_FAVS_ERROR
            ],
            `/users/me/history/episodes/`,
            'GET',
            'trakt',
            params,
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};
