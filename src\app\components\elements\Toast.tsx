import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import RNToast, { ToastOptions } from 'react-native-toast-message';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme, common } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import Text from './Text';

const styles = StyleSheet.create({
    container: {
        width: '100%',
        height: 60,
        paddingBottom: SPACING.normal,
        paddingHorizontal: SPACING.medium,
        justifyContent: 'center'
    }
});

export const defaultOptions: Partial<ToastOptions> = {
    type: 'info',
    autoHide: true,
    visibilityTime: 3000,
    bottomOffset: 0,
    position: 'bottom'
};

export interface ToastProps {}

const Toast = () => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const toastConfig = {
        success: internalState => (
            <View style={{ ...styles.container, backgroundColor: colors.success }}>
                <Text centered>{internalState.text1}</Text>
            </View>
        ),
        error: internalState => (
            <View style={{ ...styles.container, backgroundColor: colors.error }}>
                <Text centered>{internalState.text1}</Text>
            </View>
        ),
        info: internalState => (
            <View style={{ ...styles.container, backgroundColor: common.blue }}>
                <Text centered>{internalState.text1}</Text>
            </View>
        )
    };
    return <RNToast config={toastConfig} ref={ref => RNToast.setRef(ref)} />;
};

export default React.memo(Toast);
