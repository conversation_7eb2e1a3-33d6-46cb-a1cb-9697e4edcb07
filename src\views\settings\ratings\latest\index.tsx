import React, { useEffect } from 'react';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import Back from 'app/app/components/header/Back';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { getLatestRatings } from 'app/app/redux/user/actions';
import Container from 'app/app/components/containers/Container';
import { FlatList } from 'react-native';
import EpisodeRating from 'app/app/components/listItems/episode/EpisodeRating';
import MoreLoader from 'app/app/components/loaders/MoreLoader';
import ApiHelper from 'app/services/helpers/ApiHelper';
import { RequestParams } from 'app/services/helpers/RequestHelper';

export interface LatestRatingsProps {}
interface StateProps {
    latestRatingsIds: number[];
    headers: Api.TraktHeaders;
    loadingMore: boolean;
}
const LatestRatings = () => {
    // REDUX
    const dispatch = useDispatch();

    const { latestRatingsIds, headers, loadingMore } = useSelector<MSState, StateProps>(state => {
        const latestRatingsIds = state.user.latestRatings?.result;
        const headers = state.user.latestRatings?.headers;
        return {
            latestRatingsIds,
            headers,
            loadingMore: state.user.latestRatings.loadingMore
        };
    });

    useEffect(() => {
        if (latestRatingsIds.length === 0) {
            dispatch(getLatestRatings());
        }
    }, []);

    const renderItem = ({ item }: { item: number }) => {
        return <EpisodeRating {...{ id: item }} />;
    };
    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (!loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                extended: 'full',
                page: pagination.currentPage + 1
            };
            dispatch(getLatestRatings(params));
        }
    };

    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: i18n.t('LAST_RATINGS') }} />
            <Container useScrollView={false}>
                <FlatList<number>
                    data={latestRatingsIds}
                    keyExtractor={item => item.toString()}
                    renderItem={renderItem}
                    onEndReached={loadMore}
                    onEndReachedThreshold={0.1}
                    ListFooterComponent={loadingMore && <MoreLoader />}
                />
            </Container>
        </>
    );
};

export default React.memo(LatestRatings);
