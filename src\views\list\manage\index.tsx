import React, { useState, createRef, useEffect } from 'react';
import { NavParams } from 'app/app/navigation/Navigator';
import { Header, Input } from 'react-native-elements';
import i18n from 'i18n-js';
import Back from 'app/app/components/header/Back';
import Container from 'app/app/components/containers/Container';
import ClearButton from 'app/app/components/elements/ClearButton';
import { Keyboard, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { createList, updateList } from 'app/app/redux/list/actions';
import { MSState } from 'app/app/redux/redux';
import { useNavigation } from 'react-navigation-hooks';
import { resetForm } from 'app/app/redux/meta/actions';
import { SPACING } from 'app/app/styles/sizes';
import TextInput from 'app/app/components/elements/form/TextInput';
import CheckBox from 'app/app/components/elements/form/CheckBox';
import ButtonGroup from 'app/app/components/elements/form/ButtonGroup';
import Select from 'app/app/components/elements/form/Select';

i18n.missingTranslationPrefix = 'EE: ';
const styles = StyleSheet.create({
    inputField: { padding: 0, paddingLeft: 0, paddingBottom: 0, marginBottom: 0 },
    fieldContainer: { padding: 0, borderBottomWidth: 0, paddingRight: SPACING.medium },
    checkBox: { paddingVertical: SPACING.normal, paddingRight: 0 },
    chevron: { paddingVertical: SPACING.normal, paddingRight: SPACING.normal * 2.5 },
    buttons: { paddingVertical: SPACING.normal, paddingLeft: 0, paddingRight: SPACING.normal }
});
export interface ManageNavParams extends NavParams {
    list?: Api.Entity<Trakt.CustomList>;
}

const Manage = () => {
    const dispatch = useDispatch();
    const { goBack, getParam } = useNavigation();
    const list: Api.Entity<Trakt.CustomList> = getParam('list');

    /** FORM */
    const [privacy, setPrivacy] = useState(
        !list ? 0 : list.attributes.privacy === 'public' ? 0 : list.attributes.privacy === 'private' ? 1 : 2
    );
    const [name, setName] = useState(list?.attributes.name || '');
    const [description, setDescreption] = useState(list?.attributes.description || '');
    const [display_numbers, setDisplayNumbers] = useState(!!list?.attributes.display_numbers);
    const [allow_comments, setAllowComments] = useState(!!list?.attributes.allow_comments);
    const [sort_by, setSortBy] = useState(list?.attributes.sort_by);
    const [sort_how, setSortHow] = useState(list?.attributes.sort_how);
    /** END FORM */

    const descriptionRef = createRef<Input>();

    const [error, setError] = useState('');
    const { loading, success } = useSelector((state: MSState) => {
        return state.meta.form;
    });
    const onPrivacyPress = (index: number) => {
        setPrivacy(index);
    };
    const clearError = () => {
        setError('');
    };

    useEffect(() => {
        if (success === true) {
            dispatch(resetForm());
            goBack(null);
        }
    }, [success]);
    const submit = () => {
        Keyboard.dismiss();
        if (!name || name.trim() === '') {
            setError(i18n.t('NAME_REQUIRED'));
        }
        const data: Trakt.CustomListForm = {
            name,
            description,
            privacy: privacy === 0 ? 'public' : privacy === 1 ? 'private' : 'friends',
            allow_comments,
            display_numbers,
            sort_how,
            sort_by
        };
        if (list) {
            dispatch(updateList(list.id as number, data));
        } else {
            dispatch(createList(data));
        }
    };
    const action = list ? i18n.t('UPDATE') : i18n.t('CREATE');
    const title = list ? `${i18n.t('UPDATE')} ${list.attributes.name}` : i18n.t('CREATE_LIST');
    const rightComponent = <ClearButton small title={action} onPress={submit} />;
    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: title }} rightComponent={rightComponent} />
            <Container keyboardShouldPersistTaps="handled" loading={loading}>
                <TextInput
                    autoFocus
                    onFocus={clearError}
                    errorMessage={error}
                    placeholder={i18n.t('NAME')}
                    value={name}
                    onChangeText={setName}
                    onEndEditing={() => {
                        descriptionRef.current.focus();
                    }}
                    blurOnSubmit={false}
                    onSubmitEditing={() => {
                        descriptionRef.current.focus();
                    }}
                    returnKeyType="next"
                    containerStyle={{ ...styles.inputField }}
                />

                <TextInput
                    containerStyle={{ ...styles.inputField }}
                    ref={descriptionRef}
                    placeholder={i18n.t('DESCRIPTION')}
                    value={description}
                    onChangeText={setDescreption}
                    onSubmitEditing={() => {
                        Keyboard.dismiss();
                    }}
                />

                <CheckBox
                    label={i18n.t('DISPLAY_NUMBERS')}
                    subtitle={i18n.t('DISPLAY_NUMBERS_TIP')}
                    checked={display_numbers}
                    onPress={() => setDisplayNumbers(!display_numbers)}
                />
                <CheckBox
                    label={i18n.t('ALLOW_COMMENTS')}
                    subtitle={i18n.t('ALLOW_COMMENTS_TIP')}
                    checked={allow_comments}
                    onPress={() => setAllowComments(!allow_comments)}
                />
                <Select
                    title={i18n.t('SORT')}
                    subtitle={i18n.t('SORT_TIP')}
                    onSelect={setSortBy}
                    selected={sort_by}
                    options={[
                        { label: i18n.t('RANK'), value: 'rank' },
                        { label: i18n.t('ADDED'), value: 'added' },
                        { label: i18n.t('TITLE'), value: 'title' },
                        { label: i18n.t('RELEASED'), value: 'released' },
                        { label: i18n.t('RUNTIME'), value: 'runtime' },
                        { label: i18n.t('POPULARITY'), value: 'popularity' },
                        // { label: 'Percentage', value: 'percentage' },
                        { label: i18n.t('VOTES_C'), value: 'votes' },
                        // { label: 'My rating', value: 'my_rating' },
                        { label: i18n.t('RANDOM'), value: 'random' },
                        { label: i18n.t('WATCHED'), value: 'watched' },
                        { label: i18n.t('COLLECTED'), value: 'collected' }
                    ]}
                />
                <Select
                    title={i18n.t('SORT_DIR')} // "Sort direction"
                    subtitle={i18n.t('SORT_DIR_TIP')} // "Sorting direction"
                    onSelect={setSortHow}
                    selected={sort_how}
                    options={[
                        { label: i18n.t('ASCENDING'), value: 'asc' },
                        { label: i18n.t('DESCENDING'), value: 'desc' }
                    ]}
                />
                <ButtonGroup
                    buttons={['public', 'private', 'friends']}
                    selectedIndex={privacy}
                    onPress={onPrivacyPress}
                />
            </Container>
        </>
    );
};

export default React.memo(Manage);
