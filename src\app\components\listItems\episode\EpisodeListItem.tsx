import React, { useContext } from 'react';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ListItem, ThemeContext, ThemeProps } from 'react-native-elements';
import useWatched from 'app/app/hooks/useWatched';
import { Theme } from 'app/app/styles/themes';
import useLocale from 'app/app/hooks/useLocale';
import DateHelper from 'app/services/helpers/DateHelper';
import Image from '../../elements/Image';

const IMAGE_WIDTH = 92;
const RATIO = 0.56;
export interface EpisodeListItemProps {
    show: number;
    season: number;
    episode: number;
    name: string;
    air_date: string;
    episode_number: number;
    still_path: string;
    onPress: () => void;
}

const EpisodeListItem = ({
    show,
    season,
    episode,
    name,
    air_date,
    episode_number,
    still_path,
    onPress
}: EpisodeListItemProps) => {
    const locale = useLocale();

    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const uri = ShowHelper.getStillUri(still_path, 25, scheme);
    const width = IMAGE_WIDTH;
    const height = IMAGE_WIDTH * RATIO;
    const { watched } = useWatched(show, season, episode);
    const airDate = DateHelper.format(air_date, locale);
    return (
        <ListItem
            title={`${episode_number}. ${name}`}
            subtitle={airDate}
            leftElement={<Image {...{ height, width, uri }} />}
            chevron
            onPress={onPress}
            checkmark={watched}
        />
    );
};

export default React.memo(EpisodeListItem);
