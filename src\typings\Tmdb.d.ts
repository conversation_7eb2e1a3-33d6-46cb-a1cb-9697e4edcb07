// eslint-disable-next-line @typescript-eslint/no-unused-vars
namespace TMDB {
    type PosterImageSize = 'w92' | 'w185' | 'w500' | 'w780' | 'original';
    type BackdropImageSize = 'w300' | 'w780' | 'w1280' | 'original';
    type ProfileImageSize = 'w45' | 'w185' | 'w632' | 'original';
    type StillImageSize = 'w92' | 'w185' | 'w300' | 'original';
    type PeopleType = 'cast' | 'crew';
    type RatingType = 'movies' | 'shows' | 'seasons' | 'episodes' | 'all';
    interface ExternalIds {
        id?: number;
        imdb_id: string;
        tvdb_id: number;
        facebook_id?: string;
        instagram_id?: string;
        twitter_id?: string;
        freebase_id?: string;
        freebase_mid?: string;
        tvrage_id?: string;
        fetched: number;
    }
    interface Image {
        aspect_ratio: number;
        file_path: string;
        height: number;
        iso_639_1: string;
        vote_average: number;
        vote_count: number;
        width: number;
        fetched: number;
    }
    interface ShowImages {
        posters: Image[];
        backdrops: Image[];
        fetched: number;
    }

    interface Person {
        credit_id: string;
        gender: number;
        id: number;
        name: string;
        profile_path: string;
    }

    interface Creator extends Person {}
    interface Crew extends Person {
        department: string;
        job: string;
    }

    interface Cast extends Person {
        character: string;
        order: number;
    }

    interface Genre {
        id: number;
        name: string;
    }

    interface Episode {
        air_date: string;
        episode_number: number;
        id: number;
        name: string;
        overview: string;
        production_code: string;
        season_number: number;
        still_path: string;
        vote_average: number;
        vote_count: number;
    }

    interface Company {
        id: number;
        logo_path: string;
        name: string;
        origin_country: string;
    }

    interface Network extends Company {}

    interface Season {
        air_date: string;
        episode_count?: number;
        id: number;
        name: string;
        overview: string;
        poster_path: string;
        season_number: number;
    }

    interface Show {
        backdrop_path: string;
        first_air_date: string;
        genres: Genre[];
        id: number;
        name: string;
        origin_country: string[];
        original_language: string;
        original_name: string;
        overview: string;
        popularity: number;
        poster_path: string;
        vote_average: number;
        vote_count: number;
    }

    interface ShowDetails extends Show {
        created_by: Creator[];
        credits: { cast: Cast[]; crew: Crew[] };
        episode_run_time: number[];
        homepage: string;
        in_production: boolean;
        languages: string[];
        last_air_date: string;
        last_episode_to_air: Episode;
        external_ids: ExternalIds;
        networks: Network[];
        next_episode_to_air: Episode;
        number_of_episodes: number;
        number_of_seasons: number;
        production_companies: Company[];
        seasons: Season[];
        similar: {
            page: number;
            total_pages: number;
            total_results: number;
            results: Show[];
        };
        status: string;
        type: string;
        fetched: number;
    }

    interface EpisodeDetails extends Episode {
        credits: {
            guest_stars: Cast[];
            cast: Cast[];
            crew: Crew[];
        };
        fetched: number;
        external_ids: ExternalIds;
    }
    interface SeasonDetails extends Omit<Season, 'episode_count'> {
        episodes: EpisodeDetails[];
        credits: {
            id: number;
            cast: Cast[];
            crew: Crew[];
        };
        fetched: number;
    }
}
