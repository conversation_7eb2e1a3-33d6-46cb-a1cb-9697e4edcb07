import React from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import { Overlay, Button } from 'react-native-elements';
import i18n from 'i18n-js';
import { SPACING } from 'app/app/styles/sizes';
import Text from '../elements/Text';
import ClearButton from '../elements/ClearButton';

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
    container: {
        padding: SPACING.medium
    },
    overlay: { width: width / 1.5, height: 'auto' },
    bodyContainer: {
        padding: SPACING.medium
    },
    buttons: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: SPACING.medium
    },
    button: {
        flex: 1,
        marginHorizontal: SPACING.small
    }
});

export interface ConfirmProps {
    title: string;
    confirmation: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel: () => void;
    isVisible: boolean;
}

const Confirm = ({ title, confirmation, onCancel, cancelText, onConfirm, confirmText, isVisible }: ConfirmProps) => {
    const confirm = confirmText || i18n.t('OK');
    const cancel = cancelText || i18n.t('CANCEL');
    return (
        <Overlay
            isVisible={isVisible}
            onBackdropPress={onCancel}
            overlayStyle={styles.overlay}
            backdropStyle={styles.container}
        >
            <>
                <View style={styles.bodyContainer}>
                    <Text centered textStyle="title">
                        {title}
                    </Text>
                    <Text centered>{confirmation}</Text>
                </View>
                <View style={styles.buttons}>
                    <Button containerStyle={styles.button} title={confirm} onPress={onConfirm} />
                    <ClearButton containerStyle={[styles.button]} title={cancel} onPress={onCancel} />
                </View>
            </>
        </Overlay>
    );
};

export default Confirm;
