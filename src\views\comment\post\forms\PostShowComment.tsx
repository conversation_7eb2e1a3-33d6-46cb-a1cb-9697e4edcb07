import React, { useState } from 'react';
import i18n from 'i18n-js';
import { Header } from 'react-native-elements';
import Back from 'app/app/components/header/Back';

import ClearButton from 'app/app/components/elements/ClearButton';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Container from 'app/app/components/containers/Container';
import { postComment } from 'app/app/redux/comment/actions';
import { useDispatch } from 'react-redux';
import Form from '../components/Form';

export interface PostShowCommentProps {
    id: number;
    error: string;
    loading: boolean;
}

const PostShowComment = ({ id, loading, error }: PostShowCommentProps) => {
    const dispatch = useDispatch();
    const placeholder = i18n.t('COMMENT');
    const [data, setData] = useState<Trakt.CommentForm>({ comment: '', spoiler: false });
    const onChange = (data: Trakt.CommentForm) => {
        const postData = { ...data };
        postData.show = { ids: { trakt: id } };
        setData(postData);
    };
    const onSubmit = () => {
        dispatch(postComment(data));
    };
    const rightComponent = <ClearButton small title={i18n.t('POST')} onPress={onSubmit} />;

    return (
        <>
            <Header
                {...{
                    leftComponent: <Back />,
                    rightComponent,
                    centerComponent: { text: i18n.t('POST_COMMENT') }
                }}
            />
            <Container keyboardShouldPersistTaps="handled">
                <Form {...{ onChange, placeholder, error }} />
                {loading && <ScreenLoader />}
            </Container>
        </>
    );
};

export default React.memo(PostShowComment);
