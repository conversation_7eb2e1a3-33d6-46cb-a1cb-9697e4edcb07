import React, { useEffect, useState } from 'react';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import { MSState } from 'app/app/redux/redux';
import { useSelector, useDispatch } from 'react-redux';
import { getLists, getListItems } from 'app/app/redux/list/actions';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { useNavigation } from 'react-navigation-hooks';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import {
    syncFavorites,
    getLastActivity,
    SyncAction,
    resetLastActivity,
    GET_LAST_ACTIVITY_ERROR
} from 'app/app/redux/sync/actions';
import ApiHelper from 'app/services/helpers/ApiHelper';
import _ from 'lodash';
import i18n from 'i18n-js';
import { getFavorites } from 'app/app/redux/favorite/actions';
import { getCertifications, getGenres, getNetworks } from 'app/app/redux/meta/actions';
import { View, StyleSheet, Image } from 'react-native';
import cover from 'app/assets/images/show/poster_big_dark.png';
import { signOut } from 'app/app/redux/auth/actions';
import { getCollection } from 'app/app/redux/sync/collection';
import { getWatched } from 'app/app/redux/sync/watch';
import { getMyRatings } from 'app/app/redux/sync/rate';
import styles from './styles';

const Init = () => {
    const dispatch: MS.Dispatch<SyncAction> = useDispatch();
    const [fetchedActivities, setfetchedActivities] = useState<Trakt.LastActivities>(null);
    const { navigate } = useNavigation();
    const {
        isGuest,
        user,
        lists,
        listsFetched,
        listItemsFetched,
        myRatingsFetched,
        watchedFetched,
        collectionFetched,
        lastSynced,
        favorites,
        meta,
        lastActivities
    } = useSelector((state: MSState) => {
        const { isGuest } = state.auth.guest;
        const { user } = state.user.settings;
        const { result: lists, fetched: listsFetched } = state.lists.lists;
        const listItemsFetched = state.lists.listItems.fetched;
        const myRatingsFetched = state.sync.myRatings.fetched;
        const watchedFetched = state.sync.watched.fetched;
        const collectionFetched = state.sync.collection.fetched;
        const { fetched: lastSynced } = state.sync.syncFavorites;
        const { result: favorites } = state.favorites;
        const { meta } = state;
        const { lastActivities } = state.sync.activity;
        return {
            isGuest,
            user,
            lists,
            listsFetched,
            listItemsFetched,
            myRatingsFetched,
            watchedFetched,
            collectionFetched,
            lastSynced,
            favorites,
            meta,
            lastActivities
        };
    }, _.isEqual);

    const init = () => {
        dispatch(getFavorites());
        if (!meta?.networks.result || ApiHelper.shouldFetch(meta.networks.fetched, UpdateThresholds.HIGHEST)) {
            dispatch(getNetworks());
        }
        if (
            !meta?.certifications.result ||
            ApiHelper.shouldFetch(meta.certifications.fetched, UpdateThresholds.HIGHEST)
        ) {
            dispatch(getCertifications());
        }
        if (!meta?.genres.result || ApiHelper.shouldFetch(meta.genres.fetched, UpdateThresholds.HIGHEST)) {
            dispatch(getGenres());
        }

        if (isGuest) {
            navigate(Constants.Navigation.Calendar.VIEW);
        } else {
            const promises = [];
            if (
                !myRatingsFetched ||
                ApiHelper.hasNewActivity(
                    fetchedActivities,
                    lastActivities,
                    ['episodes', 'shows', 'seasons'],
                    'rated_at'
                )
            ) {
                promises.push(dispatch(getMyRatings()));
            }
            if (
                !watchedFetched ||
                ApiHelper.hasNewActivity(fetchedActivities, lastActivities, ['episodes'], 'watched_at')
            ) {
                promises.push(dispatch(getWatched()));
            }
            if (
                user &&
                (!listsFetched || ApiHelper.hasNewActivity(fetchedActivities, lastActivities, ['lists'], 'updated_at'))
            ) {
                promises.push(dispatch(getLists()));
            }
            if (
                user &&
                (!collectionFetched ||
                    ApiHelper.hasNewActivity(fetchedActivities, lastActivities, ['episodes'], 'collected_at'))
            ) {
                promises.push(dispatch(getCollection()));
            }
            if (
                listsFetched &&
                (!listItemsFetched ||
                    ApiHelper.hasNewActivity(fetchedActivities, lastActivities, ['lists'], 'updated_at'))
            ) {
                lists.forEach(id => {
                    const params: RequestParams = {
                        extended: 'full'
                    };
                    promises.push(dispatch(getListItems(id, params)));
                });
            }
            if (favorites.length > 0 && ApiHelper.shouldFetch(lastSynced, UpdateThresholds.LOW)) {
                dispatch(syncFavorites());
            }
            navigate(Constants.Navigation.Calendar.VIEW);
            Promise.all(promises).then(() => {
                dispatch(resetLastActivity(fetchedActivities));
            });
        }
    };

    useEffect(() => {
        if (!isGuest) {
            dispatch(getLastActivity()).then(action => {
                console.log('get last activity', action);
                if (action.type === GET_LAST_ACTIVITY_ERROR) {
                    dispatch(signOut());
                }
                setfetchedActivities(action.payload.lastActivities);
            });
        } else {
            init();
        }
    }, []);

    useEffect(() => {
        if (!isGuest && fetchedActivities) {
            init();
        }
    }, [fetchedActivities]);

    useEffect(() => {
        if (user && !listsFetched) {
            dispatch(getLists());
        }
        if (!user && !isGuest) {
            navigate(Constants.Navigation.Auth.SIGN_IN);
        }
    }, [user]);

    useEffect(() => {
        if (user && !listsFetched) {
            dispatch(getFavorites());
        }
    }, [favorites]);

    useEffect(() => {
        if (user && listsFetched && !listItemsFetched) {
            lists.forEach(id => {
                const params: RequestParams = {
                    extended: 'full'
                };
                dispatch(getListItems(id, params));
            });
        }
    }, [listsFetched]);

    return (
        <View
            style={{
                ...StyleSheet.absoluteFillObject,
                ...styles.imageContainer
            }}
        >
            <Image
                source={cover}
                style={{
                    ...styles.background,
                    ...StyleSheet.absoluteFillObject
                }}
                resizeMode="stretch"
            />
            <ScreenLoader message={i18n.t('SYNCING_DATA')} />
        </View>
    );
};

export default React.memo(Init, _.isEqual);
