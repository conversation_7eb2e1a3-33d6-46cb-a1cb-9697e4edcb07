import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Container from 'app/app/components/containers/Container';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Constants from 'app/app/Constants';
import { MSState } from 'app/app/redux/redux';
import moment from 'moment';
import { refreshToken } from 'app/app/redux/auth/actions';
import { useNavigation } from 'react-navigation-hooks';

const AuthCheck = () => {
    const { navigate } = useNavigation();
    const dispatch = useDispatch();
    const { token, isGuest, scheme } = useSelector((state: MSState) => {
        const { token } = state.auth.trakt;
        const { isGuest } = state.auth.guest;
        const { scheme } = state.app;
        return {
            token,
            isGuest,
            scheme
        };
    });

    useEffect(() => {
        if (!scheme) {
            navigate(Constants.Navigation.Auth.SELECT_SETTINGS);
        } else if (!token && !isGuest) {
            navigate(Constants.Navigation.Auth.SIGN_IN);
        } else if (!isGuest) {
            const created = token.created_at * 1000;
            const expires = moment(created)
                .add(token.expires_in, 'seconds')
                .diff(new Date().getTime(), 'seconds');
            console.log('token expires in', expires);
            if (expires < 3600) {
                dispatch(refreshToken(token.refresh_token)).then(() => {
                    navigate(Constants.Navigation.Auth.INIT);
                });
            } else {
                navigate(Constants.Navigation.Auth.INIT);
            }
        } else {
            navigate(Constants.Navigation.Auth.INIT);
        }
    }, []);    

    return (
        <Container>
            <ScreenLoader />
        </Container>
    );
};

export default AuthCheck;
