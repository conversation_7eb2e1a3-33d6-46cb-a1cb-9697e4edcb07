import React from 'react';
import { withNavigation, NavigationInjectedProps, FlatList } from 'react-navigation';
import { Header } from 'react-native-elements';
import PersonListItem from 'app/app/components/listItems/person/PersonListItem';
import Back from 'app/app/components/header/Back';
import i18n from 'i18n-js';

export interface PeopleNavParams {
    people: TMDB.Cast[] | TMDB.Crew[];
    type: TMDB.PeopleType;
}

export interface PeopleProps {}

type Props = PeopleProps & NavigationInjectedProps<{}>;
const People = ({ navigation }: Props) => {
    const { people, type } = navigation.state.params as PeopleNavParams;
    const renderItem = ({ item }: { item: TMDB.Cast | TMDB.Crew }) => {
        if (type === 'cast') {
            const { character, name, profile_path, gender, id } = item as TMDB.Cast;
            return (
                <PersonListItem
                    {...{
                        id,
                        character,
                        gender,
                        name,
                        profile_path
                    }}
                />
            );
        }
        if (type === 'crew') {
            const { department, job, name, profile_path, gender, id } = item as TMDB.Crew;
            return (
                <PersonListItem
                    {...{
                        id,
                        department,
                        job,
                        gender,
                        name,
                        profile_path
                    }}
                />
            );
        }
        return null;
    };
    return (
        <>
            <Header
                leftComponent={<Back />}
                centerComponent={{
                    text: type === 'cast' ? i18n.t('CAST') : i18n.t('CREW')
                }}
            />
            <FlatList<TMDB.Cast | TMDB.Crew>
                data={people}
                keyExtractor={item => item.credit_id}
                renderItem={renderItem}
            />
        </>
    );
};

export default React.memo(withNavigation(People));
