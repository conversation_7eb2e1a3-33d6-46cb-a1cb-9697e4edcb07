import React from 'react';
import { ScrollView, Dimensions } from 'react-native';
import _ from 'lodash';
import { SPACING } from 'app/app/styles/sizes';
import ShowCardItem from '../listItems/show/ShowCardItem';

const { width: screenWidth } = Dimensions.get('window');
export interface SimilarProps {
    similar: Api.Entity<Trakt.Show>[];
}

const renderItem = (show: Api.Entity<Trakt.Show>) => {
    const {
        ids: { trakt }
    } = show.attributes;
    return (
        <ShowCardItem
            {...{
                trakt,
                key: trakt,
                rounded: false,
                contentContainerStyle: {
                    marginRight: SPACING.large,
                    paddingTop: SPACING.normal
                },
                width: screenWidth / 4
            }}
        />
    );
};

const Similar = ({ similar }: SimilarProps) => {
    return (
        <ScrollView horizontal>
            {similar.map(show => {
                return renderItem(show);
            })}
        </ScrollView>
    );
};

export default React.memo(Similar, _.isEqual);
