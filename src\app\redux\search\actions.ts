import { Action } from 'redux';
import apiClient from 'app/services/api/client';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { normalize } from 'normalizr';
import { TraktShowSchema } from '../schemas';

export const SEARCH_REQUEST = 'SEARCH_REQUEST';
export const SEARCH_SUCCESS = 'SEARCH_SUCCESS';
export const SEARCH_ERROR = 'SEARCH_ERROR';
export const CLEAR_SEARCH_SUCCESS = 'CLEAR_SEARCH_SUCCESS';
export interface SearchAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<Trakt.Show>;
        append: boolean;
        headers: Api.TraktHeaders;
    };
}
export const searchShows = (params: RequestParams = {}) => {
    const append = params?.page > 1;
    return apiClient(
        [
            { type: SEARCH_REQUEST, payload: { append } },
            {
                type: SEARCH_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktShowSchema]);
                        return { normalized, headers, append };
                    });
                }
            },
            SEARCH_ERROR
        ],
        `/search/show/`,
        'GET',
        'trakt',
        params
    );
};

export const clearSearch = () => dispatch => {
    return new Promise(resolve => {
        resolve(
            dispatch({
                type: CLEAR_SEARCH_SUCCESS,
                payload: {}
            })
        );
    });
};
