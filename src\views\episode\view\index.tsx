import React, { useEffect, useContext, useState } from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import { NavParams } from 'app/app/navigation/Navigator';
import AnimatedCover from 'app/app/components/header/AnimatedCover';
import Container from 'app/app/components/containers/Container';
import Animated from 'react-native-reanimated';
import Text from 'app/app/components/elements/Text';
import { useDispatch, useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { useValues, onScrollEvent } from 'react-native-redash';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme, HEADER_HEIGHT } from 'app/app/styles/themes';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import { getEpisodeDetails } from 'app/app/redux/episode/actions';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import People from 'app/app/components/show/People';
import Divider from 'app/app/components/containers/Divider';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import { getRating } from 'app/app/redux/rating/actions';
import IMDB from 'app/app/components/show/IMDB';
import ActionsBar from 'app/app/components/show/actionBar/ActionsBar';
import useWatched from 'app/app/hooks/useWatched';
import EpisodeNavigation from 'app/app/components/show/EpisodeNavigation';
import { getEpisodeComments } from 'app/app/redux/comment/actions';
import { CommentNavParams } from 'app/views/comment/list';
import useGuest from 'app/app/hooks/useGuest';
import Toast from 'react-native-toast-message';
import { defaultOptions } from 'app/app/components/elements/Toast';
import { PostCommentNavParams } from 'app/views/comment/post';
import MainComment from 'app/app/components/show/MainComment';
import MoreStats from 'app/app/components/show/MoreStats';
import Help from 'app/app/components/containers/Help';
import showActionsLight from 'app/assets/images/help/show_actions_light.png';
import showActionsDark from 'app/assets/images/help/show_actions_dark.png';
import HelpHelper from 'app/services/helpers/HelpHelper';
import ShowHelp from 'components/show/actionBar/Help';
import { getShowDetails } from 'app/app/redux/show/actions';

const { width } = Dimensions.get('window');
export interface EpisodeNavParams extends NavParams {
    ids: Trakt.Ids;
    seasonNumber: number;
    episodeNumber: number;
}
const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    title: { paddingTop: SPACING.normal },
    section: {
        padding: SPACING.medium
    },
    subSection: {
        padding: SPACING.medium,
        paddingBottom: 0
    }
});

export interface EpisodeProps {}

type Props = EpisodeProps & NavigationInjectedProps<{}>;
const Episode = ({ navigation }: Props) => {
    const [helpVisible, setHelpVisible] = useState(false);

    const { episodeNumber, ids, seasonNumber } = navigation.state.params as EpisodeNavParams;
    const dispatch = useDispatch();
    const isGuest = useGuest();
    const [y] = useValues([0], []);
    const onScroll = onScrollEvent({ y });
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const { episodeDetails, showDetails, rating, episodeImdb, commentId } = useSelector((state: MSState) => {
        const episodeDetailsId = state.episodes.details?.[ids.tmdb]?.[seasonNumber]?.[episodeNumber]?.result;
        const episodeDetails = state.entities.episodeDetails[episodeDetailsId];
        const showDetails = state.entities.showDetails[ids.tmdb];
        const episodeImdb = episodeDetails?.attributes.external_ids?.imdb_id;
        const rating = episodeImdb && state.entities.rating[episodeImdb];
        const commentId = state.comments.episode[ids.trakt]?.[seasonNumber]?.[episodeNumber]?.main[0];
        return {
            episodeDetails,
            showDetails,
            rating,
            episodeImdb,
            commentId
        };
    });

    const { watched } = useWatched(ids.trakt, seasonNumber, episodeNumber);
    useEffect(() => {
        if (!showDetails) {
            dispatch(getShowDetails(ids.tmdb));
        }
        if (!episodeDetails) {
            dispatch(getEpisodeDetails(ids.tmdb, seasonNumber, episodeNumber));
        }
        if (episodeImdb && (!rating || ApiHelper.shouldFetch(rating?.attributes.fetched, UpdateThresholds.HIGHEST))) {
            dispatch(getRating(episodeImdb));
        }
        dispatch(getEpisodeComments(ids.trakt, seasonNumber, episodeNumber, { limit: 1 }, true));
        HelpHelper.getHelp('show').then(value => {
            setHelpVisible(value);
        });
    }, []);

    useEffect(() => {
        if (episodeImdb && (!rating || ApiHelper.shouldFetch(rating?.attributes.fetched, UpdateThresholds.HIGHEST))) {
            dispatch(getRating(episodeImdb));
        }
    }, [episodeImdb]);

    if (!episodeDetails || !showDetails) {
        return <ScreenLoader />;
    }

    const {
        attributes: {
            still_path,
            name,
            overview,
            credits: { cast, crew, guest_stars }
        }
    } = episodeDetails;
    const {
        attributes: { name: showName }
    } = showDetails;
    const backdrop = ShowHelper.getStillUri(still_path, 100, scheme);
    const imageHeight = width / 1.3;
    const subtitle = `${showName} ${seasonNumber}x${episodeNumber}`;
    const {
        attributes: { rating: rate, votes, id }
    } = rating || { attributes: {} };
    const bar = (
        <ActionsBar
            {...{
                type: 'episodes',
                ids,
                season: seasonNumber,
                episode: episodeNumber,
                episodeImdbId: id,
                imageHeight,
                isWatched: watched,
                totalEpisodes: 1
            }}
        />
    );

    const onMoreCommentsPress = () => {
        const params: CommentNavParams = { id: ids.trakt, seasonNumber, episodeNumber, type: 'episode' };
        navigation.navigate(Constants.Navigation.Comment.LIST, params);
    };
    const onPostComment = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('POST_COMMENT_WARNING')
            });
        } else {
            const params: PostCommentNavParams = {
                type: 'episode',
                id: ids.trakt,
                seasonNumber,
                episodeNumber
            };
            // @ts-ignore
            navigation.push(Constants.Navigation.Comment.POST, params);
        }
    };

    return (
        <>
            <AnimatedCover title={name} src={backdrop} y={y} height={imageHeight} bar={bar} />
            <Container fullPage useScrollView={false}>
                <Animated.ScrollView
                    scrollEventThrottle={16}
                    {...{ onScroll }}
                    contentContainerStyle={{
                        backgroundColor: colors.background,
                        paddingTop: imageHeight - HEADER_HEIGHT
                    }}
                >
                    <EpisodeNavigation {...{ ids, season: seasonNumber, episode: episodeNumber }} />
                    <View style={{ ...styles.section, ...styles.title }}>
                        <Text textStyle="doubleTitle">{name}</Text>
                        <Text color={colors.discreet}>{subtitle}</Text>
                        <Divider size={SPACING.normal} />
                        <IMDB {...{ rate, votes, imdbId: id }} />
                        <MoreStats
                            {...{ id: ids.trakt, seasonNumber, episodeNumber, title: `${subtitle} - ${name}` }}
                        />
                    </View>

                    <View style={{ ...styles.section }}>
                        <Text>{overview}</Text>
                    </View>
                    <Divider border />
                    <Divider size={SPACING.medium} />
                    <MainComment {...{ id: commentId, onMoreCommentsPress, onPostComment }} />
                    <View>
                        <Text style={{ ...styles.subSection }} textStyle="title">
                            {i18n.t('CAST')}
                        </Text>
                    </View>
                    <People {...{ people: cast, display: 'list', limit: 10 }} />
                    {guest_stars?.length > 0 && (
                        <>
                            <View>
                                <Text style={{ ...styles.subSection }} textStyle="title">
                                    {i18n.t('GUEST_STARS')}
                                </Text>
                            </View>
                            <People
                                {...{
                                    people: guest_stars,
                                    display: 'list',
                                    limit: 5
                                }}
                            />
                        </>
                    )}
                    {crew?.length > 0 && (
                        <>
                            <View>
                                <Text style={{ ...styles.subSection }} textStyle="title">
                                    {i18n.t('CREW')}
                                </Text>
                            </View>
                            <People
                                {...{
                                    people: crew,
                                    type: 'crew',
                                    display: 'list',
                                    limit: 3
                                }}
                            />
                        </>
                    )}
                </Animated.ScrollView>
            </Container>
            <Help
                {...{
                    title: i18n.t('SHOW_HELP_TITLE'),
                    isVisible: helpVisible,
                    image: scheme === 'dark' ? showActionsDark : showActionsLight,
                    imageRatio: 48 / 412,
                    textElement: <ShowHelp />
                }}
            />
        </>
    );
};

export default React.memo(withNavigation(Episode));
