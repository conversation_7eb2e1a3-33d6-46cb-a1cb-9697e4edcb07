import React, { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import getEntities from 'app/app/redux/entities/selectors';
import { Header } from 'react-native-elements';
import _ from 'lodash';
import ArrayHelper from 'app/services/helpers/ArrayHelper';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { TabView, Route } from 'react-native-tab-view';
import TabBar from 'app/app/components/containers/TabBar';
import moment from 'moment';
import i18n from 'i18n-js';
import RefreshControl from 'app/app/components/refresh/RefreshControl';
import { syncFavorites } from 'app/app/redux/sync/actions';
import ApiHelper from 'app/services/helpers/ApiHelper';
import { UpdateThresholds } from 'app/app/Constants';
import useGuest from 'app/app/hooks/useGuest';
import Banner from 'app/app/components/containers/Banner';
import WatchNow from './components/WatchNow';
import Upcoming from './components/Upcoming';

export interface CalendarProps {}

const watchNow = (data: MS.NextEpisode[], refreshControl: JSX.Element) => {
    return <WatchNow {...{ data, refreshControl }} />;
};

const upcoming = (data: MS.NextEpisode[], refreshControl: JSX.Element) => {
    return <Upcoming {...{ data, refreshControl }} />;
};
const initialLayout = { width: Dimensions.get('window').width };
const Calendar = () => {
    const [index, setIndex] = useState(0);
    const isGuest = useGuest();
    const [routes] = useState<Route[]>([{ key: 'watchNow' }, { key: 'upcoming' }]);
    const dispatch = useDispatch();
    const {
        nextEpisodes,
        refreshing,
        lastSynced,
        hasFavorites
    }: {
        nextEpisodes: MS.NextEpisode[];
        refreshing: boolean;
        lastSynced: number;
        hasFavorites: boolean;
    } = useSelector((state: MSState) => {
        const next = state.sync.nextEpisode;
        const calendarIds = state.sync.calendar.result;
        const ids = Object.keys(next)
            .map(key => {
                return next[key].episode;
            })
            .filter(r => !!r);
        const allIds = _.uniq([...ids, ...calendarIds]);

        const simpleNext = getEntities(state, allIds, 'nextEpisode') as Api.Entity<Trakt.NextEpisode>[];

        const hasFavorites = state.favorites?.result.length > 0;
        const nextEpisodes = simpleNext.map(next => {
            const tmdb = next.id;
            const episodeDetails = state.entities.episodeDetails[tmdb] as Api.Entity<TMDB.EpisodeDetails>;
            return {
                showTitle: next.attributes.show?.title,
                sortTitle: ShowHelper.getSortTitle(next.attributes.show?.title),
                airedDate: episodeDetails?.attributes.air_date,
                season: next.attributes.season,
                episode: next.attributes.number,
                episodeIds: next.attributes.ids,
                showIds: next.attributes.show.ids,
                episodeTitle: episodeDetails?.attributes.name
            } as MS.NextEpisode;
        });
        const { loading: refreshing, fetched: lastSynced } = state.sync.syncFavorites;
        return {
            nextEpisodes,
            refreshing,
            lastSynced,
            hasFavorites
        };
    }, _.isEqual);

    useEffect(() => {
        if (!isGuest && ApiHelper.shouldFetch(lastSynced, UpdateThresholds.LOW)) {
            dispatch(syncFavorites());
        }
    }, []);
    useEffect(() => {
        if (!isGuest && hasFavorites) {
            dispatch(syncFavorites());
        }
    }, [hasFavorites]);

    const sorted = ArrayHelper.sortArray(nextEpisodes, episode => {
        return episode.sortTitle;
    });
    const today = moment().format('YYYY-MM-DD');
    const toWatch = sorted.filter(episode => moment(episode.airedDate).isBefore(today));
    const future = sorted.filter(episode => !moment(episode.airedDate).isBefore(today));
    const onRefresh = () => dispatch(syncFavorites());
    const refreshControl = <RefreshControl {...{ refreshing, onRefresh }} />;
    const renderScene = ({ route: { key } }) => {
        switch (key) {
            case 'watchNow':
                return watchNow(toWatch, refreshControl);
            case 'upcoming':
                return upcoming(future, refreshControl);
            default:
                return null;
        }
    };
    return (
        <>
            <Header centerComponent={{ text: i18n.t('CALENDAR') }} />
            <TabView
                navigationState={{ index, routes }}
                renderScene={renderScene}
                onIndexChange={setIndex}
                initialLayout={initialLayout}
                renderTabBar={props => <TabBar {...props} />}
            />
            <Banner />
        </>
    );
};

export default React.memo(Calendar, _.isEqual);
