import React from 'react';
import { ThemeConsumer } from 'react-native-elements';
import { BottomTabBar as RNBottomTabBar, BottomTabBarProps } from 'react-navigation-tabs';
import { Theme, alpha } from 'styles/themes';
import { StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import { NavigationRoute } from 'react-navigation';
import _ from 'lodash';
import { SPACING } from '../styles/sizes';
import { MSState } from '../redux/redux';

const styles = StyleSheet.create({
    container: {
        height: SPACING.large * 2,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: SPACING.normal
    }
});
const BottomTabBar = (props: BottomTabBarProps) => {
    const isGuest = useSelector((state: MSState) => state.auth.guest.isGuest);
    const getButtonComponent = ({ route }: { route: NavigationRoute }) => {
        if (isGuest && route.routeName === 'Lists') {
            return () => null;
        }
        return props.getButtonComponent({ route });
    };
    return (
        <ThemeConsumer<Theme>>
            {({ theme }) => {
                const { colors, scheme } = theme;

                return (
                    <RNBottomTabBar
                        {...props}
                        getButtonComponent={getButtonComponent}
                        activeTintColor={scheme === 'dark' ? colors.primary : colors.primary}
                        inactiveTintColor={
                            scheme === 'dark' ? alpha(colors.onSurface, 0.5) : alpha(colors.onSurface, 0.5)
                        }
                        style={[
                            styles.container,
                            {
                                backgroundColor: colors.surface
                            }
                        ]}
                    />
                );
            }}
        </ThemeConsumer>
    );
};
export default React.memo(BottomTabBar, _.isEqual);
