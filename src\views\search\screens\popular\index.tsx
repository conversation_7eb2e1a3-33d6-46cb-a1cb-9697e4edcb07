import React, { useEffect } from 'react';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { popularShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import Back from 'app/app/components/header/Back';
import { FlatList } from 'react-native-gesture-handler';
import ApiHelper from 'app/services/helpers/ApiHelper';
import MoreLoader from 'app/app/components/loaders/MoreLoader';
import EmptyResults from 'app/app/components/containers/EmptyResults';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import SmallRating from 'app/app/components/show/SmallRating';
import _ from 'lodash';

const Popular = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        if (ShowListItem.length === 0) {
            const params: RequestParams = {
                extended: 'full'
            };
            dispatch(popularShows(params));
        }
    }, []);

    const { result, headers, loading, loadingMore } = useSelector((state: MSState) => {
        return {
            result: state.shows.popular.result,
            headers: state.shows.popular.headers,
            loading: state.shows.popular.loading,
            loadingMore: state.shows.popular.loadingMore
        };
    }, _.isEqual);

    const renderItem = ({ item: trakt }: { item: number }) => {
        return (
            <ShowListItem
                {...{
                    key: trakt,
                    trakt,
                    rightElement: <SmallRating {...{ trakt }} />
                }}
            />
        );
    };
    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (!loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                extended: 'full',
                page: pagination.currentPage + 1
            };
            dispatch(popularShows(params));
        }
    };

    return (
        <>
            <Header centerComponent={{ text: i18n.t('POPULAR_SHOWS') }} leftComponent={<Back />} />
            <FlatList<number>
                data={result}
                renderItem={renderItem}
                keyExtractor={item => item.toString()}
                onEndReached={loadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={loadingMore && <MoreLoader />}
                ListEmptyComponent={<EmptyResults text={i18n.t('NO_SHOWS_FOUND')} />}
            />
            {loading && result.length === 0 && <ScreenLoader />}
        </>
    );
};

export default React.memo(Popular, _.isEqual);
