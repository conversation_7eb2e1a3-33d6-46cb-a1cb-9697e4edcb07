import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import i18n from 'i18n-js';
import { SPACING } from 'app/app/styles/sizes';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import ShowMore from '../elements/ShowMore';
import CommentListItem from '../listItems/comment/CommentListItem';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    horizontal: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: SPACING.medium
    },
    section: {
        padding: SPACING.medium
    },
    postCommentContainer: {
        paddingLeft: SPACING.medium
    }
});

export interface MainCommentProps {
    id: number;
    onPostComment: () => void;
    onMoreCommentsPress: () => void;
}

const MainComment = ({ id, onPostComment, onMoreCommentsPress }: MainCommentProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    return (
        <View>
            <View style={{ ...styles.horizontal }}>
                <Text textStyle="title">{i18n.t('COMMENTS')}</Text>
                {!!id && (
                    <ShowMore
                        {...{
                            title: i18n.t('MORE_COMMENTS'),
                            onPress: onMoreCommentsPress
                        }}
                    />
                )}
            </View>
            {id ? <CommentListItem {...{ id }} /> : <Text style={{ ...styles.section }}>{i18n.t('NO_COMMENTS')}</Text>}
            <View style={{ ...styles.horizontal, ...styles.postCommentContainer }}>
                <TouchableOpacity onPress={onPostComment}>
                    <Text color={colors.primary} bold>
                        {i18n.t('POST_COMMENT')}
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default React.memo(MainComment);
