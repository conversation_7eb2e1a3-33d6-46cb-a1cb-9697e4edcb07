import React from 'react';
import { StyleSheet, View } from 'react-native';
import { SocialIcon, SocialMediaType, ThemeConsumer } from 'react-native-elements';
import { SPACING, ICON_SIZE } from 'app/app/styles/sizes';
import { Theme, alpha } from 'styles/themes';

const styles = StyleSheet.create({
    container: { flexDirection: 'row', marginTop: SPACING.small }
});

export interface ConnectionsProps {
    connections: Trakt.Connections;
}

const Connections = ({ connections }: ConnectionsProps) => {
    return (
        <ThemeConsumer<Theme>>
            {({ theme: { colors } }) => {
                return (
                    <View style={{ ...styles.container }}>
                        {Object.keys(connections).map(connection => {
                            const type = connection as SocialMediaType;
                            const connected = connections[connection];
                            return (
                                <SocialIcon
                                    key={connection}
                                    raised
                                    style={[
                                        {
                                            width: ICON_SIZE.normal,
                                            height: ICON_SIZE.normal
                                        },
                                        connected
                                            ? {}
                                            : {
                                                  backgroundColor: colors.disabled
                                              }
                                    ]}
                                    iconColor={connected ? colors.onBackground : alpha(colors.surface, 0.3)}
                                    iconSize={ICON_SIZE.small * 0.8}
                                    type={type}
                                />
                            );
                        })}
                    </View>
                );
            }}
        </ThemeConsumer>
    );
};

export default React.memo(Connections);
