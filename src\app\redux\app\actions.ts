import { AnyAction } from 'redux';
import { ColorSchemeName } from 'react-native';

export const APP_STARTED = 'APP_STARTED';
export const RESET_STORE = 'RESET_STORE';
export const SET_LOCALE = 'SET_LOCALE';
export const SET_SCHEME = 'SET_SCHEME';
export const UNATHORIZED = 'UNAUTHORIZED';
export const SET_MARK_EPISODE_VIEWED = 'SET_MARK_EPISODE_VIEWED';

export interface AppAction extends AnyAction {
    payload: {
        appStarted: boolean;
        locale: MS.Locales;
        scheme: ColorSchemeName;
        markEpisodeViewed: boolean;
    };
}

export const appStarted = () => dispatch => {
    return new Promise(resolve => {
        resolve(dispatch({ type: APP_STARTED, payload: { appStarted: true } }));
    });
};

export const resetStore = () => (dispatch): Promise<AppAction> => {
    return new Promise(resolve => resolve(dispatch({ type: RESET_STORE })));
};

export const setLocale = (locale: MS.Locales) => dispatch => {
    return new Promise(resolve => {
        resolve(dispatch({ type: SET_LOCALE, payload: { locale } }));
    });
};

export const setScheme = (scheme: ColorSchemeName) => dispatch => {
    return new Promise(resolve => {
        resolve(dispatch({ type: SET_SCHEME, payload: { scheme } }));
    });
};

export const setMarkEpisodeViewed = (markEpisodeViewed: boolean) => dispatch => {
    return new Promise(resolve => {
        resolve(dispatch({ type: SET_MARK_EPISODE_VIEWED, payload: { markEpisodeViewed } }));
    });
};
