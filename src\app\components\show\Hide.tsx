import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Icon, IconProps } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { useDispatch } from 'react-redux';
import { hideShow } from 'app/app/redux/show/actions';
import _ from 'lodash';

export interface HideProps {
    id: number;
    iconProps?: IconProps;
}

const Hide = ({ id, iconProps }: HideProps) => {
    const dispatch = useDispatch();
    const onRemove = () => {
        dispatch(hideShow(id));
    };
    const icon = {
        name: 'trash-o',
        type: 'font-awesome',
        size: ICON_SIZE.small,
        onPress: onRemove,
        ...iconProps
    };
    return <Icon Component={TouchableOpacity} {...icon} />;
};

export default React.memo(Hide, _.isEqual);
