import moment from 'moment';
import { Threshold } from './ApiHelper';

export default class DateHelper {
    static format(date: moment.MomentInput, locale: MS.Locales, format: string = 'DD MMM YYYY'): string {
        moment.locale(locale);
        return !moment(date).isValid() ? '-' : moment(date).format(format);
    }

    static getMonthName(month: number): string {
        return moment.months()[month - 1];
    }

    static isOlder(date: moment.MomentInput, threshold: Threshold): boolean {
        const now = moment.utc(new Date()).format();
        return moment(date)
            .add(threshold.amount, threshold.unit)
            .isBefore(now);
    }

    static convertToDays(minutes: number): { days: number; hours: number; mins: number } {
        const daysDec = moment.duration(minutes, 'minutes').asDays();
        const days = Math.floor(daysDec);
        const hoursDec = moment.duration(daysDec - days, 'days').asHours();
        const hours = Math.floor(hoursDec);
        const mins = Math.round(moment.duration(hoursDec - hours, 'hours').asMinutes());
        return { days, hours, mins };
    }
}
