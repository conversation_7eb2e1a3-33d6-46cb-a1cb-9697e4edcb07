import { useState, useEffect } from 'react';
import Database from 'app/services/storage/DB';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { MSState } from '../redux/redux';

const useRating = (type: Trakt.RateType, show: number, season: number, episode: number) => {
    const [rating, setRating] = useState(0);
    const updated = useSelector<MSState, number>(state => state.meta.dbUpdates.ratings);

    const calculate = () => {
        let query = 'SELECT * FROM ratings WHERE show=?';
        const values = [show];
        if (type === 'seasons') {
            query += ' AND season =? AND episode =-1';
            values.push(season);
        } else if (type === 'episodes') {
            query += ' AND season=? AND episode=?';
            values.push(season);
            values.push(episode);
        } else if (type === 'shows') {
            query += ' AND season =-1 AND episode =-1';
        }
        Database.query(query, values).then(r => {
            if (_.isArray(r) && r.length > 0) {
                setRating(r[r.length - 1].rating);
            }
        });
    };

    useEffect(() => {
        calculate();
    }, []);

    useEffect(() => {
        calculate();
    }, [updated]);

    return rating;
};
export default useRating;
