import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { Action } from 'redux';
import { TmdbExternalIdsSchema } from '../schemas';

export const GET_PERSON_EX_IDS_REQUEST = 'GET_PERSON_EX_IDS_REQUEST';
export const GET_PERSON_EX_IDS_SUCCESS = 'GET_PERSON_EX_IDS_SUCCESS';
export const GET_PERSON_EX_IDS_ERROR = 'GET_PERSON_EX_IDS_ERROR';

export interface PersonAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<TMDB.ExternalIds>;
    };
}

export const getPersonExternalIds = (id: number) => {
    return apiClient(
        [
            GET_PERSON_EX_IDS_REQUEST,
            {
                type: GET_PERSON_EX_IDS_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        const normalized = normalize(json, TmdbExternalIdsSchema);
                        return { normalized };
                    });
                }
            },
            GET_PERSON_EX_IDS_ERROR
        ],
        `/person/${id}/external_ids`,
        'GET',
        'tmdb'
    );
};
