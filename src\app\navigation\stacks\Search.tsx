import Constants from 'app/app/Constants';
import Search from 'app/views/search/view';
import { createStackNavigator } from 'react-navigation-stack';
import { dark, light } from 'app/app/styles/themes';
import Trending from 'app/views/search/screens/trending';
import Popular from 'app/views/search/screens/popular';
import Recommended from 'app/views/search/screens/recommended';
import Watched from 'app/views/search/screens/watched';
import Anticipated from 'app/views/search/screens/anticipated';
import Common from './Common';
import TransitionConfiguration from '../transitions';

export default createStackNavigator(
    {
        [Constants.Navigation.Search.VIEW]: Search,
        [Constants.Navigation.Search.TRENDING]: Trending,
        [Constants.Navigation.Search.POPULAR]: Popular,
        [Constants.Navigation.Search.RECOMMENDED]: Recommended,
        [Constants.Navigation.Search.WATCHED]: Watched,
        [Constants.Navigation.Search.ANTICIPATED]: Anticipated,
        ...Common
    },
    {
        headerMode: 'none',
        defaultNavigationOptions: ({ theme }) => {
            const colors = theme === 'dark' ? dark : light;
            return { headerStyle: { backgroundColor: colors.background } };
        },
        transitionConfig: TransitionConfiguration
    }
);
