import React from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import empty from 'app/assets/images/help/favorite.png';
import Image from 'app/app/components/elements/Image';
import i18n from 'i18n-js';
import Text from 'app/app/components/elements/Text';
import { SPACING } from 'app/app/styles/sizes';

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
    container: {
        padding: SPACING.medium
    }
});
export interface EmptyWatchingProps {
    type: 'watchlist' | 'watching';
}
const EmptyWatching = ({ type }: EmptyWatchingProps) => {
    const text = type === 'watching' ? i18n.t('ADD_TO_FAV_HELP') : i18n.t('ADD_TO_FAV_HELP_WATCHLIST');
    return (
        <View>
            <Text style={{ ...styles.container }}>{text}</Text>
            <Image {...{ uri: empty, width, height: (410 / 820) * width, border: false }} />
        </View>
    );
};

EmptyWatching.defaultProps = {
    type: 'watching'
};

export default React.memo(EmptyWatching);
