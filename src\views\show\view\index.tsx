import React, { useEffect, useContext, useState } from 'react';
import { StyleSheet, Dimensions, View } from 'react-native';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import Container from 'app/app/components/containers/Container';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { getShowDetails, similarShows, ShowAction } from 'app/app/redux/show/actions';
import Text from 'app/app/components/elements/Text';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import AnimatedCover from 'app/app/components/header/AnimatedCover';
import ShowHelper from 'app/services/helpers/ShowHelper';
import Animated from 'react-native-reanimated';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { onScrollEvent, useValues } from 'react-native-redash';
import { Theme, HEADER_HEIGHT } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import Divider from 'app/app/components/containers/Divider';
import _ from 'lodash';
import Networks from 'app/app/components/show/Networks';
import Genres from 'app/app/components/show/Genres';
import Tag from 'app/app/components/containers/Tag';
import i18n from 'i18n-js';
import People from 'app/app/components/show/People';
import Seasons from 'app/app/components/show/Seasons';
import Similar from 'app/app/components/show/Similar';
import getEntities from 'app/app/redux/entities/selectors';
import { getRating } from 'app/app/redux/rating/actions';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import IMDB from 'app/app/components/show/IMDB';
import ActionsBar from 'app/app/components/show/actionBar/ActionsBar';
import Watching from 'app/app/components/show/Favorite';
import { ListItemsState } from 'app/app/redux/list/reducers';
import { getShowProgress } from 'app/app/redux/sync/watch';
import AverageRating from 'app/app/components/show/AverageRating';
import Trakt from 'app/app/components/show/Trakt';
import RefreshControl from 'app/app/components/refresh/RefreshControl';
import { getShowComments } from 'app/app/redux/comment/actions';
import { CommentNavParams } from 'app/views/comment/list';
import { PostCommentNavParams } from 'app/views/comment/post/index';
import MainComment from 'app/app/components/show/MainComment';
import Toast from 'react-native-toast-message';
import useGuest from 'app/app/hooks/useGuest';
import { defaultOptions } from 'app/app/components/elements/Toast';
import MoreStats from 'app/app/components/show/MoreStats';
import Help from 'app/app/components/containers/Help';
import showActionsLight from 'app/assets/images/help/show_actions_light.png';
import showActionsDark from 'app/assets/images/help/show_actions_dark.png';
import HelpHelper from 'app/services/helpers/HelpHelper';
import ShowHelp from 'components/show/actionBar/Help';

const styles = StyleSheet.create({
    horizontal: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    section: {
        padding: SPACING.medium
    },
    subSection: {
        padding: SPACING.medium,
        paddingBottom: 0
    },
    yearStatus: { justifyContent: 'center', alignItems: 'center', flexDirection: 'row' },
    row: {
        paddingTop: SPACING.medium
    },
    postCommentContainer: {
        paddingLeft: SPACING.medium
    },
    status: {
        marginLeft: SPACING.medium
    }
});
const { width } = Dimensions.get('window');
export interface ShowViewNavParams {
    ids: Trakt.Ids;
}

export interface ShowProps {}

type Props = ShowProps & NavigationInjectedProps<ShowViewNavParams>;
const Show = ({ navigation }: Props) => {
    const [refreshing, setRefreshing] = useState(false);
    const [helpVisible, setHelpVisible] = useState(false);

    const { ids } = navigation.state.params;
    const {
        show,
        showDetails,
        similar,
        rating,
        listItems,
        progressFetched,
        commentId,
        similarFetched
    }: {
        show: Api.Entity<Trakt.Show, {}>;
        showDetails: Api.Entity<TMDB.ShowDetails, {}>;
        similar: Api.Entity<Trakt.Show>[];
        rating: Api.Entity<OMDB.Rating>;
        images: Api.Entity<TMDB.ShowImages, {}>;
        listItems: ListItemsState;
        progressFetched: number;
        commentId: number;
        similarFetched: number;
    } = useSelector((state: MSState) => {
        const similarIds = state.shows.similar[ids.trakt]?.result;
        const commentId = state.comments.show[ids.trakt]?.main[0];
        return {
            show: state.entities.show[ids.trakt] as Api.Entity<Trakt.Show>,
            showDetails: state.entities.showDetails[ids.tmdb] as Api.Entity<TMDB.ShowDetails>,
            similar: getEntities(state, similarIds, 'show') as Api.Entity<Trakt.Show>[],
            rating: state.entities.rating[ids.imdb] as Api.Entity<OMDB.Rating>,
            images: state.entities.image[ids.tmdb] as Api.Entity<TMDB.ShowImages, {}>,
            listItems: state.lists.listItems as ListItemsState,
            progressFetched: state.sync.showProgress[ids.trakt]?.fetched,
            simmilarFetched: state.shows.similar[ids.trakt]?.fetched,
            commentId
        };
    }, _.isEqual);
    const isGuest = useGuest();

    const dispatch: MS.Dispatch<ShowAction> = useDispatch();
    const [y] = useValues([0], []);
    const onScroll = onScrollEvent({ y });
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    useEffect(() => {
        if (
            !showDetails ||
            (ids.tmdb && ApiHelper.shouldFetch(showDetails.attributes.fetched, UpdateThresholds.HIGH))
        ) {
            dispatch(getShowDetails(ids.tmdb));
        }
        if (ids.imdb && (!rating || ApiHelper.shouldFetch(rating.attributes.fetched, UpdateThresholds.HIGHEST))) {
            dispatch(getRating(ids.imdb));
        }
        if (ApiHelper.shouldFetch(progressFetched, UpdateThresholds.HIGH)) {
            dispatch(getShowProgress(ids.trakt));
        }
        if (similar.length === 0 || ApiHelper.shouldFetch(similarFetched, UpdateThresholds.HIGHEST)) {
            dispatch(similarShows(ids.trakt, { extended: 'full', limit: 7 }));
        }
        dispatch(getShowComments(ids.trakt, { limit: 1 }, true));
        HelpHelper.getHelp('show').then(value => {
            setHelpVisible(value);
        });
    }, []);

    if (!show) {
        return <ScreenLoader />;
    }
    const onRefresh = () => {
        setRefreshing(true);
        dispatch(getShowDetails(ids.tmdb)).then(() => {
            setRefreshing(false);
        });
    };
    const {
        year,
        status,
        certification,
        title,
        network,
        runtime,
        genres: traktGenres,
        overview: traktOverView
    } = show.attributes;
    const {
        backdrop_path,
        name,
        overview,
        networks,
        genres,
        created_by,
        credits: { cast },
        seasons
    } = showDetails?.attributes || { credits: {} };

    const backdrop = ShowHelper.getBackdropUri(backdrop_path, 100, scheme);
    const imageHeight = ((width * 440) / 780) * 1.5;
    const [statusLabel, color] = ShowHelper.getStatus(status);
    const {
        attributes: { rating: rate, votes }
    } = rating || { attributes: {} };
    const listed = ShowHelper.getListed(listItems, ids.trakt);
    const totalEpisodes = ShowHelper.getTotalEpisodes(seasons);
    const bar = <ActionsBar {...{ ids, type: 'shows', listed: listed.length, imageHeight, totalEpisodes }} />;
    const watching = <Watching ids={ids} />;
    const refreshControl = <RefreshControl {...{ refreshing, onRefresh }} />;
    const onMoreCommentsPress = () => {
        const params: CommentNavParams = { id: ids.trakt, type: 'show' };
        navigation.navigate(Constants.Navigation.Comment.LIST, params);
    };
    const onPostComment = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('POST_COMMENT_WARNING')
            });
        } else {
            const params: PostCommentNavParams = {
                type: 'show',
                id: ids.trakt
            };
            // @ts-ignore
            navigation.push(Constants.Navigation.Comment.POST, params);
        }
    };

    const traktL = traktOverView?.length || 0;
    const ovL = overview?.length || 0;
    const showOverview = traktL > ovL ? traktOverView : overview;

    return (
        <>
            <AnimatedCover
                title={name || title}
                src={backdrop}
                y={y}
                height={imageHeight}
                bar={bar}
                watching={watching}
            />
            <Container fullPage useScrollView={false}>
                <Animated.ScrollView
                    refreshControl={refreshControl}
                    scrollEventThrottle={16}
                    {...{ onScroll }}
                    contentContainerStyle={{
                        backgroundColor: colors.background,
                        paddingTop: imageHeight - HEADER_HEIGHT
                    }}
                >
                    <View style={{ ...styles.section }}>
                        <View style={styles.horizontal}>
                            <Text textStyle="doubleTitle">{name || title}</Text>
                        </View>
                        <View style={{ ...styles.horizontal }}>
                            <View style={{ ...styles.yearStatus }}>
                                <Text>{year}</Text>
                                <Text style={{ ...styles.status }} color={color}>
                                    {statusLabel}
                                </Text>
                            </View>
                            {certification && (
                                <Tag
                                    {...{
                                        label: certification,
                                        textStyle: 'nano'
                                    }}
                                />
                            )}
                        </View>
                        <Divider size={SPACING.medium} />
                        <View style={{ ...styles.horizontal }}>
                            <IMDB {...{ rate, votes, imdbId: ids.imdb }} />
                            <Trakt {...{ rate: show.attributes.rating, votes: show.attributes.votes }} />
                        </View>
                        <AverageRating {...{ type: 'shows', show: ids.trakt, centeredIcon: true }} />
                        <MoreStats {...{ id: ids.trakt, title: name || title }} />
                        <Divider size={SPACING.medium} border />
                        <Networks {...{ networks, network }} />
                        <Genres {...{ genres, traktGenres }} />
                        <View>
                            <Text style={{ ...styles.row }}>{`${i18n.t('RUNTIME')} ${runtime}'`}</Text>
                        </View>
                    </View>
                    <Divider border />
                    <View style={{ ...styles.section }}>
                        <Text>{showOverview}</Text>
                        {created_by && (
                            <Text style={{ ...styles.row }} textStyle="small">{`${i18n.t(
                                'CREATED_BY'
                            )}: ${created_by.map(c => c.name).join(', ')}`}</Text>
                        )}
                    </View>
                    <Divider border />
                    <MainComment {...{ id: commentId, onMoreCommentsPress, onPostComment }} />
                    <View>
                        <Text style={{ ...styles.subSection }} textStyle="title">
                            {i18n.t('CAST')}
                        </Text>
                        <People {...{ people: cast }} />
                    </View>
                    <View style={{ ...styles.section }}>
                        <Text textStyle="title">{i18n.t('SEASONS')}</Text>
                        <Seasons {...{ seasons, ids }} />
                    </View>
                    <View style={{ ...styles.section }}>
                        <Text textStyle="title">{i18n.t('SIMILAR_SHOWS')}</Text>
                        <Similar {...{ similar }} />
                    </View>
                </Animated.ScrollView>
            </Container>
            <Help
                {...{
                    title: i18n.t('SHOW_HELP_TITLE'),
                    isVisible: helpVisible,
                    image: scheme === 'dark' ? showActionsDark : showActionsLight,
                    imageRatio: 48 / 412,
                    textElement: <ShowHelp />
                }}
            />
        </>
    );
};

export default withNavigation(React.memo(Show, _.isEqual));
