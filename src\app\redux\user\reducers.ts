import { combineReducers } from 'redux';
import _ from 'lodash';
import { SIGN_OUT_SUCCESS, REFRESH_TOKEN_REQUEST } from '../auth/actions';
import {
    UserAction,
    GET_USER_SETTINGS_REQUEST,
    GET_USER_SETTINGS_SUCCESS,
    GET_USER_SETTINGS_ERROR,
    GET_USER_STATS_REQUEST,
    GET_USER_STATS_SUCCESS,
    GET_USER_STATS_ERROR,
    GET_LATEST_RATINGS_REQUEST,
    GET_LATEST_RATINGS_SUCCESS,
    GET_LATEST_RATINGS_ERROR
} from './actions';
import { RESET_STORE } from '../app/actions';

export const initialSettingsState: Trakt.UserSettings = {
    user: null,
    account: null,
    connections: null,
    sharing_text: null
};

export function settings(state: Trakt.UserSettings = initialSettingsState, action: UserAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
            return initialSettingsState;
        case GET_USER_SETTINGS_REQUEST:
        case REFRESH_TOKEN_REQUEST: {
            return state;
        }
        case GET_USER_SETTINGS_SUCCESS: {
            return { ...action.payload.settings };
        }
        case GET_USER_SETTINGS_ERROR: {
            return state;
        }
        default:
            return state;
    }
}

export const initialStatsState: Trakt.UserStats = null;

export function stats(state: Trakt.UserStats = initialStatsState, action: UserAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialStatsState;
        case GET_USER_STATS_REQUEST: {
            return state;
        }
        case GET_USER_STATS_SUCCESS: {
            return { ...action.payload.stats };
        }
        case GET_USER_STATS_ERROR: {
            return state;
        }
        default:
            return state;
    }
}

export interface LatestRatings {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
}

export const initialLatestRatings: LatestRatings = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null
};

export function latestRatings(state: LatestRatings = initialLatestRatings, action: UserAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialLatestRatings;
        case GET_LATEST_RATINGS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }
        case GET_LATEST_RATINGS_SUCCESS: {
            const { normalized, headers, append } = action.payload;
            const { result } = normalized;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, result),
                    headers,
                    loading: false,
                    loadingMore: false
                };
            }
            return {
                ...state,
                result,
                headers,
                loading: false
            };
        }
        case GET_LATEST_RATINGS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

export interface UserState {
    latestRatings: LatestRatings;
    settings: Trakt.UserSettings;
    stats: Trakt.UserStats;
}

export const initialUserState: UserState = {
    latestRatings: initialLatestRatings,
    settings: initialSettingsState,
    stats: initialStatsState
};
export default combineReducers({ latestRatings, settings, stats });
