import { combineReducers } from 'redux';
import _ from 'lodash';
import { SIGN_OUT_SUCCESS } from '../auth/actions';

import { RESET_STORE } from '../app/actions';
import {
    GET_TRENDING_SHOWS_ERROR,
    GET_TRENDING_SHOWS_REQUEST,
    GET_TRENDING_SHOWS_SUCCESS,
    ShowAction,
    GET_POPULAR_SHOWS_REQUEST,
    GET_POPULAR_SHOWS_SUCCESS,
    GET_POPULAR_SHOWS_ERROR,
    GET_RECOMMENDED_SHOWS_REQUEST,
    GET_RECOMMENDED_SHOWS_SUCCESS,
    GET_RECOMMENDED_SHOWS_ERROR,
    HIDE_SHOW_SUCCESS,
    GET_WATCHED_SHOWS_REQUEST,
    GET_WATCHED_SHOWS_SUCCESS,
    GET_WATCHED_SHOWS_ERROR,
    GET_SIMILAR_SHOWS_REQUEST,
    GET_SIMILAR_SHOWS_ERROR,
    GET_SIMILAR_SHOWS_SUCCESS,
    GET_ANTICIPATED_SHOWS_REQUEST,
    GET_ANTICIPATED_SHOWS_SUCCESS,
    GET_ANTICIPATED_SHOWS_ERROR
} from './actions';

/** TRENDING */
export interface TrendingShowsState {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
    fetched: number;
}
export const initialTrendingShowsState: TrendingShowsState = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null,
    fetched: null
};

export function trending(state: TrendingShowsState = initialTrendingShowsState, action: ShowAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialTrendingShowsState;
        case GET_TRENDING_SHOWS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case GET_TRENDING_SHOWS_SUCCESS: {
            const { append, headers, normalized } = action.payload;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, normalized.result),
                    headers,
                    loading: false,
                    loadingMore: false,
                    fetched: new Date().getTime()
                };
            }
            return {
                ...state,
                result: normalized.result,
                headers,
                loading: false,
                loadingMore: false,
                fetched: new Date().getTime()
            };
        }
        case GET_TRENDING_SHOWS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

/** POPULAR */
export interface PopularShowsState {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
    fetched: number;
}
export const initialPopularShowsState: PopularShowsState = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null,
    fetched: null
};

export function popular(state: PopularShowsState = initialPopularShowsState, action: ShowAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialPopularShowsState;
        case GET_POPULAR_SHOWS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case GET_POPULAR_SHOWS_SUCCESS: {
            const { append, headers, normalized } = action.payload;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, normalized.result),
                    headers,
                    loading: false,
                    loadingMore: false,
                    fetched: new Date().getTime()
                };
            }
            return {
                ...state,
                result: normalized.result,
                headers,
                loading: false,
                loadingMore: false,
                fetched: new Date().getTime()
            };
        }
        case GET_POPULAR_SHOWS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

/** WATCHED */
export interface WatchedShowsState {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
    fetched: number;
}
export const initialWatchedShowsState: WatchedShowsState = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null,
    fetched: null
};

export function watched(state: WatchedShowsState = initialWatchedShowsState, action: ShowAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialWatchedShowsState;
        case GET_WATCHED_SHOWS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case GET_WATCHED_SHOWS_SUCCESS: {
            const { append, headers, normalized } = action.payload;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, normalized.result),
                    headers,
                    loading: false,
                    loadingMore: false,
                    fetched: new Date().getTime()
                };
            }
            return {
                ...state,
                result: normalized.result,
                headers,
                loading: false,
                loadingMore: false,
                fetched: new Date().getTime()
            };
        }
        case GET_WATCHED_SHOWS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

/** RECOMMENDED */
export interface RecommendedShowsState {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
    fetched: number;
}
export const initialRecommendedShowsState: RecommendedShowsState = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null,
    fetched: null
};

export function recommended(state: RecommendedShowsState = initialRecommendedShowsState, action: ShowAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialRecommendedShowsState;
        case GET_RECOMMENDED_SHOWS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case GET_RECOMMENDED_SHOWS_SUCCESS: {
            const { append, headers, normalized } = action.payload;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, normalized.result),
                    headers,
                    loading: false,
                    loadingMore: false,
                    fetched: new Date().getTime()
                };
            }
            return {
                ...state,
                result: normalized.result,
                headers,
                loading: false,
                loadingMore: false,
                fetched: new Date().getTime()
            };
        }
        case HIDE_SHOW_SUCCESS: {
            const { id } = action.payload;
            return {
                ...state,
                result: state.result.filter(val => val !== id)
            };
        }
        case GET_RECOMMENDED_SHOWS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

/** ANTICIPATED */
export interface AnticipatedShowsState {
    loading: boolean;
    loadingMore: boolean;
    result: number[];
    headers: Api.TraktHeaders;
    fetched: number;
}
export const initialAnticipatedShowsState: AnticipatedShowsState = {
    loading: false,
    loadingMore: false,
    result: [],
    headers: null,
    fetched: null
};

export function anticipated(state: AnticipatedShowsState = initialAnticipatedShowsState, action: ShowAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialAnticipatedShowsState;
        case GET_ANTICIPATED_SHOWS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case GET_ANTICIPATED_SHOWS_SUCCESS: {
            const { append, headers, normalized } = action.payload;
            if (append) {
                return {
                    ...state,
                    result: _.union(state.result, normalized.result),
                    headers,
                    loading: false,
                    loadingMore: false,
                    fetched: new Date().getTime()
                };
            }
            return {
                ...state,
                result: normalized.result,
                headers,
                loading: false,
                loadingMore: false,
                fetched: new Date().getTime()
            };
        }
        case GET_ANTICIPATED_SHOWS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

/** SIMILAR */
export interface SimilarShowsState {
    loading: boolean;
    loadingMore: boolean;
    [key: number]: {
        result: number[];
        headers: Api.TraktHeaders;
        fetched: null;
    };
}
export const initialSimilarShowsState: SimilarShowsState = {
    loading: false,
    loadingMore: false
};

export function similar(state: SimilarShowsState = initialSimilarShowsState, action: ShowAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialSimilarShowsState;
        case GET_SIMILAR_SHOWS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }

        case GET_SIMILAR_SHOWS_SUCCESS: {
            const { id, normalized, headers } = action.payload;
            return {
                ...state,
                [id]: {
                    result: normalized.result,
                    headers,
                    fetched: new Date().getTime()
                },
                loading: false,
                loadingMore: false
            };
        }
        case GET_SIMILAR_SHOWS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }
        default:
            return state;
    }
}

export interface ShowsState {
    anticipated: AnticipatedShowsState;
    popular: PopularShowsState;
    recommended: RecommendedShowsState;
    similar: SimilarShowsState;
    trending: TrendingShowsState;
    watched: WatchedShowsState;
}

export const initialShowsState: ShowsState = {
    anticipated: initialAnticipatedShowsState,
    popular: initialPopularShowsState,
    recommended: initialRecommendedShowsState,
    similar: initialSimilarShowsState,
    trending: initialTrendingShowsState,
    watched: initialWatchedShowsState
};
export default combineReducers({
    anticipated,
    popular,
    recommended,
    similar,
    trending,
    watched
});
