import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Icon } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import StringHelper from 'app/services/helpers/StringHelper';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export interface WatchersProps {
    trakt: number;
    size: number;
    nFormatted: boolean;
    watchKey: 'watchers' | 'watcher_count';
}

const Watchers = ({ trakt, size, nFormatted, watchKey }: WatchersProps) => {
    const locale = useSelector((state: MSState) => state.app.locale);
    const watchers = useSelector((state: MSState) => state.entities.show[trakt].meta[watchKey]);
    const watchersFormatted = !nFormatted
        ? watchers
            ? watchers.toLocaleString(locale)
            : null
        : StringHelper.nFormat(watchers);
    return (
        <View style={styles.container}>
            <Icon name="user" type="entypo" {...{ size }} />
            <Text>{watchersFormatted}</Text>
        </View>
    );
};
Watchers.defaultProps = {
    size: ICON_SIZE.small,
    nFormatted: false,
    watchKey: 'watcher_count'
};
export default React.memo(Watchers);
