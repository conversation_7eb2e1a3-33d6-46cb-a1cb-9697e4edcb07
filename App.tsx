import React, { useEffect, useState } from 'react';
import { LogBox, StyleSheet, Appearance } from 'react-native';
import { appStarted, setLocale, setScheme as setStateScheme } from 'app/app/redux/app/actions';
import { enableScreens } from 'react-native-screens';
import { Persistor } from 'redux-persist';
import configureStore from 'app/app/redux/store';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';

import { ThemeProvider, Header } from 'react-native-elements';
import Navigator from 'app/app/navigation/Navigator';
import * as en from 'app/app/tranlsations/english.json';
import * as el from 'app/app/tranlsations/greek.json';
import i18n from 'i18n-js';
import { createTheme, common } from 'app/app/styles/themes';
import Storage from 'app/services/storage/Storage';
import moment from 'moment';
import 'moment/min/locales';
import { AppInitialState } from 'app/app/redux/redux';
import Database from 'app/services/storage/DB';

import * as Sentry from 'sentry-expo';
import ExpoConstants from 'expo-constants';
import LocaleHelper from 'app/services/helpers/LocaleHelper';
import Constants from 'app/app/Constants';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { setTestDeviceIDAsync } from 'expo-ads-admob';
import NetInfo from '@react-native-community/netinfo';
import Icon from 'app/app/components/elements/Icon';

// if (__DEV__) {
//     // eslint-disable-next-line global-require
//     const whyDidYouRender = require('@welldone-software/why-did-you-render');
//     // eslint-disable-next-line global-require
//     const ReactRedux = require('react-redux');

//     whyDidYouRender(React, {
//         include: [/CommentListItem/],
//         // trackAllPureComponents: true,
//         trackExtraHooks: [[ReactRedux, 'useSelector']],
//         titleColor: 'yellow',
//         diffNameColor: 'white',
//         diffPathColor: 'gray'
//     });
// }

const styles = StyleSheet.create({
    noInternetContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 100,
        zIndex: 100,
        backgroundColor: common.red
    },
    noInternetIcon: {
        opacity: 0.2,
        position: 'absolute',
        top: 40,
        right: 20,
        zIndex: 100
    },
    gestureHandler :{
        flex: 1
    }
});

const dsn = Constants.App.SENTRY_DNS;
Sentry.init({
    dsn,
    enableInExpoDevelopment: true,
    debug: true,
    environment: __DEV__ ? 'Development' : 'Production',
    release: ExpoConstants.manifest.revisionId
});

export default () => {
    LogBox.ignoreLogs(['Setting a timer', 'It appears that you are using']);
    //LogBox.ignoreAllLogs();
    i18n.fallbacks = true;
    i18n.translations = { el, en };
    if (!__DEV__) {
        enableScreens();
    }
    const [scheme, setScheme] = React.useState(Appearance.getColorScheme());
    const [persist, setPersist] = React.useState(null);
    const [ready, setReady] = React.useState(false);
    const [isInternetReachable, setIsInternetReachable] = useState(null);
    const [showNoNet, setShowNoNet] = useState(false);
    const theme = createTheme(scheme);

    const init = async persist => {
        if (__DEV__) {
            await setTestDeviceIDAsync('EMULATOR');
        }
        await Database.createTables(false);
        return Promise.all([
            Storage.getItem(Storage.KEY_THEME).then(scheme => {
                const currentScheme = scheme || Appearance.getColorScheme();
                setScheme(currentScheme);
                persist.store.dispatch(setStateScheme(currentScheme));
            }),
            LocaleHelper.getLocale().then((loc: MS.Locales) => {
                i18n.locale = loc;
                moment.locale(loc);
                persist.store.dispatch(setLocale(loc));
            })
        ]);
    };

    useEffect(() => {
        Storage.getItem(Storage.KEY_THEME).then(scheme => {
            const currentScheme = scheme || Appearance.getColorScheme();
            setScheme(currentScheme);
        });
    }, [theme]);

    useEffect(() => {
        const persist: { store: any; persistor: Persistor } = configureStore(AppInitialState, () => {
            persist.store.dispatch(appStarted());
        });
        setPersist(persist);
        init(persist).then(() => {
            setReady(true);
        });
        const unsubscribe = NetInfo.addEventListener(state => {
            const isConnected = state.isInternetReachable;
            setIsInternetReachable(isConnected);
            if (!isConnected) {
                setShowNoNet(false);
            }
        });
        return () => {
            unsubscribe();
        };
    }, []);
    return ready ? (
        <Provider store={persist.store}>
            <PersistGate persistor={persist.persistor}>
                <ThemeProvider theme={theme}>
                    <ActionSheetProvider>
                        <GestureHandlerRootView style={styles.gestureHandler}>
                            <Navigator />
                            {!isInternetReachable && !showNoNet && (
                                <Header
                                    rightComponent={
                                        <Icon
                                            name="close"
                                            onPress={() => {
                                                setShowNoNet(true);
                                            }}
                                        />
                                    }
                                    centerComponent={{ text: i18n.t('NO_INTERNET') }}
                                    containerStyle={{ ...styles.noInternetContainer }}
                                />
                            )}
                            {showNoNet && (
                                <Icon
                                    onPress={() => {
                                        setShowNoNet(false);
                                    }}
                                    size={24}
                                    name="exclamation-circle"
                                    type="font-awesome"
                                    color={common.red}
                                    containerStyle={{
                                        ...styles.noInternetIcon
                                    }}
                                />
                            )}
                        </GestureHandlerRootView>
                    </ActionSheetProvider>
                </ThemeProvider>
            </PersistGate>
        </Provider>
    ) : null;
};
