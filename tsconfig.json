{"compilerOptions": {"allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": "./", "paths": {"app/*": ["./src/*"], "redx/*": ["./src/app/redux/*"], "components/*": ["./src/app/components/*"], "styles/*": ["./src/app/styles/*"], "views/*": ["./src/views/*"], "services/*": ["./src/services/*"]}}, "exclude": ["__tests__/**/*-test.ts"], "extends": "expo/tsconfig.base"}