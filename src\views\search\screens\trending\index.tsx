import React, { useEffect } from 'react';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { trendingShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import Back from 'app/app/components/header/Back';
import { FlatList } from 'react-native-gesture-handler';
import ApiHelper from 'app/services/helpers/ApiHelper';
import MoreLoader from 'app/app/components/loaders/MoreLoader';
import EmptyResults from 'app/app/components/containers/EmptyResults';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Watchers from 'app/app/components/show/Watchers';
import _ from 'lodash';

const Trending = () => {
    const dispatch = useDispatch();
    const { result, headers, loading, loadingMore } = useSelector((state: MSState) => {
        return {
            result: state.shows.trending.result,
            headers: state.shows.trending.headers,
            loading: state.shows.trending.loading,
            loadingMore: state.shows.trending.loadingMore
        };
    }, _.isEqual);
    useEffect(() => {
        if (result.length === 0) {
            const params: RequestParams = {
                extended: 'full'
            };
            dispatch(trendingShows(params));
        }
    }, []);

    const renderItem = ({ item: trakt }: { item: number }) => {
        return (
            <ShowListItem
                {...{
                    key: trakt,
                    trakt,
                    rightElement: <Watchers {...{ trakt, watchKey: 'watchers' }} />
                }}
            />
        );
    };
    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (!loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                extended: 'full',
                page: pagination.currentPage + 1
            };
            dispatch(trendingShows(params));
        }
    };

    return (
        <>
            <Header centerComponent={{ text: i18n.t('TRENDING_SHOWS') }} leftComponent={<Back />} />
            <FlatList<number>
                data={result}
                renderItem={renderItem}
                keyExtractor={item => item.toString()}
                onEndReached={loadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={loadingMore && <MoreLoader />}
                ListEmptyComponent={<EmptyResults text={i18n.t('NO_SHOWS_FOUND')} />}
            />
            {loading && <ScreenLoader />}
        </>
    );
};

export default React.memo(Trending, _.isEqual);
