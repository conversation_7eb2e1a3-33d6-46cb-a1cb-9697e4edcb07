import { combineReducers } from 'redux';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import { RESET_STORE } from '../app/actions';
import { CLEAR_STATS_SUCCESS, GET_STATS_REQUEST, GET_STATS_SUCCESS, GET_STATS_ERROR, StatsAction } from './actions';

export interface ItemState {
    stats: Trakt.Stats;
    loading: boolean;
}

export const initialItemState: ItemState = {
    stats: null,
    loading: false
};

export function item(state: ItemState = initialItemState, action: StatsAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
        case CLEAR_STATS_SUCCESS:
            return initialItemState;
        case GET_STATS_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_STATS_SUCCESS: {
            return { stats: action.payload.stats, loading: false };
        }
        case GET_STATS_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface StatsState {
    item: ItemState;
}

export const initialStatsState: StatsState = {
    item: initialItemState
};
export default combineReducers({
    item
});
