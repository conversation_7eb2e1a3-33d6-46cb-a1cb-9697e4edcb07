import React from 'react';
import { NavigationInjectedProps, createAppContainer, createSwitchNavigator } from 'react-navigation';

import { createStackNavigator } from 'react-navigation-stack';
import AuthCheck from 'app/views/auth/AuthCheck';
import { MSState } from 'app/app/redux/redux';
import { useSelector } from 'react-redux';
import { StatusBar } from 'react-native';
import ErrorBoundary from 'app/views/error/ErrorBoundary';
import SelectSettings from 'app/views/auth/SelectSettings';
import AppStack from './AppStack';
import AuthStack from './AuthStack';
import Constants from '../Constants';
import Toast from '../components/elements/Toast';

export interface NavParams {
    id?: number;
    title?: string;
    headerRight?: JSX.Element;
    headerLeft?: JSX.Element;
    transition?: 'slideUp' | 'slideDown' | 'collapseExpand' | 'default';
}

const MainStack = createStackNavigator(
    {
        App: AppStack
    },
    {
        headerMode: 'none'
    }
);

const Router = createSwitchNavigator(
    {
        [Constants.Navigation.Auth.SELECT_SETTINGS]: SelectSettings,
        [Constants.Navigation.Auth.CHECK]: AuthCheck,
        [Constants.Navigation.Auth.AUTH]: AuthStack,
        [Constants.Navigation.Main.STACK]: MainStack
    },
    {
        initialRouteName: Constants.Navigation.Auth.CHECK
    }
);

class AppNavigator extends React.Component<NavigationInjectedProps> {
    static router = Router.router;

    render() {
        const { navigation } = this.props;
        return (
            <ErrorBoundary {...{ navigation }}>
                <Router navigation={navigation} />
                <Toast />
            </ErrorBoundary>
        );
    }
}

const App = createAppContainer(AppNavigator);
const AppNav = () => {
    const scheme = useSelector((state: MSState) => state.app.scheme);
    return (
        <>
            <StatusBar
                translucent
                backgroundColor="transparent"
                showHideTransition="fade"
                barStyle={scheme === 'dark' ? 'light-content' : 'dark-content'}
            />
            <App theme={scheme} />
        </>
    );
};

export default AppNav;
