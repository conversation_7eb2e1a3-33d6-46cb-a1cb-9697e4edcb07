import React from 'react';
import { View } from 'react-native';
import _ from 'lodash';
import Constants from 'app/app/Constants';
import { EpisodeNavParams } from 'app/views/episode/view';
import { useNavigation } from 'react-navigation-hooks';
import EpisodeListItem from '../listItems/episode/EpisodeListItem';

export interface EpisodesProps {
    ids: Trakt.Ids;
    seasonNumber: number;
    episodes: TMDB.EpisodeDetails[];
}

const Episodes = ({ ids, episodes, seasonNumber }: EpisodesProps) => {
    // @ts-ignore
    const { push } = useNavigation();
    const onPress = (episodeNumber: number) => {
        const params: EpisodeNavParams = { ids, seasonNumber, episodeNumber };
        push(Constants.Navigation.Episode.VIEW, params);
    };
    return (
        <View>
            {episodes.map(episode => {
                const { id, name, air_date, episode_number, still_path } = episode;
                return (
                    <EpisodeListItem
                        {...{
                            show: ids.trakt,
                            season: seasonNumber,
                            episode: episode_number,
                            air_date,
                            episode_number,
                            name,
                            still_path,
                            key: id,
                            onPress: () => onPress(episode_number)
                        }}
                    />
                );
            })}
        </View>
    );
};

export default React.memo(Episodes, _.isEqual);
