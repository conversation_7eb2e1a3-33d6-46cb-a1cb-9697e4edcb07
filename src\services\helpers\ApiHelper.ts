import moment from 'moment';
import _ from 'lodash';

export interface Threshold {
    amount: moment.DurationInputArg1;
    unit: moment.unitOfTime.DurationConstructor;
}
export default class ApiHelper {
    static getPagination(headers: Api.TraktHeaders): Api.TraktPagination {
        if (!headers || !headers.map) {
            return {
                currentPage: 0,
                totalPages: 0,
                limit: 0,
                totalItems: 0
            };
        }
        return {
            currentPage: parseInt(headers.map['x-pagination-page']),
            totalPages: parseInt(headers.map['x-pagination-page-count']),
            limit: parseInt(headers.map['x-pagination-limit']),
            totalItems: parseInt(headers.map['x-pagination-item-count'])
        };
    }

    static shouldFetch(lastFetch: number, threshold: Threshold): boolean {
        if (!lastFetch) {
            return true;
        }
        const { amount, unit } = threshold;
        const diff = moment(new Date(lastFetch))
            .add(amount, unit)
            .diff();
        return diff < 0;
    }

    static hasNewActivity(
        lastActivity: Trakt.LastActivities,
        prevActivity: Trakt.LastActivities,
        types: string[],
        action: string
    ): boolean {
        if (_.isEmpty(lastActivity) || _.isEmpty(prevActivity)) {
            return true;
        }
        let hasNewActivity = false;
        types.forEach(type => {
            const lastTime = lastActivity?.[type][action];
            const prevTime = prevActivity?.[type][action];
            if (lastTime > prevTime) {
                hasNewActivity = true;
            }
        });
        return hasNewActivity;
    }
}
