import { RESET_STORE } from 'app/app/redux/app/actions';
import { FavoriteAction, GET_FAVORITES_REQUEST, GET_FAVORITES_SUCCESS, GET_FAVORITES_ERROR } from './actions';
import { SIGN_OUT_SUCCESS } from '../auth/actions';

export interface FavoritesState {
    loading: boolean;
    result: number[];
    refreshing: boolean;
    fetched: number;
}

export const initialFavoritesState: FavoritesState = {
    loading: false,
    result: [],
    refreshing: false,
    fetched: 0
};

export default function favorites(state: FavoritesState = initialFavoritesState, action: FavoriteAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialFavoritesState;
        case GET_FAVORITES_REQUEST: {
            const { refreshing } = action.payload;
            return { ...state, loading: true, refreshing };
        }
        case GET_FAVORITES_SUCCESS: {
            return {
                loading: false,
                result: action.payload.normalized.result,
                refreshing: false,
                fetched: new Date().getTime()
            };
        }
        case GET_FAVORITES_ERROR: {
            return { ...state, loading: false, refreshing: false };
        }
        default:
            return state;
    }
}
