import React, { useContext } from 'react';
import { StyleSheet, TouchableOpacity, View, StyleProp, ViewStyle, Dimensions } from 'react-native';
import ShowHelper from 'app/services/helpers/ShowHelper';
import _ from 'lodash';
import { ShowViewNavParams } from 'app/views/show/view';
import Constants from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import withShowFetch, { ShowFetchProps } from 'app/app/hocs/withShowFetch';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import Image from '../../elements/Image';
import Text from '../../elements/Text';

const { width: screenWidth } = Dimensions.get('window');

export const DEFAULT_RATIO = 0.66;

const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: 'row'
    },
    subtitle: {
        flexDirection: 'row'
    },
    image: {
        borderWidth: 1
    },
    card: {},
    cardAction: {
        position: 'absolute',
        top: 0,
        right: 0
    }
});
export interface ShowCardItemProps {
    trakt: number;
    rounded?: boolean;
    contentContainerStyle?: StyleProp<ViewStyle>;
    square?: boolean;
    rightElement?: JSX.Element;
    width?: number;
    useSortTitle: boolean;
}
type Props = ShowCardItemProps & ShowFetchProps;
const ShowCardItem = ({
    rounded,
    contentContainerStyle,
    square,
    rightElement,
    width,
    useSortTitle,
    show,
    image
}: Props) => {
    const ratio = square ? 1 : DEFAULT_RATIO;
    // @ts-ignore
    const { push } = useNavigation();
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    if (!show) {
        return null;
    }

    const { title, year, ids } = show?.attributes;
    const uri = ShowHelper.getPosterUri(image?.file_path, 33, scheme);
    const onPress = () => {
        const params: ShowViewNavParams = { ids };
        push(Constants.Navigation.Show.VIEW, params);
    };
    const cardWidth = width || screenWidth / 5;
    console.log(uri)
    return (
        <View style={[{ ...styles.card, width: cardWidth }, contentContainerStyle]}>
            <TouchableOpacity onPress={onPress}>
                <Image
                    {...{
                        width: cardWidth,
                        height: cardWidth / ratio,
                        rounded,
                        uri
                    }}
                />
                <Text numberOfLines={2} textStyle="small" centered>
                    {useSortTitle ? ShowHelper.getSortTitle(title) : title}
                </Text>
                <Text textStyle="small" centered>
                    {year}
                </Text>
                <View style={styles.cardAction}>{rightElement}</View>
            </TouchableOpacity>
        </View>
    );
};

ShowCardItem.defaultProps = {
    rounded: false,
    contentContainerStyle: {},
    square: false,
    rightElement: null,
    useSortTitle: false
};

export default withShowFetch(React.memo(ShowCardItem, _.isEqual));
