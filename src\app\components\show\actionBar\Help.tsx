import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import i18n from 'i18n-js';
import { ThemeContext, ThemeProps, Icon } from 'react-native-elements';
import { Theme, common } from 'app/app/styles/themes';
import { ICON_SIZE, SPACING } from 'app/app/styles/sizes';
import IMDB from 'app/assets/images/companies/imdb.svg';
import Text from '../../elements/Text';

const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        marginBottom: SPACING.normal,
        borderBottomWidth: 1,
        paddingVertical: SPACING.normal
    },
    icon: {
        width: 40,
        alignItems: 'center',
        justifyContent: 'center'
    },
    text: {
        flex: 1,
        marginHorizontal: SPACING.normal
    }
});

export interface HelpProps {}

const Help = () => {
    const {
        theme: { scheme, colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const help: {
        id: string;
        icon?: JSX.Element;
        iconColor?: string;
        iconName?: string;
        iconType?: string;
        text: string;
    }[] = [
        {
            id: 'show',
            iconName: 'tv',
            iconColor: scheme === 'dark' ? common.white : common.black,
            text: i18n.t('SHOW_ACTIONS_HELP_TV')
        },
        {
            id: 'imdb',
            icon: (
                <View style={{ ...styles.icon }}>
                    <IMDB {...{ width: 30, height: 15 }} />
                </View>
            ),
            text: i18n.t('SHOW_ACTIONS_IMDB')
        },
        {
            id: 'share',
            iconName: 'share',
            iconColor: common.green,
            text: i18n.t('SHOW_ACTIONS_HELP_SHARE')
        },
        {
            id: 'collect',
            iconName: 'disc',
            iconType: 'feather',
            iconColor: common.green,
            text: i18n.t('SHOW_ACTIONS_HELP_COLLECT')
        },
        {
            id: 'list',
            iconName: 'list',
            iconType: 'font-awesome',
            iconColor: common.blue,
            text: i18n.t('SHOW_ACTIONS_HELP_LIST')
        },
        {
            id: 'rate',
            iconName: 'heart',
            iconType: 'font-awesome',
            iconColor: common.red,
            text: i18n.t('SHOW_ACTIONS_HELP_RATE')
        },
        {
            id: 'watch',
            iconName: 'check-circle',
            iconType: 'font-awesome',
            iconColor: common.green,
            text: i18n.t('SHOW_ACTIONS_HELP_WATCH')
        }
    ];
    return (
        <View>
            {help.map(help => {
                const { iconColor, iconName, icon, text, id, iconType } = help;
                return (
                    <View key={id} style={{ ...styles.row, borderBottomColor: colors.divider }}>
                        {iconName && (
                            <Icon
                                {...{
                                    color: iconColor,
                                    type: iconType,
                                    name: iconName,
                                    size: ICON_SIZE.small,
                                    containerStyle: styles.icon
                                }}
                            />
                        )}
                        {icon}
                        <Text style={{ ...styles.text }}>{text}</Text>
                    </View>
                );
            })}
        </View>
    );
};

export default React.memo(Help);
