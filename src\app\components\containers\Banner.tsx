import React from 'react';
import { AdMobBanner } from 'expo-ads-admob';
import Constants from 'app/app/Constants';

const Banner = () => {
    const show = Constants.App.AdMob.SHOW_BANNER;
    return show ? (
        <AdMobBanner
            bannerSize="fullBanner"
            adUnitID={Constants.App.AdMob.ANDROID.BANNER}
            servePersonalizedAds // true or false
        />
    ) : null;
};

export default React.memo(Banner);
