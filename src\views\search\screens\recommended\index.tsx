import React, { useEffect } from 'react';
import { withNavigation } from 'react-navigation';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { recommendedShows, hideShow } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import Back from 'app/app/components/header/Back';
import { FlatList } from 'react-native-gesture-handler';
import EmptyResults from 'app/app/components/containers/EmptyResults';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import _ from 'lodash';
import { common } from 'app/app/styles/themes';
import MoreLoader from 'app/app/components/loaders/MoreLoader';

export interface RecommendedProps {}
const Recommended = () => {
    const dispatch = useDispatch();
    const { loading, result, total, loadingMore } = useSelector((state: MSState) => {
        const { recommended } = state.shows;
        return {
            loading: recommended.loading,
            result: recommended.result,
            total: recommended.result.length,
            loadingMore: recommended.loadingMore,
            headers: recommended.headers
        };
    }, _.isEqual);

    useEffect(() => {
        if (total < 10) {
            const params: RequestParams = {
                extended: 'full',
                limit: 10,
                page: 1
            };
            dispatch(recommendedShows(params));
        }
    }, [total]);

    const loadMore = () => {
        if (!loadingMore && total < 90) {
            const params: RequestParams = {
                extended: 'full',
                page: 1,
                limit: total + 10
            };
            dispatch(recommendedShows(params));
        }
    };

    const renderItem = ({ item: trakt }: { item: number }) => {
        const swipeButtons = [
            {
                label: i18n.t('HIDE_REC'),
                onPress: () => dispatch(hideShow(trakt)),
                iconProps: { name: 'trash' },
                backgroundColor: common.red
            }
        ];

        return (
            <>
                <ShowListItem
                    {...{
                        key: trakt,
                        trakt,
                        extraSwipeButtons: swipeButtons
                    }}
                />
            </>
        );
    };
    return (
        <>
            <Header centerComponent={{ text: i18n.t('RECOMMENDED_SHOWS') }} leftComponent={<Back />} />
            <FlatList<number>
                data={result}
                renderItem={renderItem}
                keyExtractor={item => item.toString()}
                onEndReached={loadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={loadingMore && <MoreLoader />}
                ListEmptyComponent={<EmptyResults text={i18n.t('NO_SHOWS_FOUND')} />}
            />
            {loading && result.length === 0 && <ScreenLoader />}
        </>
    );
};

export default withNavigation(React.memo(Recommended, _.isEqual));
