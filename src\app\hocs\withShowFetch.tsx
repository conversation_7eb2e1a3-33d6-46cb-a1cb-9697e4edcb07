import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';
import { MSState } from '../redux/redux';
import { getShowImages } from '../redux/image/actions';
import { getShowDetails } from '../redux/show/actions';

export interface ShowFetchProps {
    show: Api.Entity<Trakt.Show>;
    image: TMDB.Image;
}
const withShowFetch = BaseComponent => (props: { trakt: number }) => {
    const dispatch = useDispatch();
    const defaultArray = [];
    const {
        show,
        posters,
        hasFetched,
        poster_path,
        showDetailsFetched
    }: {
        show: Api.Entity<Trakt.Show>;
        posters: TMDB.Image[];
        hasFetched: boolean;
        poster_path: string;
        showDetailsFetched: boolean;
    } = useSelector((state: MSState) => {
        const show = state.entities.show[props.trakt];
        const ids = show?.attributes.ids;
        const posters =
            ids && state.entities.image[ids.tmdb] ? state.entities.image[ids.tmdb].attributes.posters : defaultArray;
        const hasFetched = !!(ids && state.entities.image[ids.tmdb]);
        const poster_path = state.entities.showDetails[ids.tmdb]?.attributes.poster_path || null;
        const showDetailsFetched = state.entities.showDetails[ids.tmdb]?.attributes.fetched || null;
        return {
            show,
            posters,
            hasFetched,
            poster_path,
            showDetailsFetched
        };
    }, _.isEqual);
    const tmdb = show?.attributes.ids.tmdb;
    const image = posters[0] || {
        aspect_ratio: 0.66,
        file_path: poster_path,
        height: 1740,
        iso_639_1: 'en',
        vote_average: 0,
        vote_count: 0,
        width: 1160
    };

    useEffect(() => {
        if (!poster_path && posters.length === 0 && tmdb && !hasFetched) {
            dispatch(getShowImages(tmdb));
        } else if (!poster_path && posters.length === 0 && tmdb && !showDetailsFetched) {
            dispatch(getShowDetails(tmdb));
        }
    }, []);

    useEffect(() => {
        if (posters.length === 0 && tmdb && !hasFetched) {
            dispatch(getShowImages(tmdb));
        }
    }, [tmdb]);
    return <BaseComponent {...props} {...{ show, image }} />;
};

export default withShowFetch;
