import React, { useEffect } from 'react';
import { FlatList } from 'react-native';
import { NavParams } from 'app/app/navigation/Navigator';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import Close from 'app/app/components/header/Close';
import { useDispatch, useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import Container from 'app/app/components/containers/Container';
import CommentListItem from 'app/app/components/listItems/comment/CommentListItem';
import { useNavigation } from 'react-navigation-hooks';
import { getReplies } from 'app/app/redux/comment/actions';
import ApiHelper from 'app/services/helpers/ApiHelper';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import MoreLoader from 'app/app/components/loaders/MoreLoader';

export interface RepliesNavParams extends NavParams {
    id: number;
}

const Replies = () => {
    const { getParam } = useNavigation();
    const id = getParam('id');
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(getReplies(id, { limit: 20 }));
    }, []);
    const {
        ids,
        headers,
        loadingMore
    }: { ids: number[]; headers: Api.TraktHeaders; loadingMore: boolean } = useSelector((state: MSState) => {
        return {
            ids: state.comments.replies[id]?.result,
            headers: state.comments.replies[id]?.headers,
            loadingMore: state.comments.replies?.loadingMore
        };
    });

    const renderItem = ({ item }: { item: number }) => {
        return <CommentListItem {...{ id: item, showBorder: true, isReply: true }} />;
    };
    const loadMore = () => {
        const pagination = ApiHelper.getPagination(headers);
        if (!loadingMore && pagination.currentPage < pagination.totalPages) {
            const params: RequestParams = {
                page: pagination.currentPage + 1
            };
            dispatch(getReplies(id, params));
        }
    };
    return (
        <>
            <Header leftComponent={<Close />} centerComponent={{ text: i18n.t('REPLIES') }} />
            <Container useScrollView={false}>
                <FlatList<number>
                    {...{ data: ids, keyExtractor: item => item.toString(), renderItem }}
                    ListHeaderComponent={<CommentListItem {...{ id, showBorder: true }} />}
                    onEndReached={loadMore}
                    onEndReachedThreshold={0.1}
                    ListFooterComponent={loadingMore && <MoreLoader />}
                />
            </Container>
        </>
    );
};

export default React.memo(Replies);
