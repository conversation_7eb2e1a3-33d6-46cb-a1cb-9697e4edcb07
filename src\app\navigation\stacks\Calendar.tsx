import Constants from 'app/app/Constants';
import Calendar from 'app/views/calendar/view';
import { createStackNavigator } from 'react-navigation-stack';
import { dark, light } from 'app/app/styles/themes';
import TransitionConfiguration from '../transitions';
import Common from './Common';

export default createStackNavigator(
    {
        [Constants.Navigation.Calendar.VIEW]: Calendar,
        ...Common
    },
    {
        headerMode: 'none',
        defaultNavigationOptions: ({ theme }) => {
            const colors = theme === 'dark' ? dark : light;
            return { headerStyle: { backgroundColor: colors.background } };
        },
        transitionConfig: TransitionConfiguration
    }
);
