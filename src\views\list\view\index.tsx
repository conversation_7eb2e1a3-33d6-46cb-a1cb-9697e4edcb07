import React, { useEffect, useState, useContext } from 'react';
import { FlatList } from 'react-native';
import { <PERSON><PERSON>, ThemeContext, ThemeProps } from 'react-native-elements';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { getLists } from 'app/app/redux/list/actions';
import { MSState } from 'app/app/redux/redux';
import ListListItem from 'app/app/components/listItems/list/ListListItem';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import _ from 'lodash';
import RefreshControl from 'app/app/components/refresh/RefreshControl';
import HeaderIcon from 'app/app/components/header/HeaderIcon';
import Banner from 'app/app/components/containers/Banner';
import swipeListLight from 'app/assets/images/help/swipe_list_light.png';
import swipeListDark from 'app/assets/images/help/swipe_list_dark.png';
import HelpHelper from 'app/services/helpers/HelpHelper';
import Help from 'app/app/components/containers/Help';
import { Theme } from 'app/app/styles/themes';

const renderItem = ({ item }: { item: number }) => {
    return <ListListItem {...{ id: item }} />;
};
const Lists = () => {
    const dispatch = useDispatch();
    const [helpVisible, setHelpVisible] = useState(false);
    const { navigate } = useNavigation();
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const { lists, fetched, refreshing } = useSelector((state: MSState) => {
        const { result: lists, fetched, refreshing } = state.lists.lists;
        return {
            lists,
            fetched,
            refreshing
        };
    }, _.isEqual);

    useEffect(() => {
        if (lists.length === 0 || ApiHelper.shouldFetch(fetched, UpdateThresholds.HIGH)) {
            dispatch(getLists());
        } else {
            HelpHelper.getHelp('swipeShow').then(value => {
                setHelpVisible(value);
            });
        }
    }, []);
    const onPress = () => {
        navigate(Constants.Navigation.Lists.MANAGE);
    };

    const onRefresh = () => dispatch(getLists(true));
    const refreshControl = <RefreshControl {...{ refreshing, onRefresh }} />;
    const rightComponent = <HeaderIcon name="plus" onPress={onPress} />;

    return (
        <>
            <Header rightComponent={rightComponent} centerComponent={{ text: i18n.t('LISTS') }} />
            <FlatList<number>
                data={lists}
                keyExtractor={item => item.toString()}
                renderItem={renderItem}
                refreshControl={refreshControl}
            />
            <Banner />
            <Help
                {...{
                    title: i18n.t('SWIPE_LIST_HELP_TITLE'),
                    isVisible: helpVisible,
                    image: scheme === 'dark' ? swipeListDark : swipeListLight,
                    imageRatio: 110 / 412,
                    text: i18n.t('SWIPE_LIST_HELP')
                }}
            />
        </>
    );
};

export default React.memo(Lists);
