import React, { useEffect } from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { trendingShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Teaser from 'app/app/components/containers/Teaser';
import Watchers from 'app/app/components/show/Watchers';
import _ from 'lodash';

export interface TrendingProps {}
type Props = TrendingProps & NavigationInjectedProps<{}>;
const Trending = ({ navigation }: Props) => {
    const dispatch = useDispatch();
    const { result, headers, fetched } = useSelector((state: MSState) => {
        const { trending } = state.shows;
        return {
            headers: state.shows.trending.headers,
            result: trending.result.slice(0, Constants.App.LIST_TEASER_ITEMS),
            fetched: state.shows.trending.fetched
        };
    }, _.isEqual);
    useEffect(() => {
        if (ApiHelper.shouldFetch(fetched, UpdateThresholds.LOWER)) {
            const params: RequestParams = {
                extended: 'full',
                limit: 20
            };
            dispatch(trendingShows(params));
        }
    }, []);

    const renderItem = (trakt: number) => {
        return (
            <ShowListItem
                {...{
                    key: trakt,
                    trakt,
                    rightElement: <Watchers {...{ trakt, watchKey: 'watchers' }} />
                }}
            />
        );
    };
    const { totalItems } = ApiHelper.getPagination(headers);
    const onPress = () => {
        navigation.navigate(Constants.Navigation.Search.TRENDING);
    };
    return (
        result.length > 0 && (
            <Teaser
                {...{
                    data: result,
                    onPress,
                    renderItem,
                    title: i18n.t('TRENDING_SHOWS'),
                    subtitle: i18n.t('TRENDING_SHOWS_SUB'),
                    showMore: totalItems > Constants.App.LIST_TEASER_ITEMS
                }}
            />
        )
    );
};

export default withNavigation(React.memo(Trending, _.isEqual));
