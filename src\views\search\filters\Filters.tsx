import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import i18n from 'i18n-js';
import Slider from 'app/app/components/elements/form/Slider';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import { Header } from 'react-native-elements';
import Back from 'app/app/components/header/Back';
import HeaderIcon from 'app/app/components/header/HeaderIcon';
import Select from 'app/app/components/elements/form/Select';
import { SPACING } from 'app/app/styles/sizes';
import { MSState } from 'app/app/redux/redux';
import { useSelector } from 'react-redux';
import getEntities from 'app/app/redux/entities/selectors';
import ShowHelper from 'app/services/helpers/ShowHelper';
import StringHelper from 'app/services/helpers/StringHelper';

const styles = StyleSheet.create({
    headers: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    apply: {
        position: 'absolute',
        right: 20
    },
    cancel: {
        position: 'absolute',
        left: 20
    },
    clear: {
        position: 'absolute',
        right: 80
    }
});
const START_YEAR = 1913;
const END_YEAR = new Date().getFullYear();

const START_RUNTIME = 0;
const END_RUNTIME = 200;

const START_RATING = 0;
const END_RATING = 100;

export interface FiltersProps {
    filters: RequestParams;
    onApply: (params: RequestParams) => void;
    onCancel: () => void;
}
const defaultArray = [];
const Filters = ({ onApply, filters, onCancel }: FiltersProps) => {
    const filterYears = (filters.years as number[]) || [START_YEAR, END_YEAR];
    const filterRuntimes = (filters.runtimes as number[]) || [START_RUNTIME, END_RUNTIME];
    const filterRatings = (filters.ratings as number[]) || [START_RATING, END_RATING];
    const filterGenres = (filters.genres as string[]) || defaultArray;
    const filterCertifications = (filters.certifications as string[]) || defaultArray;
    const filterStatus = (filters.status as string[]) || defaultArray;
    const filterNetworks = (filters.networks as string[]) || defaultArray;

    const {
        allGenres,
        allCertifications,
        allNetworks
    }: {
        allGenres: Api.Entity<Trakt.Genre>[];
        allCertifications: Api.Entity<Trakt.Certification>[];
        allNetworks: string[];
    } = useSelector((state: MSState) => {
        const genreIds = state.meta.genres.result;
        const allGenres = getEntities(state, genreIds, 'genre') as Api.Entity<Trakt.Genre>[];
        const certificationsIds = state.meta.certifications.result;
        const allCertifications = getEntities(state, certificationsIds, 'certification') as Api.Entity<
            Trakt.Certification
        >[];
        const allNetworks = state.meta.networks.result;
        return { allGenres, allCertifications, allNetworks };
    });

    const [years, setYears] = useState<number[]>(filterYears);
    const [runtimes, setRuntimes] = useState<number[]>(filterRuntimes);
    const [ratings, setRatings] = useState<number[]>(filterRatings);
    const [genres, setGenres] = useState<string[]>(filterGenres);
    const [certifications, setCertifications] = useState<string[]>(filterCertifications);
    const [status, setStatus] = useState<Trakt.ShowStatus[]>(filterStatus);
    const [networks, setNetworks] = useState<string[]>(filterNetworks);

    const apply = () => {
        const params: RequestParams = {
            years,
            runtimes,
            ratings,
            genres,
            certifications,
            status,
            networks
        };
        if (years[0] === START_YEAR && years[1] === END_YEAR) {
            delete params.years;
        }
        if (runtimes[0] === START_RUNTIME && runtimes[1] === END_RUNTIME) {
            delete params.runtimes;
        }
        if (ratings[0] === START_RATING && ratings[1] === END_RATING) {
            delete params.ratings;
        }
        if (genres.length === 0) {
            delete params.genres;
        }
        if (certifications.length === 0) {
            delete params.certifications;
        }
        if (status.length === 0) {
            delete params.status;
        }
        if (networks.length === 0) {
            delete params.networks;
        }
        onApply(params);
    };

    const reset = () => {
        setYears([START_YEAR, END_YEAR]);
        setRuntimes([START_RUNTIME, END_RUNTIME]);
        setRatings([START_RATING, END_RATING]);
        setGenres([]);
        setCertifications([]);
        setStatus([]);
        setNetworks([]);
    };
    const rightComponent = (
        <View style={{ ...styles.headers }}>
            <HeaderIcon name="undo" type="fontisto" onPress={reset} />
            <HeaderIcon name="check" type="feather" onPress={apply} />
        </View>
    );

    const onBack = () => {
        onCancel();
    };

    return (
        <>
            <Header
                {...{
                    leftComponent: <Back onPress={onBack} />,
                    centerComponent: { text: 'FILTERS' },
                    rightComponent
                }}
            />
            <View style={{ paddingHorizontal: SPACING.normal }}>
                <Select
                    {...{
                        multiple: true,
                        options: allNetworks.map(network => ({
                            label: network,
                            value: network
                        })),
                        onSelect: setNetworks,
                        title: i18n.t('NETWORK'),
                        subtitle: null,
                        selected: networks
                    }}
                />
                <Select
                    {...{
                        multiple: true,
                        options: allGenres.map(genre => ({ label: genre.attributes.name, value: genre.id })),
                        onSelect: setGenres,
                        title: i18n.t('GENRE'),
                        subtitle: null,
                        selected: genres
                    }}
                />
                <Select
                    {...{
                        multiple: true,
                        options: ShowHelper.getShowStatuses().map(status => ({
                            label: StringHelper.toSentenceCase(status),
                            value: status
                        })),
                        onSelect: setStatus,
                        title: i18n.t('STATUS'),
                        subtitle: null,
                        selected: status
                    }}
                />
                <Select
                    {...{
                        multiple: true,
                        options: allCertifications.map(cert => ({
                            label: cert.attributes.description,
                            value: cert.id
                        })),
                        onSelect: setCertifications,
                        title: i18n.t('CERTIFICATIONS'),
                        subtitle: null,
                        selected: certifications
                    }}
                />
                <Slider onChange={setYears} title={i18n.t('YEAR')} values={years} min={START_YEAR} max={END_YEAR} />
                <Slider
                    onChange={setRuntimes}
                    title={i18n.t('RUNTIME')}
                    values={runtimes}
                    min={START_RUNTIME}
                    max={END_RUNTIME}
                />
                <Slider
                    onChange={setRatings}
                    title={i18n.t('RATING')}
                    values={ratings}
                    min={START_RATING}
                    max={END_RATING}
                />
            </View>
        </>
    );
};

export default React.memo(Filters);
