import React from 'react';
import { ButtonGroupProps, ListItem, ButtonGroup as RNButtonGroup } from 'react-native-elements';
import styles from './styles';

export interface Props extends ButtonGroupProps {}

const ButtonGroup = (props: Props) => {
    return (
        <ListItem
            containerStyle={{ ...styles.fieldContainer, ...styles.buttons }}
            title={<RNButtonGroup {...props} />}
        />
    );
};

export default React.memo(ButtonGroup);
