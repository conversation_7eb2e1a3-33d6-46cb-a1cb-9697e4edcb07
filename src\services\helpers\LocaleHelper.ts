import * as Localization from 'expo-localization';
import moment from 'moment';
import Storage from '../storage/Storage';

export default class LocaleHelper {
    static getDefaultLocale(): MS.Locales {
        const mobileLocale = Localization.locale?.substr(0, 2) || 'en';
        if (['en', 'el'].includes(mobileLocale)) {
            return mobileLocale as MS.Locales;
        }
        return 'en';
    }

    static async getLocale(): Promise<MS.Locales> {
        return Storage.getItem(Storage.KEY_LOCALE).then(locale => {
            const userLocale = LocaleHelper.getDefaultLocale();
            const loc = locale || userLocale;
            return loc as MS.Locales;
        });
    }

    static getLanguageLabel(locale: MS.Locales): string {
        switch (locale) {
            case 'en':
            default:
                return 'ENGLISH';
            case 'el':
                return 'GREEK';
        }
    }

    static setMomentLocale(locale: MS.Locales) {
        switch (locale) {
            case 'el':
                moment.updateLocale(locale);
                break;
            case 'en':
            default:
                moment.updateLocale('en-gb');
                break;
        }
    }
}
