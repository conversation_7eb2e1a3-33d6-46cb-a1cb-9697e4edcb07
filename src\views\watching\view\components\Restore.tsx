import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import Text from 'app/app/components/elements/Text';
import ClearButton from 'app/app/components/elements/ClearButton';
import i18n from 'i18n-js';
import { SPACING } from 'app/app/styles/sizes';
import { useDispatch, useSelector } from 'react-redux';
import { getWatchedHistoryForFavs } from 'app/app/redux/sync/watch';
import moment from 'moment';
import { MSState } from 'app/app/redux/redux';
import _ from 'lodash';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import getEntities from 'app/app/redux/entities/selectors';
import { addToFavorites } from 'app/app/redux/favorite/actions';
import { ShowAction } from 'app/app/redux/show/actions';

const styles = StyleSheet.create({
    backup: {
        padding: SPACING.medium
    }
});
const startDate = moment()
    .subtract(2, 'month')
    .format('YYYY-MM-DDT00:00:00.000Z');

interface StateProps {
    shows: Api.Entity<Trakt.Show>[];
    images: { [key: number]: string };
}
const Restore = () => {
    const [inserting, setInserting] = useState(false);
    const [insertingFavs, setInsertingFavs] = useState(false);
    const dispatch: MS.Dispatch<ShowAction> = useDispatch();
    const { shows, images } = useSelector<MSState, StateProps>(state => {
        const { shows: showsIds } = state.meta.importShows;
        const shows = getEntities(state, showsIds, 'show') as Api.Entity<Trakt.Show>[];
        const images = {};
        shows.forEach(show => {
            images[show.id] = state.entities.image[show.attributes.ids.tmdb]?.attributes.posters?.[0];
        });
        return {
            shows,
            images
        };
    }, _.isEqual);

    useEffect(() => {
        if (shows.length > 0 && !_.isEmpty(images) && !inserting) {
            setInserting(true);
            const promises = [];
            shows.forEach(show => {
                const poster = images[show.id];
                promises.push(dispatch(addToFavorites(show, poster)));
            });
            Promise.all(promises).then(() => {
                setInsertingFavs(false);
            });
        }
    }, [shows, images]);

    const restore = () => {
        setInsertingFavs(true);
        dispatch(getWatchedHistoryForFavs({ start_at: startDate, limit: 300 }));
    };
    return (
        <View style={{ ...styles.backup }}>
            <Text centered textStyle="large">
                {i18n.t('RESTORE_FROM_TRAKT')}
            </Text>
            <ClearButton title={i18n.t('RESTORE_BACKUP')} onPress={restore} />
            {insertingFavs && <ScreenLoader />}
        </View>
    );
};

export default React.memo(Restore);
