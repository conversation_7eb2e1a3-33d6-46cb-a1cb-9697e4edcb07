import { common } from 'styles/themes';
import Constants from 'app/app/Constants';
import { ImageURISource, Dimensions, ColorSchemeName } from 'react-native';
import defaultLightPoster from 'app/assets/images/show/poster_light.png';
import defaultDarkPoster from 'app/assets/images/show/poster_dark.png';
import defaultBigLightPoster from 'app/assets/images/show/poster_big_light.png';
import defaultBigDarkPoster from 'app/assets/images/show/poster_big_dark.png';
import defaultLightBackdrop from 'app/assets/images/show/backdrop_light.png';
import defaultDarkBackdrop from 'app/assets/images/show/backdrop_dark.png';
import defaultWomanProfile from 'app/assets/images/avatar/woman.png';
import defaultManProfile from 'app/assets/images/avatar/man.png';
import { ListItemsState } from 'app/app/redux/list/reducers';

import ArrayHelper from './ArrayHelper';

const { width } = Dimensions.get('window');
export type ImageType = 'poster' | 'backdrop' | 'still' | 'profile';
export type ImageSize = 20 | 25 | 33 | 50 | 100;

const posterSizes: number[] = [92, 185, 254, 342, 500, 780];
const backdropSizes: number[] = [300, 780, 1380];
const stillSizes: number[] = [92, 185, 300];
// const logoSizes: number[] = [45, 92, 154, 185, 300, 500];
const profileSizes: number[] = [45, 185, 632];

export default class ShowHelper {
    static getShowStatuses(): Trakt.ShowStatus[] {
        return ['canceled', 'ended', 'in production', 'planned', 'returning series'];
    }

    static getStatus(status: Trakt.ShowStatus) {
        switch (status) {
            case 'canceled':
                return ['Cancelled', common.red];
            case 'ended':
                return ['Ended', common.blue];
            case 'in production':
                return ['In Production', common.yellow];
            case 'planned':
                return ['In Production', common.orange];
            case 'returning series':
                return ['In Production', common.green];
            default:
                return [];
        }
    }

    private static getImageSize = (type: ImageType, size: ImageSize): string => {
        const optimal: number = (width * (size as number)) / 100;
        let finalSize: string;
        switch (type) {
            case 'poster':
                posterSizes.forEach(posterSize => {
                    if (posterSize >= optimal && !finalSize) {
                        finalSize = `w${posterSize}`;
                    }
                });
                break;
            case 'backdrop':
                backdropSizes.forEach(backdropSize => {
                    if (backdropSize >= optimal && !finalSize) {
                        finalSize = `w${backdropSize}`;
                    }
                });
                break;
            case 'profile':
                profileSizes.forEach(profileSize => {
                    if (profileSize >= optimal && !finalSize) {
                        finalSize = `w${profileSize}`;
                    }
                });
                break;
            case 'still':
                stillSizes.forEach(stillSize => {
                    if (stillSize >= optimal && !finalSize) {
                        finalSize = `w${stillSize}`;
                    }
                });
                break;

            default:
                break;
        }
        return finalSize || 'original';
    };

    static getPosterUri(path: string, size: ImageSize, scheme: ColorSchemeName): ImageURISource | number {
        if (!path) {
            if (scheme === 'dark') {
                return size < 25 ? defaultDarkPoster : defaultBigDarkPoster;
            }
            return size < 25 ? defaultLightPoster : defaultBigLightPoster;
        }
        const imageSize = ShowHelper.getImageSize('poster', size);
        return {
            uri: `${Constants.Api.Tmdb.IMAGES.SECURE_BASE_URL}/${imageSize}/${path}`
        };
    }

    static getBackdropUri(path: string, size: ImageSize, scheme: ColorSchemeName): ImageURISource | number {
        if (!path) {
            return scheme === 'dark' ? defaultDarkBackdrop : defaultLightBackdrop;
        }
        const imageSize = ShowHelper.getImageSize('backdrop', size);
        return {
            uri: `${Constants.Api.Tmdb.IMAGES.SECURE_BASE_URL}/${imageSize}/${path}`
        };
    }

    static getProfileUri(
        path: string,
        gender: number, // 1 man , 2 woman
        size: ImageSize
    ): ImageURISource | number {
        if (!path) {
            return gender === 1 ? defaultWomanProfile : defaultManProfile;
        }
        const imageSize = ShowHelper.getImageSize('profile', size);
        return {
            uri: `${Constants.Api.Tmdb.IMAGES.SECURE_BASE_URL}/${imageSize}/${path}`
        };
    }

    static getStillUri(path: string, size: ImageSize, scheme: ColorSchemeName): ImageURISource | number {
        if (!path) {
            return scheme === 'dark' ? defaultDarkBackdrop : defaultLightBackdrop;
        }
        const imageSize = ShowHelper.getImageSize('still', size);
        return {
            uri: `${Constants.Api.Tmdb.IMAGES.SECURE_BASE_URL}/${imageSize}/${path}`
        };
    }

    static getCoverUri(path: string, scheme: ColorSchemeName): ImageURISource | number {
        if (!path) {
            return scheme === 'dark' ? defaultDarkBackdrop : defaultLightBackdrop;
        }
        return { uri: path };
    }

    static getSeason(seasons: TMDB.Season[], seasonNumber: number): TMDB.Season {
        const filtered = seasons.filter(season => {
            return season.season_number === seasonNumber;
        });
        if (filtered.length === 1) {
            return filtered[0];
        }
        return null;
    }

    static getSortTitle(title: string): string {
        const words = ['A ', 'The ', 'An '];
        for (let i = 0; i < words.length; i += 1) {
            const word = words[i];
            if (title !== null && title.length > 0 && title.substring(0, word.length) === word) {
                return `${title.substring(word.length).trim()}, ${word}`;
            }
        }
        return title;
    }

    static getListed(listItems: ListItemsState, id: number): number[] {
        const listed = Object.keys(listItems).map(key => {
            const listItem = listItems[key];
            if (listItem.result) {
                return listItem.result.includes(id) ? parseInt(key) : null;
            }
            return null;
        });
        return listed.filter(l => !!l);
    }

    static createRateData(
        type: Trakt.RateType,
        show: number,
        season: number,
        episode: number,
        rate: number
    ): Trakt.RateData {
        const data: Trakt.RateData = {
            shows: [
                {
                    ids: { trakt: show }
                }
            ]
        };
        switch (type) {
            case 'shows':
            default: {
                data.shows[0].rating = rate;
                return data;
            }
            case 'seasons': {
                data.shows[0] = { ids: { trakt: show }, seasons: [{ number: season, rating: rate }] };
                return data;
            }
            case 'episodes': {
                data.shows[0] = {
                    ids: { trakt: show },
                    seasons: [{ number: season, episodes: [{ number: episode, rating: rate }] }]
                };
                return data;
            }
        }
    }

    static getNextEpisode(showDetails: Api.Entity<TMDB.ShowDetails>, season: number, episode: number) {
        if (!showDetails) {
            return null;
        }
        const currentSeason = ShowHelper.getSeason(showDetails.attributes.seasons, season);
        const nextSeason = ShowHelper.getSeason(showDetails.attributes.seasons, season + 1);
        if (currentSeason && currentSeason.episode_count > episode) {
            return { season, episode: episode + 1 };
        }
        if (nextSeason && currentSeason.episode_count === episode) {
            return { season: season + 1, episode: 1 };
        }

        return null;
    }

    static getPreviousEpisode(showDetails: Api.Entity<TMDB.ShowDetails>, season: number, episode: number) {
        if (!showDetails) {
            return null;
        }
        const currentSeason = ShowHelper.getSeason(showDetails.attributes.seasons, season);
        const previousSeaon = ShowHelper.getSeason(showDetails.attributes.seasons, season - 1);

        if (currentSeason && episode > 1) {
            return { season, episode: episode - 1 };
        }
        if (previousSeaon && episode === 1) {
            return { season: season - 1, episode: previousSeaon.episode_count };
        }

        return null;
    }

    static sortShows(
        shows: Api.Entity<Trakt.Show, Trakt.ShowMeta>[],
        sorting: Trakt.ListSorting,
        dir: Trakt.SortDirection
    ): Api.Entity<Trakt.Show, {}>[] {
        switch (sorting) {
            case 'title':
                return ArrayHelper.sortArray(shows, show => ShowHelper.getSortTitle(show.attributes.title), dir);
            case 'added':
                return ArrayHelper.sortArray(shows, show => show.meta.listed_at, dir);
            case 'collected':
                return ArrayHelper.sortArray(shows, show => show.meta.collected_count, dir);
            case 'popularity':
                return ArrayHelper.sortArray(shows, show => show.attributes.rating, dir);
            case 'rank':
                return shows;
            case 'released':
                return ArrayHelper.sortArray(shows, show => show.attributes.first_aired, dir);
            case 'runtime':
                return ArrayHelper.sortArray(shows, show => show.attributes.runtime, dir);
            case 'votes':
                return ArrayHelper.sortArray(shows, show => show.attributes.votes, dir);
            case 'watched':
                return ArrayHelper.sortArray(shows, show => show.meta.watchers, dir);
            case 'random':
                return ArrayHelper.sortArray(shows, () => Math.random(), dir);
            default:
                break;
        }
        return shows;
    }

    static getTotalEpisodes(seasons: TMDB.Season[]): number {
        if (!seasons) {
            return 0;
        }
        let episodes = 0;
        seasons.forEach(season => {
            if (season.season_number > 0) {
                episodes += season.episode_count;
            }
        });
        return episodes;
    }
}
