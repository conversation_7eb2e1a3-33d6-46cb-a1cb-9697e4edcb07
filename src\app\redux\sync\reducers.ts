import { RESET_STORE } from 'app/app/redux/app/actions';
import { combineReducers } from 'redux';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import {
    SyncAction,
    SYNC_FAVORITES_REQUEST,
    SYNC_FAVORITES_SUCCESS,
    SYNC_FAVORITES_ERROR,
    GET_MY_CALENDAR_SUCCESS,
    GET_MY_CALENDAR_REQUEST,
    GET_MY_CALENDAR_ERROR,
    GET_LAST_ACTIVITY_REQUEST,
    GET_LAST_ACTIVITY_SUCCESS,
    RESET_LAST_ACTIVITY,
    UPDATE_LAST_ACTIVITY_SUCCESS
} from './actions';
import { REMOVE_FROM_FAVORITES_SUCCESS } from '../favorite/actions';
import { GET_COLLECTION_REQUEST, GET_COLLECTION_SUCCESS, GET_COLLECTION_ERROR } from './collection';
import {
    GET_WATCHED_REQUEST,
    GET_WATCHED_SUCCESS,
    GET_WATCHED_ERROR,
    GET_SHOW_PROGRESS_REQUEST,
    GET_SHOW_PROGRESS_SUCCESS,
    GET_SHOW_PROGRESS_ERROR
} from './watch';
import { GET_MY_RATINGS_REQUEST, GET_MY_RATINGS_SUCCESS, GET_MY_RATINGS_ERROR } from './rate';

export interface MyRatingsState {
    loading: boolean;
    fetched: number;
}

export const initialMyRatingsState: MyRatingsState = {
    loading: false,
    fetched: 0
};

export function myRatings(state: MyRatingsState = initialMyRatingsState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialMyRatingsState;
        case GET_MY_RATINGS_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_MY_RATINGS_SUCCESS: {
            return { loading: false, fetched: new Date().getTime() };
        }
        case GET_MY_RATINGS_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface CollectionState {
    loading: boolean;
    fetched: number;
}

export const initialCollectionState: CollectionState = {
    loading: false,
    fetched: 0
};

export function collection(state: CollectionState = initialCollectionState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialCollectionState;
        case GET_COLLECTION_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_COLLECTION_SUCCESS: {
            return { loading: false, fetched: new Date().getTime() };
        }
        case GET_COLLECTION_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface WatchedState {
    loading: boolean;
    fetched: number;
}

export const initialWatchedState: WatchedState = {
    loading: false,
    fetched: 0
};

export function watched(state: WatchedState = initialWatchedState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialWatchedState;
        case GET_WATCHED_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_WATCHED_SUCCESS: {
            return { loading: false, fetched: new Date().getTime() };
        }
        case GET_WATCHED_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface ShowProgressState {
    loading: boolean;
    [key: number]: { fetched: number };
}

export const initialShowProgressState: ShowProgressState = {
    loading: false
};

export function showProgress(state: ShowProgressState = initialShowProgressState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialShowProgressState;
        case GET_SHOW_PROGRESS_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_SHOW_PROGRESS_SUCCESS: {
            const { show } = action.payload;
            return { loading: false, ...state, [show]: { fetched: new Date().getTime() } };
        }
        case GET_SHOW_PROGRESS_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface NextEpisodeState {
    loading: boolean;
    [key: number]: number;
}

export const initialNextEpisodeState: NextEpisodeState = {
    loading: false
};

export function nextEpisode(state: NextEpisodeState = initialNextEpisodeState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialNextEpisodeState;
        case GET_SHOW_PROGRESS_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_SHOW_PROGRESS_SUCCESS: {
            const { normalized, show } = action.payload;
            if (!normalized.result) {
                const newState = { ...state };
                delete newState[show];
                return { loading: false, ...newState };
            }
            const episode = normalized.result as number;
            return { loading: false, ...state, [show]: { episode } };
        }
        case REMOVE_FROM_FAVORITES_SUCCESS: {
            const { show } = action.payload;
            const newState = { ...state };
            delete newState[show];
            return { loading: false, ...newState };
        }
        case GET_SHOW_PROGRESS_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface CalendarState {
    loading: boolean;
    result: number[];
}

export const initialCalendarState: CalendarState = {
    loading: false,
    result: []
};

export function calendar(state: CalendarState = initialCalendarState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialCalendarState;
        case GET_MY_CALENDAR_REQUEST: {
            return { ...state, loading: true };
        }
        case GET_MY_CALENDAR_SUCCESS: {
            const result = (action.payload.normalized.result as number[]).filter(v => !!v);
            return { loading: false, ...state, result };
        }
        case GET_MY_CALENDAR_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface SyncFavoritesState {
    loading: boolean;
    fetched: number;
}

export const initialSyncFavoritesState: SyncFavoritesState = {
    loading: false,
    fetched: 0
};

export function syncFavorites(state: SyncFavoritesState = initialSyncFavoritesState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialSyncFavoritesState;
        case SYNC_FAVORITES_REQUEST: {
            return { ...state, loading: true };
        }
        case SYNC_FAVORITES_SUCCESS: {
            return { loading: false, fetched: new Date().getTime() };
        }
        case SYNC_FAVORITES_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface ActivityState {
    loading: boolean;
    fetched: number;
    lastActivities: Trakt.LastActivities;
}

export const initialActivityState: ActivityState = {
    loading: false,
    fetched: 0,
    lastActivities: null
};

export function activity(state: ActivityState = initialActivityState, action: SyncAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialActivityState;
        case GET_LAST_ACTIVITY_REQUEST: {
            return { ...state, loading: true, fetched: 0 };
        }
        case GET_LAST_ACTIVITY_SUCCESS: {
            return {
                ...state,
                loading: false,
                fetched: new Date().getTime()
            };
        }
        case RESET_LAST_ACTIVITY:
        case UPDATE_LAST_ACTIVITY_SUCCESS: {
            return { ...state, lastActivities: action.payload.lastActivities };
        }
        case SYNC_FAVORITES_ERROR: {
            return { ...state, loading: false, fetched: 0 };
        }
        default:
            return state;
    }
}

export interface SyncState {
    activity: ActivityState;
    calendar: CalendarState;
    collection: CollectionState;
    myRatings: MyRatingsState;
    nextEpisode: NextEpisodeState;
    showProgress: ShowProgressState;
    syncFavorites: SyncFavoritesState;
    watched: WatchedState;
}

export const initialSyncState: SyncState = {
    activity: initialActivityState,
    calendar: initialCalendarState,
    collection: initialCollectionState,
    myRatings: initialMyRatingsState,
    nextEpisode: initialNextEpisodeState,
    showProgress: initialShowProgressState,
    syncFavorites: initialSyncFavoritesState,
    watched: initialWatchedState
};
export default combineReducers({
    activity,
    calendar,
    collection,
    myRatings,
    nextEpisode,
    showProgress,
    syncFavorites,
    watched
});
