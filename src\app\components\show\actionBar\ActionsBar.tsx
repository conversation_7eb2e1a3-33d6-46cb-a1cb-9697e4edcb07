import React, { useState, useEffect, useContext } from 'react';
import { View, Linking, Share } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import Constants from 'app/app/Constants';
import { useNavigation } from 'react-navigation-hooks';
import { ShowViewNavParams } from 'app/views/show/view';
import i18n from 'i18n-js';
import { LinearGradient } from 'expo-linear-gradient';
import Toast from 'react-native-toast-message';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { rate } from 'app/app/redux/sync/rate';
import useRating from 'app/app/hooks/useRating';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { ListedNavParams } from 'app/views/show/listed';
import useWatched from 'app/app/hooks/useWatched';
import { watchEpisode } from 'app/app/redux/sync/watch';
import { defaultOptions } from '../../elements/Toast';
import ActionRating from './components/ActionRating';
import styles from './styles';
import ActionShow from './components/ActionShow';
import ActionImdb from './components/ActionImdb';
import ActionShare from './components/ActionShare';
import ActionWatch from './components/ActionWatch';
import ActionList from './components/ActionList';
import ActionRate from './components/ActionRate';
import ActionCollect from './components/ActionCollect';

export const RATE_HEIGHT = 60;
export const ACTION_BAR_HEIGHT = 60;

export interface ActionBarProps {
    ids: Trakt.Ids;
    type: Trakt.RateType;
    season?: number;
    episode?: number;
    episodeImdbId?: string;
    listed?: number;
    imageHeight: number;
    totalEpisodes: number;
}

interface StateProps {
    isGuest: boolean;
    title: string;
    lists: number;
    markEpisodeViewed: boolean;
}

const ActionBar = ({
    ids,
    season,
    episode,
    episodeImdbId,
    listed,
    type,
    imageHeight,
    totalEpisodes
}: ActionBarProps) => {
    // REDUX
    const { percent } = useWatched(ids.trakt, season || -1, episode || -1);
    const dispatch = useDispatch();
    const initRating = useRating(type, ids.trakt, season, episode);
    const { isGuest, title, lists, markEpisodeViewed } = useSelector<MSState, StateProps>(state => {
        return {
            isGuest: state.auth.guest.isGuest,
            title: state.entities.show[ids.trakt]?.attributes.title || '',
            lists: state.lists.lists?.result.length || 0,
            markEpisodeViewed: state.app.markEpisodeViewed
        };
    });

    // STATE
    const [openRating, setOpenRating] = useState(false);
    const [rating, setRating] = useState(initRating);
    const [isImdbAvail, setImdbAvail] = useState(type === 'episodes' ? !!episodeImdbId : !!ids.imdb);

    // @ts-ignore
    const { navigate, push } = useNavigation();

    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    useEffect(() => {
        setRating(initRating);
    }, [initRating]);

    // EFFECTS
    useEffect(() => {
        if (type === 'episodes' && !!episodeImdbId) {
            setImdbAvail(true);
        }
    }, [episodeImdbId]);

    // EVENTS
    const getShareUrl = () => {
        const imdbId = ids.imdb;
        if (type === 'episodes') {
            return `${Constants.Api.Imdb.SHOW_URL}${episodeImdbId}`;
        }
        if (type === 'seasons') {
            return `${Constants.Api.Imdb.SHOW_URL}${imdbId}/episodes?season=${season}`;
        }
        return `${Constants.Api.Imdb.SHOW_URL}${imdbId}`;
    };

    const onImdbPress = () => {
        const uri = getShareUrl();
        Linking.canOpenURL(uri).then(can => {
            if (can) {
                Linking.openURL(uri);
            }
        });
    };
    const onShowPress = () => {
        const params: ShowViewNavParams = { ids };
        navigate(Constants.Navigation.Show.VIEW, params);
    };
    const onSharePress = async () => {
        const url = getShareUrl();
        try {
            const share = await Share.share({ url, title, message: url });
            if (share.action === Share.sharedAction) {
                // console.log({ share });
            } else if (share.action === Share.dismissedAction) {
                // console.log({ share });
            }
        } catch (error) {
            // console.log({ error });
        }
    };
    const onListPress = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('MANAGE_LIST_WARNING')
            });
        } else if (lists === 0) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('MANAGE_LIST_NO_LISTS_WARNING')
            });
        } else {
            const params: ListedNavParams = { id: ids.trakt };
            push(Constants.Navigation.Show.LISTED, params);
        }
    };

    const onHeartPress = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('RATE_WARNING')
            });
        } else {
            setOpenRating(!openRating);
        }
    };

    const onRateChange = (rating: number) => {
        const data = ShowHelper.createRateData(type, ids.trakt, season, episode, rating);
        dispatch(rate(type, data));
        setRating(rating);
        setOpenRating(!openRating);
        if (type === 'episodes' && markEpisodeViewed && percent === 0) {
            dispatch(watchEpisode(ids.trakt, season, episode));
        }
    };

    // PROPS
    const gradient =
        scheme === 'dark'
            ? ['#00000000', '#00000099', '#000000AA', '#000000DD', '#000000FF']
            : ['#FFFFFF00', '#FFFFFF99', '#FFFFFFAA', '#FFFFFFDD', '#FFFFFFFF'];

    return (
        <>
            <ActionRating
                {...{ coverImageHeight: imageHeight, isGuest, rating, onRateChange, openRating, onHeartPress }}
            />
            <View style={{ ...styles.container }}>
                <LinearGradient
                    pointerEvents="none"
                    colors={gradient}
                    locations={[0, 0.1, 0.2, 0.6, 1]}
                    style={styles.gradient}
                />
                <View>{type !== 'shows' && <ActionShow {...{ onShowPress }} />}</View>
                <View style={{ ...styles.right }}>
                    <ActionImdb {...{ isImdbAvail, onImdbPress }} />
                    <ActionShare {...{ isImdbAvail, onSharePress }} />
                    <ActionCollect {...{ isGuest, ids, season, episode, type, totalEpisodes }} />
                    {type === 'shows' && <ActionList {...{ listed, onListPress, isGuest, lists }} />}
                    <ActionRate {...{ onHeartPress, isGuest, rating }} />
                    <ActionWatch {...{ isGuest, ids, season, episode, type }} />
                </View>
            </View>
        </>
    );
};

ActionBar.defaultProps = {
    episode: null,
    season: null,
    episodeImdbId: null,
    listed: 10
};

export default React.memo(ActionBar);
