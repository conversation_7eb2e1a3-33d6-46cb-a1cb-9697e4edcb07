import React, { useContext } from 'react';
import { Image as RNEImage, StyleProp, ImageStyle, View, ImageURISource, ImageResizeMode } from 'react-native';
import FastImage from 'expo-fast-image';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import _ from 'lodash';
import { Theme } from 'app/app/styles/themes';

export interface ImageProps {
    uri: ImageURISource | number;
    width: number;
    height: number;
    rounded: boolean;
    border: boolean;
    cache: boolean;
    resizeMode: ImageResizeMode;
    backgroundColor?: string;
}

const Image = ({ uri, width, height, rounded, border, cache, resizeMode, backgroundColor }: ImageProps) => {
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    const style: StyleProp<ImageStyle> = {
        width,
        height,
        borderColor: colors.discreet,
        borderWidth: border ? 1 : 0,
        borderRadius: rounded ? width / 2 : 5,
        backgroundColor: backgroundColor || colors.background
    };
    const tint = scheme as 'light' | 'dark';
    return (
        <View style={{ width, height }}>
            {_.isNumber(uri) || !cache ? (
                <RNEImage source={uri} style={[style]} resizeMode={resizeMode} />
            ) : (
                <FastImage
                    {...{
                        tintColor: tint,
                        style: { ...style },
                        source: {
                            uri: uri.uri,
                            cache: 'force-cache',
                            headers: {
                                'Cache-Control': 'max-age=31536000'
                            }
                        }
                    }}
                />
            )}
        </View>
    );
};
Image.defaultProps = {
    rounded: false,
    border: true,
    cache: true,
    resizeMode: 'cover'
};

export default React.memo(Image, _.isEqual);
