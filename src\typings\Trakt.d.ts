namespace Trakt {
    type Period = 'weekly' | 'monthly' | 'yearly' | 'all';
    type ShowStatus = 'returning series' | 'in production' | 'planned' | 'canceled' | 'ended';
    type Privacy = 'private' | 'friends' | 'public';
    type RateType = 'shows' | 'seasons' | 'episodes' | 'movies';
    type Gender = 'male' | 'female' | 'other' | '';
    type Location = string;
    type ListSorting =
        | 'rank'
        | 'added'
        | 'title'
        | 'released'
        | 'runtime'
        | 'popularity'
        // | 'percentage'
        | 'votes'
        // | 'my_rating'
        | 'random'
        | 'watched'
        | 'collected';
    type SortDirection = 'asc' | 'desc';
    type CommentsSort = 'newest' | 'oldest' | 'likes' | 'replies' | 'highest' | 'lowest' | 'plays' | 'watched';
    interface Ids {
        slug: string;
        trakt: number;
        tvdb: number;
        imdb: string;
        tmdb: number;
        tvrage: number;
    }
    interface Token {
        access_token: string;
        token_type: 'Bearer';
        expires_in: number;
        refresh_token: string;
        scope: 'public';
        created_at: number;
    }
    interface CustomList {
        allow_comments: boolean;
        comment_count: number;
        created_at: string;
        description: string;
        display_numbers: boolean;
        ids: { trakt: number; slug: string };
        item_count: number;
        likes: number;
        name: string;
        privacy: Privacy;
        sort_by: ListSorting;
        sort_how: SortDirection;
        updated_at: string;
    }

    interface BasicShow {
        title: string;
        year: number;
        ids: Ids;
    }

    interface Show extends BasicShow {
        overview: string;
        first_aired: string;
        airs: { day: string; time: string; timezone: string };
        runtime: number;
        certification: string;
        network: string;
        country: string;
        trailer: string;
        homepage: string;
        status: Trakt.ShowStatus;
        rating: number;
        votes: number;
        comment_count: number;
        updated_at: string;
        language: string;
        available_translations: string[];
        genres: string[];
        aired_episodes: number;
        watchers?: number;
        fetched: number;
    }

    interface ShowMeta {
        watchers: number;
        watcher_count: number;
        play_count: number;
        collected_count: number;
        collector_count: number;
        listed_at: string;
        rank: number;
        list_count: number;
    }

    interface UserImages {
        avatar: {
            full: string;
        };
    }

    interface BasicUser {
        username: string;
        private: boolean;
        name: string;
        vip: boolean;
        vip_ep: boolean;
        ids: {
            slug: string;
        };
    }

    interface User extends BasicUser {
        joined_at: string;
        location: Location;
        about: string;
        gender: Gender;
        age: number;
        images: UserImages;
        vip_og: boolean;
        vip_years: number;
    }

    interface Account {
        timezone: string;
        date_format: string;
        time_24hr: boolean;
        cover_image: string;
        token: string;
    }
    interface Connections {
        facebook: boolean;
        twitter: boolean;
        google: boolean;
        tumblr: boolean;
        medium: boolean;
        slack: boolean;
    }
    interface SharingText {
        watching: string;
        watched: string;
        rated: string;
    }
    interface UserSettings {
        user: User;
        account: Account;
        connections: Connections;
        sharing_text: SharingText;
    }

    interface CustomListForm {
        name: string;
        description: string;
        privacy: Privacy;
        display_numbers: boolean;
        allow_comments: boolean;
        sort_by: ListSorting;
        sort_how: SortDirection;
    }

    interface MyRating {
        rating: number;
    }

    interface RateData {
        shows: [
            {
                rating?: number;
                ids: { trakt: number };
                seasons?: [
                    {
                        rating?: number;
                        number: number;
                        episodes?: [
                            {
                                rating: number;
                                number: number;
                            }
                        ];
                    }
                ];
            }
        ];
    }

    interface WatchedItem {
        show: number;
        season: number;
        episode: number;
    }

    interface NextEpisode {
        ids: Trakt.Ids;
        season: number;
        number: number;
        airDate: string;
        title: string;
        show: {
            ids: Trakt.ids;
            title: string;
            year: number;
        };
    }

    interface Certification {
        name: string;
        slug: string;
        description: string;
    }

    interface Genre {
        name: string;
        slug: string;
    }

    interface LastActivity {
        rated_at: string;
        watchlisted_at: string;
        commented_at: string;
    }

    interface MovieActivity extends LAstActivity {
        hidden_at: string;
        watched_at: string;
        collected_at: string;
        paused_at: string;
    }

    interface EpisodeActivity extends LastActivity {
        watched_at: string;
        collected_at: string;
        paused_at: string;
    }
    interface ShowActivity extends LastActivity {
        hidden_at: string;
    }

    interface SeasonActivity extends LastActivity {
        hidden_at: string;
    }

    interface LastActivities {
        all: string;
        movies: MovieActivity;
        episodes: EpisodeActivity;
        shows: ShowActivity;
        seasons: SeasonActivity;
        comments: {
            liked_at: string;
        };
        lists: {
            liked_at: string;
            commented_at: string;
            updated_at: string;
        };
        account: {
            settings_at;
        };
    }

    interface Sharing {
        twitter: boolean;
        tumblr: boolean;
        medium: boolean;
    }

    interface Comment {
        id: number;
        parent_id: number;
        created_at: string;
        updated_at: string;
        comment: string;
        spoiler: boolean;
        review: boolean;
        replies: number;
        likes: number;
        user_rating: number;
        user: BasicUser;
        sharing?: Sharing;
    }

    interface CommentForm {
        show?: { ids: { trakt } };
        season?: { ids: { trakt } };
        episode?: { ids: { trakt } };
        list?: { ids: { trakt } };
        comment: string;
        spoiler: boolean;
        sharing?: Sharing;
    }

    interface BasicSeason {
        number: number;
        ids: Trakt.ids;
    }

    interface Season extends BasicSeason {}

    interface BasicEpisode {
        season: number;
        number: number;
        title: string;
        ids: Trakt.Ids;
    }
    interface Episode extends BasicEpisode {}

    interface Stats {
        watchers: number;
        plays: number;
        collectors: number;
        collected_episodes: number;
        comments: number;
        lists: number;
        votes: number;
    }

    interface UserItemStat {
        plays?: number;
        watched: number;
        minutes?: number;
        collected: number;
        ratings: number;
        comments: number;
    }

    interface UserRatings {
        total: number;
        distribution: {
            [key: string]: number;
        };
    }

    interface UserStats {
        movies: UserItemStat;
        shows: UserItemStat;
        seasons: {
            ratings: number;
            comments: number;
        };
        episodes: UserItemStat;
        network: {
            friends: number;
            followers: number;
            following: number;
        };
        ratings: UserRatings;
    }

    interface EpisodeRating {
        episode: BasicEpisode;
        rated_at: string;
        rating: number;
        show: BasicShow;
        type: 'episode';
    }
}
