{
    "expo": {
        "name": "App",
        "slug": "App",
        "privacy": "public",
        "platforms": ["ios", "android", "web"],
        "version": "1.0.0",
        "orientation": "portrait",
        "icon": "./assets/icon.png",
        "scheme": "myscheme",
        "splash": {
            "image": "./assets/splash.png",
            "resizeMode": "contain",
            "backgroundColor": "#ffffff"
        },
        "packagerOpts": {
            "config": "metro.config.js",
            "sourceExts": ["js", "jsx", "svg", "ts", "tsx"]
        },
        "updates": {
            "fallbackToCacheTimeout": 0
        },
        "assetBundlePatterns": ["**/*"],
        "ios": {
            "supportsTablet": true,
            "bundleIdentifier": "com.kremalagame.series",
            "config":{
              "googleMobileAdsAppId":"ADMOD_APP_ID"
            }         
        },
        "android": {
            "package": "com.kremalagame.series",
            "versionCode": 1,
            "config":{
              "googleMobileAdsAppId":"ADMOD_APP_ID"
            }         
        },
        "androidNavigationBar":{
            "visible":"leanback"
        }
        "userInterfaceStyle": "automatic",
        "extra" :{
            "sentry" :{
                "dsn":"Sentry DSN"              
            },
            "trakt":{
                "staging":{
                    "client_id":"TRAKT CLIENT ID",
                    "client_secret":"TRAKT CLIENT SECRET",
                },
                "live":{
                    "client_id":"TRAKT CLIENT ID",
                    "client_secret":"TRAKT CLIENT SECRET",
                }
            },
            "tmdb":{
                 "api_key":"d47221d928a993203f9a873d6fe310b4"
             },
        },
        "hooks": {
            "postPublish": [
                {
                    "file": "sentry-expo/upload-sourcemaps",
                    "config": {
                        "organization": "your organization's short name here",
                        "project": "your project name here",
                        "authToken": "your auth token here"                        
                    }
                }
            ]
        }
    }
}
