import Constants from 'expo-constants';
import { Threshold } from 'app/services/helpers/ApiHelper';

const useStaging = false;
const DEV = __DEV__ && useStaging;
export type thresholds = 'NOW' | 'LOWEST' | 'LOWER' | 'LOW' | 'HIGH' | 'HIGHER' | 'HIGHEST' | 'NEVER';
export const UpdateThresholds: { [key in thresholds]: Threshold } = {
    NOW: { amount: 0, unit: 'seconds' },
    LOWEST: { amount: 30, unit: 'minutes' },
    LOWER: { amount: 1, unit: 'hours' },
    LOW: { amount: 6, unit: 'hours' },
    HIGH: { amount: 12, unit: 'hours' },
    HIGHER: { amount: 1, unit: 'day' },
    HIGHEST: { amount: 7, unit: 'days' },
    NEVER: { amount: 15, unit: 'days' }
};
export default {
    Navigation: {
        Auth: {
            AUTH: 'Auth',
            CHECK: 'AuthLoad',
            SIGN_IN: 'SignIn',
            INIT: 'Init',
            SELECT_SETTINGS: 'SelectSettings'
        },
        Calendar: {
            VIEW: 'Calendar'
        },
        Comment: {
            LIST: 'Comments',
            REPLIES: 'Replies',
            POST: 'PostComment'
        },
        Database: {
            VIEW: 'Database'
        },
        Episode: {
            VIEW: 'Episode'
        },
        Lists: {
            View: 'Lists',
            SHOWS: 'ListShows',
            MANAGE: 'Manage'
        },
        Main: {
            STACK: 'App'
        },

        People: {
            VIEW: 'People'
        },
        Search: {
            VIEW: 'Search',
            TRENDING: 'Trending',
            POPULAR: 'Popular',
            RECOMMENDED: 'Recommended',
            WATCHED: 'Watched',
            ANTICIPATED: 'Anticipated'
        },
        Season: {
            VIEW: 'Season'
        },
        Settings: {
            ADVANCED: 'Advanced',
            VIEW: 'Settings',
            USER: 'User',
            STATS: 'UserStats',
            RATINGS: 'Ratings',
            LATEST_RATINGS: 'LatestRatings'
        },
        Show: {
            VIEW: 'Show',
            LISTED: 'Listed'
        },
        Stats: {
            VIEW: 'Stats'
        },
        Watching: {
            VIEW: 'Watching'
        }
    },
    Api: {
        Trakt: {
            ENDPOINT: DEV ? 'https://api-staging.trakt.tv' : 'https://api.trakt.tv',
            AUTH: DEV ? 'https://staging.trakt.tv/oauth/authorize' : 'https://api.trakt.tv/oauth/authorize',
            REDIRECT_URL: 'https://auth.expo.io/@aekdb/mySeries',
            CLIENT_ID: DEV
                ? Constants.manifest.extra.trakt.staging.client_id
                : Constants.manifest.extra.trakt.live.client_id,
            CLIENT_SECRET: DEV
                ? Constants.manifest.extra.trakt.staging.client_secret
                : Constants.manifest.extra.trakt.live.client_secret,
            RESPONSE_TYPE: 'code',
            GRANT_TYPE_AUTH: 'authorization_code',
            GRANT_TYPE_REFRESH: 'refresh_token'
        },
        Tmdb: {
            ENDPOINT: 'https://api.themoviedb.org/3',
            API_KEY: Constants.manifest.extra.tmdb.api_key,
            IMAGE_TYPES: {
                BACKDROP: 'backdrops',
                LOGO: 'logos',
                POSTER: 'posters',
                STILL: 'stills',
                PROFILE: 'profiles'
            },
            IMAGES: {
                BASE_URL: 'http://image.tmdb.org/t/p/',
                SECURE_BASE_URL: 'https://image.tmdb.org/t/p/'
            }
        },
        Omdb: {
            ENDPOINT: 'https://www.omdbapi.com',
            API_KEY: Constants.manifest.extra.omdb.api_key
        },
        Imdb: {
            PEOPLE_URL: 'https://www.imdb.com/name/',
            SHOW_URL: 'https://www.imdb.com/title/'
        }
    },
    App: {
        LIST_TEASER_ITEMS: 3,
        CARD_TEASER_ITEMS: 7,
        SENTRY_DNS: Constants.manifest.extra.sentry.dsn,
        AdMob: {
            SHOW_BANNER: false,
            ANDROID: {
                BANNER: DEV ? 'ca-app-pub-3940256099942544/6300978111' : Constants.manifest.extra.admob.android.banner
            }
        },
        NO_SAFE_AREAVIEW: true
    }
};
