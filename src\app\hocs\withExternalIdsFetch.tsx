import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Linking } from 'react-native';
import { MSState } from '../redux/redux';
import { getPersonExternalIds } from '../redux/person/actions';
import Constants from '../Constants';

export interface ExternalIdsFetchProps {
    onPress: () => void;
}
const withExternalIdsFetch = BaseComponent => (props: { id: number }) => {
    const [pressed, setPressed] = useState(false);
    const { id } = props;
    const dispatch = useDispatch();
    const imdb = useSelector((state: MSState) => state.entities.externalIds[id]?.attributes.imdb_id);
    const onPress = () => {
        if (!imdb) {
            dispatch(getPersonExternalIds(id));
        }
        setPressed(true);
    };

    useEffect(() => {
        if (pressed && imdb) {
            const url = `${Constants.Api.Imdb.PEOPLE_URL}${imdb}`;
            Linking.canOpenURL(url).then(can => {
                if (can) {
                    Linking.openURL(url);
                }
            });
        }
    }, [imdb, pressed]);

    return <BaseComponent {...props} {...{ imdb, onPress }} />;
};

export default withExternalIdsFetch;
