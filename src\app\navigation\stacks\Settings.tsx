import Constants from 'app/app/Constants';
import Settings from 'views/settings/view';
import User from 'views/settings/user';
import { createStackNavigator } from 'react-navigation-stack';
import { dark, light } from 'app/app/styles/themes';
import Database from 'app/views/settings/database';
import UserStats from 'views/settings/stats';
import TotalRatings from 'views/settings/ratings/total';
import Advanced from 'app/views/settings/advanced';
import LatestRatings from 'views/settings/ratings/latest';
import TransitionConfiguration from '../transitions';
import Common from './Common';

export default createStackNavigator(
    {
        [Constants.Navigation.Settings.VIEW]: Settings,
        [Constants.Navigation.Settings.USER]: User,
        [Constants.Navigation.Settings.ADVANCED]: {
            screen: Advanced,
            params: { noTabBar: true }
        },
        [Constants.Navigation.Database.VIEW]: {
            screen: Database,
            params: { noTabBar: true }
        },
        [Constants.Navigation.Settings.STATS]: {
            screen: UserStats,
            params: { noTabBar: true, transition: 'slideUp' }
        },
        [Constants.Navigation.Settings.RATINGS]: {
            screen: TotalRatings,
            params: { noTabBar: true, transition: 'slideUp' }
        },
        [Constants.Navigation.Settings.LATEST_RATINGS]: {
            screen: LatestRatings,
            params: { noTabBar: true, transition: 'slideUp' }
        },
        ...Common
    },
    {
        headerMode: 'none',
        defaultNavigationOptions: ({ theme }) => {
            const colors = theme === 'dark' ? dark : light;
            return { headerStyle: { backgroundColor: colors.background } };
        },
        transitionConfig: TransitionConfiguration
    }
);
