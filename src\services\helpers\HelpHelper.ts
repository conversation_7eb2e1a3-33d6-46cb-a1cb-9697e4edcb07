import Storage from '../storage/Storage';

type Help = 'swipeShow' | 'swipeList' | 'show';

type HelpCollection = {
    [key in Help]?: boolean;
};
export default class HelpHelper {
    static async getHelp(help: Help, autoUpdate: boolean = true): Promise<boolean> {
        return Storage.getItem(Storage.KEY_HELPER)
            .then((helps: HelpCollection) => {
                const value = helps?.[help];
                if (!value) {
                    return { value: true, helps };
                }
                return { value: false, helps };
            })
            .then(({ value, helps }) => {
                if (autoUpdate) {
                    const newHelps = { ...helps };
                    newHelps[help] = true;
                    Storage.setItem(Storage.KEY_HELPER, newHelps);
                }
                return value;
            });
    }
}
