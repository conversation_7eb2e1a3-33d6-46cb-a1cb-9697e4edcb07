import { createStackNavigator } from 'react-navigation-stack';
import SignIn from 'app/views/auth/SignInView';
import Init from 'views/auth/Init';
import SelectSettings from 'app/views/auth/SelectSettings';
import Constants from '../Constants';

const AuthStack = createStackNavigator(
    {
        [Constants.Navigation.Auth.SIGN_IN]: SignIn,
        [Constants.Navigation.Auth.INIT]: Init,
        [Constants.Navigation.Auth.SELECT_SETTINGS]: SelectSettings
    },
    {
        initialRouteKey: 'signIn',
        headerMode: 'none'
    }
);
export default AuthStack;
