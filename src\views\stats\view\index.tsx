import React, { useEffect } from 'react';
import { NavigationInjectedProps } from 'react-navigation';
import { NavParams } from 'app/app/navigation/Navigator';
import { useDispatch, useSelector } from 'react-redux';
import { getShowStats, getSeasonStats, getEpisodeStats, clearStats } from 'app/app/redux/stats/actions';
import { MSState } from 'app/app/redux/redux';
import { Header, ListItem } from 'react-native-elements';
import Back from 'app/app/components/header/Back';
import Container from 'app/app/components/containers/Container';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import i18n from 'i18n-js';
import StringHelper from 'app/services/helpers/StringHelper';

export interface StatsNavParams extends NavParams {
    id: number;
    seasonNumber?: number;
    episodeNumber?: number;
}

const Stats = ({ navigation }: NavigationInjectedProps<StatsNavParams>) => {
    const { id, seasonNumber, episodeNumber, title } = navigation.state.params;
    const dispatch = useDispatch();

    const stats = useSelector((state: MSState) => state.stats.item.stats);

    useEffect(() => {
        if (episodeNumber > 0) {
            dispatch(getEpisodeStats(id, seasonNumber, episodeNumber));
        } else if (seasonNumber > 0) {
            dispatch(getSeasonStats(id, seasonNumber));
        } else {
            dispatch(getShowStats(id));
        }
        return () => {
            dispatch(clearStats());
        };
    }, []);
    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: title }} />
            <Container withPadding={false}>
                {stats ? (
                    Object.keys(stats).map(key => {
                        const value: number = stats[key];
                        return <ListItem key={key} title={i18n.t(key)} rightTitle={StringHelper.formatNumber(value)} />;
                    })
                ) : (
                    <ScreenLoader />
                )}
            </Container>
        </>
    );
};

export default React.memo(Stats);
