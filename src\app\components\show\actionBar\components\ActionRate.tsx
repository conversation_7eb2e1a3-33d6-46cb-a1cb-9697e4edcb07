import React from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Icon, Badge } from 'react-native-elements';
import { common } from 'app/app/styles/themes';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { View } from 'react-native';
import styles from '../styles';

export interface ActionRateProps {
    onHeartPress: () => void;
    rating: number;
    isGuest: boolean;
}

const ActionRate = ({ rating, onHeartPress, isGuest }: ActionRateProps) => {
    const opacity = isGuest ? 0.3 : 1;

    return (
        <TouchableOpacity onPress={onHeartPress}>
            <Icon
                Component={View}
                name={rating ? 'heart' : 'heart-o'}
                type="font-awesome"
                color={common.red}
                containerStyle={{ ...styles.icon, opacity }}
                size={ICON_SIZE.medium}
            />
            {rating > 0 && (
                <Badge
                    value={rating}
                    containerStyle={{
                        ...styles.ratingBadgeContainer
                    }}
                    badgeStyle={{
                        ...styles.ratingBadge
                    }}
                    textStyle={{ ...styles.ratingBadgeText }}
                />
            )}
        </TouchableOpacity>
    );
};

export default React.memo(ActionRate);
