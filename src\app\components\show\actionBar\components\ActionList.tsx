import React from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Icon, Badge } from 'react-native-elements';
import { common } from 'app/app/styles/themes';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { View } from 'react-native';
import styles from '../styles';

export interface ActionListProps {
    onListPress: () => void;
    listed: number;
    isGuest: boolean;
    lists: number;
}

const ActionList = ({ isGuest, lists, listed, onListPress }: ActionListProps) => {
    const opacity = isGuest || lists === 0 ? 0.3 : 1;

    return (
        <TouchableOpacity onPress={onListPress}>
            <Icon
                Component={View}
                name="list"
                type="font-awesome"
                color={common.blue}
                containerStyle={{ ...styles.icon, opacity }}
                size={ICON_SIZE.medium}
            />
            {listed > 0 && (
                <Badge
                    value={listed}
                    containerStyle={{
                        ...styles.listBadgeContainer
                    }}
                    badgeStyle={{
                        ...styles.listBadge
                    }}
                    status="primary"
                    textStyle={{ ...styles.listBadgeText }}
                />
            )}
        </TouchableOpacity>
    );
};

export default React.memo(ActionList);
