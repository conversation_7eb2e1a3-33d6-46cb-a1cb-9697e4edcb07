Arguments: 
  /usr/local/bin/node /usr/local/Cellar/yarn/1.17.3/libexec/bin/yarn.js add @types/react-native-chart-kit

PATH: 
  /usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/platform-tools

Yarn version: 
  1.17.3

Node version: 
  12.5.0

Platform: 
  darwin x64

Trace: 
  Error: https://registry.npmjs.org/@types%2freact-native-chart-kit: Not found
      at Request.params.callback [as _callback] (/usr/local/Cellar/yarn/1.17.3/libexec/lib/cli.js:66830:18)
      at Request.self.callback (/usr/local/Cellar/yarn/1.17.3/libexec/lib/cli.js:140464:22)
      at Request.emit (events.js:200:13)
      at Request.<anonymous> (/usr/local/Cellar/yarn/1.17.3/libexec/lib/cli.js:141436:10)
      at Request.emit (events.js:200:13)
      at IncomingMessage.<anonymous> (/usr/local/Cellar/yarn/1.17.3/libexec/lib/cli.js:141358:12)
      at Object.onceWrapper (events.js:288:20)
      at IncomingMessage.emit (events.js:205:15)
      at endReadableNT (_stream_readable.js:1154:12)
      at processTicksAndRejections (internal/process/task_queues.js:74:11)

npm manifest: 
  {
      "main": "node_modules/expo/AppEntry.js",
      "scripts": {
          "start": "expo start",
          "android": "expo start --android",
          "ios": "expo start --ios",
          "web": "expo start --web",
          "eject": "expo eject",
          "prettier:write": "npx prettier --write **/*.{js,jsx,ts,tsx,json} && npx prettier --write *.{js,jsx,ts,tsx,json}",
          "lint": "tsc --noEmit && eslint --ext .js,.jsx,.ts,.tsx ./"
      },
      "dependencies": {
          "@expo/react-native-action-sheet": "^3.7.0",
          "@ptomasroos/react-native-multi-slider": "^2.2.2",
          "@react-native-community/masked-view": "0.1.6",
          "@react-native-community/netinfo": "5.5.1",
          "@react-native-community/viewpager": "3.3.0",
          "@welldone-software/why-did-you-render": "^4.1.2",
          "expo": "^37.0.0",
          "expo-ads-admob": "~8.1.0",
          "expo-auth-session": "^1.0.1",
          "expo-blur": "~8.1.0",
          "expo-constants": "~9.0.0",
          "expo-file-system": "~8.1.0",
          "expo-linear-gradient": "~8.1.0",
          "expo-localization": "~8.1.0",
          "expo-sqlite": "~8.1.0",
          "expo-updates": "~0.1.2",
          "i18n-js": "^3.5.1",
          "lodash": "^4.17.15",
          "moment": "^2.24.0",
          "node-emoji": "^1.10.0",
          "normalizr": "^3.6.0",
          "query-string": "^6.12.1",
          "react": "16.9.0",
          "react-dom": "16.9.0",
          "react-native": "https://github.com/expo/react-native/archive/sdk-37.0.1.tar.gz",
          "react-native-appearance": "~0.3.3",
          "react-native-chart-kit": "^5.6.0",
          "react-native-elements": "^2.0.0",
          "react-native-expo-image-cache": "^4.1.0",
          "react-native-gesture-handler": "~1.6.0",
          "react-native-reanimated": "~1.7.0",
          "react-native-redash": "12.1.0",
          "react-native-safe-area-context": "0.7.3",
          "react-native-screens": "~2.2.0",
          "react-native-snap-carousel": "^3.9.0",
          "react-native-star-rating": "^1.1.0",
          "react-native-status-bar-height": "^2.5.0",
          "react-native-svg": "11.0.1",
          "react-native-tab-view": "^2.14.0",
          "react-native-toast-message": "^1.3.0",
          "react-native-web": "^0.11.7",
          "react-navigation": "^4.0.10",
          "react-navigation-hooks": "^1.1.0",
          "react-navigation-stack": "1.9.3",
          "react-navigation-tabs": "^2.7.0",
          "react-redux": "^7.1.3",
          "redux": "^4.0.5",
          "redux-api-middleware": "^3.1.0",
          "redux-logger": "^3.0.6",
          "redux-persist": "^6.0.0",
          "redux-thunk": "^2.3.0",
          "reselect": "^4.0.0",
          "sentry-expo": "^2.0.3",
          "tinycolor2": "^1.4.1",
          "use-memo-one": "^1.1.1"
      },
      "devDependencies": {
          "@babel/core": "^7.0.0",
          "@babel/plugin-proposal-optional-chaining": "^7.9.0",
          "@react-native-community/eslint-config": "^0.0.6",
          "@react-native-community/eslint-plugin": "^1.0.0",
          "@types/i18n-js": "^3.0.1",
          "@types/react": "^16.9.11",
          "@types/react-native": "^0.60.22",
          "@types/react-native-snap-carousel": "^3.8.1",
          "@types/react-redux": "^7.1.5",
          "@types/redux-api-middleware": "^3.0.7",
          "@types/redux-logger": "^3.0.7",
          "@types/tinycolor2": "^1.4.2",
          "@typescript-eslint/eslint-plugin": "^2.14.0",
          "@typescript-eslint/parser": "^2.14.0",
          "babel-plugin-module-resolver": "^4.0.0",
          "babel-preset-expo": "^8.1.0",
          "eslint": "^6.8.0",
          "eslint-config-airbnb-typescript": "^6.3.1",
          "eslint-config-prettier": "^6.9.0",
          "eslint-import-resolver-alias": "^1.1.2",
          "eslint-plugin-import": "^2.19.1",
          "eslint-plugin-jsx-a11y": "^6.2.3",
          "eslint-plugin-prettier": "^3.1.2",
          "eslint-plugin-react": "^7.17.0",
          "husky": "^3.1.0",
          "prettier": "^1.19.1",
          "pretty-quick": "^2.0.1",
          "react-native-svg-transformer": "^0.14.3",
          "typescript": "^3.8.3"
      },
      "private": true,
      "husky": {
          "hooks": {
              "pre-commit": "pretty-quick --staged && npm run lint"
          }
      }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@babel/code-frame@^7.0.0", "@babel/code-frame@^7.5.5":
    version "7.5.5"
    resolved "https://registry.yarnpkg.com/@babel/code-frame/-/code-frame-7.5.5.tgz#bc0782f6d69f7b7d49531219699b988f669a8f9d"
    integrity sha512-27d4lZoomVyo51VegxI20xZPuSHusqbQag/ztrBC7wegWoQ1nLREPVSKSW8byhTlzTKyNE4ifaTA6lCp7JjpFw==
    dependencies:
      "@babel/highlight" "^7.0.0"
  
  "@babel/code-frame@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/code-frame/-/code-frame-7.8.3.tgz#33e25903d7481181534e12ec0a25f16b6fcf419e"
    integrity sha512-a9gxpmdXtZEInkCSHUJDLHZVBgb1QS0jhss4cPP93EW7s+uC5bikET2twEF3KV+7rDblJcmNvTR7VJejqd2C2g==
    dependencies:
      "@babel/highlight" "^7.8.3"
  
  "@babel/core@^7.0.0":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/core/-/core-7.7.7.tgz#ee155d2e12300bcc0cff6a8ad46f2af5063803e9"
    integrity sha512-jlSjuj/7z138NLZALxVgrx13AOtqip42ATZP7+kYl53GvDV6+4dCek1mVUo8z8c8Xnw/mx2q3d9HWh3griuesQ==
    dependencies:
      "@babel/code-frame" "^7.5.5"
      "@babel/generator" "^7.7.7"
      "@babel/helpers" "^7.7.4"
      "@babel/parser" "^7.7.7"
      "@babel/template" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
      convert-source-map "^1.7.0"
      debug "^4.1.0"
      json5 "^2.1.0"
      lodash "^4.17.13"
      resolve "^1.3.2"
      semver "^5.4.1"
      source-map "^0.5.0"
  
  "@babel/core@^7.4.5":
    version "7.9.0"
    resolved "https://registry.yarnpkg.com/@babel/core/-/core-7.9.0.tgz#ac977b538b77e132ff706f3b8a4dbad09c03c56e"
    integrity sha512-kWc7L0fw1xwvI0zi8OKVBuxRVefwGOrKSQMvrQ3dW+bIIavBY3/NpXmpjMy7bQnLgwgzWQZ8TlM57YHpHNHz4w==
    dependencies:
      "@babel/code-frame" "^7.8.3"
      "@babel/generator" "^7.9.0"
      "@babel/helper-module-transforms" "^7.9.0"
      "@babel/helpers" "^7.9.0"
      "@babel/parser" "^7.9.0"
      "@babel/template" "^7.8.6"
      "@babel/traverse" "^7.9.0"
      "@babel/types" "^7.9.0"
      convert-source-map "^1.7.0"
      debug "^4.1.0"
      gensync "^1.0.0-beta.1"
      json5 "^2.1.2"
      lodash "^4.17.13"
      resolve "^1.3.2"
      semver "^5.4.1"
      source-map "^0.5.0"
  
  "@babel/generator@^7.0.0", "@babel/generator@^7.7.4", "@babel/generator@^7.7.7":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/generator/-/generator-7.7.7.tgz#859ac733c44c74148e1a72980a64ec84b85f4f45"
    integrity sha512-/AOIBpHh/JU1l0ZFS4kiRCBnLi6OTHzh0RPk3h9isBxkkqELtQNFi1Vr/tiG9p1yfoUdKVwISuXWQR+hwwM4VQ==
    dependencies:
      "@babel/types" "^7.7.4"
      jsesc "^2.5.1"
      lodash "^4.17.13"
      source-map "^0.5.0"
  
  "@babel/generator@^7.9.0", "@babel/generator@^7.9.5":
    version "7.9.5"
    resolved "https://registry.yarnpkg.com/@babel/generator/-/generator-7.9.5.tgz#27f0917741acc41e6eaaced6d68f96c3fa9afaf9"
    integrity sha512-GbNIxVB3ZJe3tLeDm1HSn2AhuD/mVcyLDpgtLXa5tplmWrJdF/elxB56XNqCuD6szyNkDi6wuoKXln3QeBmCHQ==
    dependencies:
      "@babel/types" "^7.9.5"
      jsesc "^2.5.1"
      lodash "^4.17.13"
      source-map "^0.5.0"
  
  "@babel/helper-annotate-as-pure@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.7.4.tgz#bb3faf1e74b74bd547e867e48f551fa6b098b6ce"
    integrity sha512-2BQmQgECKzYKFPpiycoF9tlb5HA4lrVyAmLLVK177EcQAqjVLciUb2/R+n1boQ9y5ENV3uz2ZqiNw7QMBBw1Og==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-builder-binary-assignment-operator-visitor@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.7.4.tgz#5f73f2b28580e224b5b9bd03146a4015d6217f5f"
    integrity sha512-Biq/d/WtvfftWZ9Uf39hbPBYDUo986m5Bb4zhkeYDGUllF43D+nUe5M6Vuo6/8JDK/0YX/uBdeoQpyaNhNugZQ==
    dependencies:
      "@babel/helper-explode-assignable-expression" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-builder-react-jsx@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-builder-react-jsx/-/helper-builder-react-jsx-7.7.4.tgz#da188d247508b65375b2c30cf59de187be6b0c66"
    integrity sha512-kvbfHJNN9dg4rkEM4xn1s8d1/h6TYNvajy9L1wx4qLn9HFg0IkTsQi4rfBe92nxrPUFcMsHoMV+8rU7MJb3fCA==
    dependencies:
      "@babel/types" "^7.7.4"
      esutils "^2.0.0"
  
  "@babel/helper-call-delegate@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-call-delegate/-/helper-call-delegate-7.7.4.tgz#621b83e596722b50c0066f9dc37d3232e461b801"
    integrity sha512-8JH9/B7J7tCYJ2PpWVpw9JhPuEVHztagNVuQAFBVFYluRMlpG7F1CgKEgGeL6KFqcsIa92ZYVj6DSc0XwmN1ZA==
    dependencies:
      "@babel/helper-hoist-variables" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-create-class-features-plugin@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.7.4.tgz#fce60939fd50618610942320a8d951b3b639da2d"
    integrity sha512-l+OnKACG4uiDHQ/aJT8dwpR+LhCJALxL0mJ6nzjB25e5IPwqV1VOsY7ah6UB1DG+VOXAIMtuC54rFJGiHkxjgA==
    dependencies:
      "@babel/helper-function-name" "^7.7.4"
      "@babel/helper-member-expression-to-functions" "^7.7.4"
      "@babel/helper-optimise-call-expression" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-replace-supers" "^7.7.4"
      "@babel/helper-split-export-declaration" "^7.7.4"
  
  "@babel/helper-create-regexp-features-plugin@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.7.4.tgz#6d5762359fd34f4da1500e4cff9955b5299aaf59"
    integrity sha512-Mt+jBKaxL0zfOIWrfQpnfYCN7/rS6GKx6CCCfuoqVVd+17R8zNDlzVYmIi9qyb2wOk002NsmSTDymkIygDUH7A==
    dependencies:
      "@babel/helper-regex" "^7.4.4"
      regexpu-core "^4.6.0"
  
  "@babel/helper-define-map@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-define-map/-/helper-define-map-7.7.4.tgz#2841bf92eb8bd9c906851546fe6b9d45e162f176"
    integrity sha512-v5LorqOa0nVQUvAUTUF3KPastvUt/HzByXNamKQ6RdJRTV7j8rLL+WB5C/MzzWAwOomxDhYFb1wLLxHqox86lg==
    dependencies:
      "@babel/helper-function-name" "^7.7.4"
      "@babel/types" "^7.7.4"
      lodash "^4.17.13"
  
  "@babel/helper-explode-assignable-expression@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.7.4.tgz#fa700878e008d85dc51ba43e9fb835cddfe05c84"
    integrity sha512-2/SicuFrNSXsZNBxe5UGdLr+HZg+raWBLE9vC98bdYOKX/U6PY0mdGlYUJdtTDPSU0Lw0PNbKKDpwYHJLn2jLg==
    dependencies:
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-function-name@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-function-name/-/helper-function-name-7.7.4.tgz#ab6e041e7135d436d8f0a3eca15de5b67a341a2e"
    integrity sha512-AnkGIdiBhEuiwdoMnKm7jfPfqItZhgRaZfMg1XX3bS25INOnLPjPG1Ppnajh8eqgt5kPJnfqrRHqFqmjKDZLzQ==
    dependencies:
      "@babel/helper-get-function-arity" "^7.7.4"
      "@babel/template" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-function-name@^7.9.5":
    version "7.9.5"
    resolved "https://registry.yarnpkg.com/@babel/helper-function-name/-/helper-function-name-7.9.5.tgz#2b53820d35275120e1874a82e5aabe1376920a5c"
    integrity sha512-JVcQZeXM59Cd1qanDUxv9fgJpt3NeKUaqBqUEvfmQ+BCOKq2xUgaWZW2hr0dkbyJgezYuplEoh5knmrnS68efw==
    dependencies:
      "@babel/helper-get-function-arity" "^7.8.3"
      "@babel/template" "^7.8.3"
      "@babel/types" "^7.9.5"
  
  "@babel/helper-get-function-arity@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-get-function-arity/-/helper-get-function-arity-7.7.4.tgz#cb46348d2f8808e632f0ab048172130e636005f0"
    integrity sha512-QTGKEdCkjgzgfJ3bAyRwF4yyT3pg+vDgan8DSivq1eS0gwi+KGKE5x8kRcbeFTb/673mkO5SN1IZfmCfA5o+EA==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-get-function-arity@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-get-function-arity/-/helper-get-function-arity-7.8.3.tgz#b894b947bd004381ce63ea1db9f08547e920abd5"
    integrity sha512-FVDR+Gd9iLjUMY1fzE2SR0IuaJToR4RkCDARVfsBBPSP53GEqSFjD8gNyxg246VUyc/ALRxFaAK8rVG7UT7xRA==
    dependencies:
      "@babel/types" "^7.8.3"
  
  "@babel/helper-hoist-variables@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.7.4.tgz#612384e3d823fdfaaf9fce31550fe5d4db0f3d12"
    integrity sha512-wQC4xyvc1Jo/FnLirL6CEgPgPCa8M74tOdjWpRhQYapz5JC7u3NYU1zCVoVAGCE3EaIP9T1A3iW0WLJ+reZlpQ==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-member-expression-to-functions@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.7.4.tgz#356438e2569df7321a8326644d4b790d2122cb74"
    integrity sha512-9KcA1X2E3OjXl/ykfMMInBK+uVdfIVakVe7W7Lg3wfXUNyS3Q1HWLFRwZIjhqiCGbslummPDnmb7vIekS0C1vw==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-member-expression-to-functions@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.8.3.tgz#659b710498ea6c1d9907e0c73f206eee7dadc24c"
    integrity sha512-fO4Egq88utkQFjbPrSHGmGLFqmrshs11d46WI+WZDESt7Wu7wN2G2Iu+NMMZJFDOVRHAMIkB5SNh30NtwCA7RA==
    dependencies:
      "@babel/types" "^7.8.3"
  
  "@babel/helper-module-imports@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-module-imports/-/helper-module-imports-7.7.4.tgz#e5a92529f8888bf319a6376abfbd1cebc491ad91"
    integrity sha512-dGcrX6K9l8258WFjyDLJwuVKxR4XZfU0/vTUgOQYWEnRD8mgr+p4d6fCUMq/ys0h4CCt/S5JhbvtyErjWouAUQ==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-module-imports@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-module-imports/-/helper-module-imports-7.8.3.tgz#7fe39589b39c016331b6b8c3f441e8f0b1419498"
    integrity sha512-R0Bx3jippsbAEtzkpZ/6FIiuzOURPcMjHp+Z6xPe6DtApDJx+w7UYyOLanZqO8+wKR9G10s/FmHXvxaMd9s6Kg==
    dependencies:
      "@babel/types" "^7.8.3"
  
  "@babel/helper-module-transforms@^7.7.4", "@babel/helper-module-transforms@^7.7.5":
    version "7.7.5"
    resolved "https://registry.yarnpkg.com/@babel/helper-module-transforms/-/helper-module-transforms-7.7.5.tgz#d044da7ffd91ec967db25cd6748f704b6b244835"
    integrity sha512-A7pSxyJf1gN5qXVcidwLWydjftUN878VkalhXX5iQDuGyiGK3sOrrKKHF4/A4fwHtnsotv/NipwAeLzY4KQPvw==
    dependencies:
      "@babel/helper-module-imports" "^7.7.4"
      "@babel/helper-simple-access" "^7.7.4"
      "@babel/helper-split-export-declaration" "^7.7.4"
      "@babel/template" "^7.7.4"
      "@babel/types" "^7.7.4"
      lodash "^4.17.13"
  
  "@babel/helper-module-transforms@^7.9.0":
    version "7.9.0"
    resolved "https://registry.yarnpkg.com/@babel/helper-module-transforms/-/helper-module-transforms-7.9.0.tgz#43b34dfe15961918707d247327431388e9fe96e5"
    integrity sha512-0FvKyu0gpPfIQ8EkxlrAydOWROdHpBmiCiRwLkUiBGhCUPRRbVD2/tm3sFr/c/GWFrQ/ffutGUAnx7V0FzT2wA==
    dependencies:
      "@babel/helper-module-imports" "^7.8.3"
      "@babel/helper-replace-supers" "^7.8.6"
      "@babel/helper-simple-access" "^7.8.3"
      "@babel/helper-split-export-declaration" "^7.8.3"
      "@babel/template" "^7.8.6"
      "@babel/types" "^7.9.0"
      lodash "^4.17.13"
  
  "@babel/helper-optimise-call-expression@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.7.4.tgz#034af31370d2995242aa4df402c3b7794b2dcdf2"
    integrity sha512-VB7gWZ2fDkSuqW6b1AKXkJWO5NyNI3bFL/kK79/30moK57blr6NbH8xcl2XcKCwOmJosftWunZqfO84IGq3ZZg==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-optimise-call-expression@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.8.3.tgz#7ed071813d09c75298ef4f208956006b6111ecb9"
    integrity sha512-Kag20n86cbO2AvHca6EJsvqAd82gc6VMGule4HwebwMlwkpXuVqrNRj6CkCV2sKxgi9MyAUnZVnZ6lJ1/vKhHQ==
    dependencies:
      "@babel/types" "^7.8.3"
  
  "@babel/helper-plugin-utils@^7.0.0":
    version "7.0.0"
    resolved "https://registry.yarnpkg.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0.tgz#bbb3fbee98661c569034237cc03967ba99b4f250"
    integrity sha512-CYAOUCARwExnEixLdB6sDm2dIJ/YgEAKDM1MOeMeZu9Ld/bDgVo8aiWrXwcY7OBh+1Ea2uUcVRcxKk0GJvW7QA==
  
  "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.8.3.tgz#9ea293be19babc0f52ff8ca88b34c3611b208670"
    integrity sha512-j+fq49Xds2smCUNYmEHF9kGNkhbet6yVIBp4e6oeQpH1RUs/Ir06xUKzDjDkGcaaokPiTNs2JBWHjaE4csUkZQ==
  
  "@babel/helper-regex@^7.0.0", "@babel/helper-regex@^7.4.4":
    version "7.5.5"
    resolved "https://registry.yarnpkg.com/@babel/helper-regex/-/helper-regex-7.5.5.tgz#0aa6824f7100a2e0e89c1527c23936c152cab351"
    integrity sha512-CkCYQLkfkiugbRDO8eZn6lRuR8kzZoGXCg3149iTk5se7g6qykSpy3+hELSwquhu+TgHn8nkLiBwHvNX8Hofcw==
    dependencies:
      lodash "^4.17.13"
  
  "@babel/helper-remap-async-to-generator@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.7.4.tgz#c68c2407350d9af0e061ed6726afb4fff16d0234"
    integrity sha512-Sk4xmtVdM9sA/jCI80f+KS+Md+ZHIpjuqmYPk1M7F/upHou5e4ReYmExAiu6PVe65BhJPZA2CY9x9k4BqE5klw==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.7.4"
      "@babel/helper-wrap-function" "^7.7.4"
      "@babel/template" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-replace-supers@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-replace-supers/-/helper-replace-supers-7.7.4.tgz#3c881a6a6a7571275a72d82e6107126ec9e2cdd2"
    integrity sha512-pP0tfgg9hsZWo5ZboYGuBn/bbYT/hdLPVSS4NMmiRJdwWhP0IznPwN9AE1JwyGsjSPLC364I0Qh5p+EPkGPNpg==
    dependencies:
      "@babel/helper-member-expression-to-functions" "^7.7.4"
      "@babel/helper-optimise-call-expression" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-replace-supers@^7.8.6":
    version "7.8.6"
    resolved "https://registry.yarnpkg.com/@babel/helper-replace-supers/-/helper-replace-supers-7.8.6.tgz#5ada744fd5ad73203bf1d67459a27dcba67effc8"
    integrity sha512-PeMArdA4Sv/Wf4zXwBKPqVj7n9UF/xg6slNRtZW84FM7JpE1CbG8B612FyM4cxrf4fMAMGO0kR7voy1ForHHFA==
    dependencies:
      "@babel/helper-member-expression-to-functions" "^7.8.3"
      "@babel/helper-optimise-call-expression" "^7.8.3"
      "@babel/traverse" "^7.8.6"
      "@babel/types" "^7.8.6"
  
  "@babel/helper-simple-access@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-simple-access/-/helper-simple-access-7.7.4.tgz#a169a0adb1b5f418cfc19f22586b2ebf58a9a294"
    integrity sha512-zK7THeEXfan7UlWsG2A6CI/L9jVnI5+xxKZOdej39Y0YtDYKx9raHk5F2EtK9K8DHRTihYwg20ADt9S36GR78A==
    dependencies:
      "@babel/template" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helper-simple-access@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-simple-access/-/helper-simple-access-7.8.3.tgz#7f8109928b4dab4654076986af575231deb639ae"
    integrity sha512-VNGUDjx5cCWg4vvCTR8qQ7YJYZ+HBjxOgXEl7ounz+4Sn7+LMD3CFrCTEU6/qXKbA2nKg21CwhhBzO0RpRbdCw==
    dependencies:
      "@babel/template" "^7.8.3"
      "@babel/types" "^7.8.3"
  
  "@babel/helper-split-export-declaration@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.7.4.tgz#57292af60443c4a3622cf74040ddc28e68336fd8"
    integrity sha512-guAg1SXFcVr04Guk9eq0S4/rWS++sbmyqosJzVs8+1fH5NI+ZcmkaSkc7dmtAFbHFva6yRJnjW3yAcGxjueDug==
    dependencies:
      "@babel/types" "^7.7.4"
  
  "@babel/helper-split-export-declaration@^7.8.3":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.8.3.tgz#31a9f30070f91368a7182cf05f831781065fc7a9"
    integrity sha512-3x3yOeyBhW851hroze7ElzdkeRXQYQbFIb7gLK1WQYsw2GWDay5gAJNw1sWJ0VFP6z5J1whqeXH/WCdCjZv6dA==
    dependencies:
      "@babel/types" "^7.8.3"
  
  "@babel/helper-validator-identifier@^7.9.0", "@babel/helper-validator-identifier@^7.9.5":
    version "7.9.5"
    resolved "https://registry.yarnpkg.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.9.5.tgz#90977a8e6fbf6b431a7dc31752eee233bf052d80"
    integrity sha512-/8arLKUFq882w4tWGj9JYzRpAlZgiWUJ+dtteNTDqrRBz9Iguck9Rn3ykuBDoUwh2TO4tSAJlrxDUOXWklJe4g==
  
  "@babel/helper-wrap-function@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helper-wrap-function/-/helper-wrap-function-7.7.4.tgz#37ab7fed5150e22d9d7266e830072c0cdd8baace"
    integrity sha512-VsfzZt6wmsocOaVU0OokwrIytHND55yvyT4BPB9AIIgwr8+x7617hetdJTsuGwygN5RC6mxA9EJztTjuwm2ofg==
    dependencies:
      "@babel/helper-function-name" "^7.7.4"
      "@babel/template" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helpers@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/helpers/-/helpers-7.7.4.tgz#62c215b9e6c712dadc15a9a0dcab76c92a940302"
    integrity sha512-ak5NGZGJ6LV85Q1Zc9gn2n+ayXOizryhjSUBTdu5ih1tlVCJeuQENzc4ItyCVhINVXvIT/ZQ4mheGIsfBkpskg==
    dependencies:
      "@babel/template" "^7.7.4"
      "@babel/traverse" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/helpers@^7.9.0":
    version "7.9.2"
    resolved "https://registry.yarnpkg.com/@babel/helpers/-/helpers-7.9.2.tgz#b42a81a811f1e7313b88cba8adc66b3d9ae6c09f"
    integrity sha512-JwLvzlXVPjO8eU9c/wF9/zOIN7X6h8DYf7mG4CiFRZRvZNKEF5dQ3H3V+ASkHoIB3mWhatgl5ONhyqHRI6MppA==
    dependencies:
      "@babel/template" "^7.8.3"
      "@babel/traverse" "^7.9.0"
      "@babel/types" "^7.9.0"
  
  "@babel/highlight@^7.0.0":
    version "7.5.0"
    resolved "https://registry.yarnpkg.com/@babel/highlight/-/highlight-7.5.0.tgz#56d11312bd9248fa619591d02472be6e8cb32540"
    integrity sha512-7dV4eu9gBxoM0dAnj/BCFDW9LFU0zvTrkq0ugM7pnHEgguOEeOz1so2ZghEdzviYzQEED0r4EAgpsBChKy1TRQ==
    dependencies:
      chalk "^2.0.0"
      esutils "^2.0.2"
      js-tokens "^4.0.0"
  
  "@babel/highlight@^7.8.3":
    version "7.9.0"
    resolved "https://registry.yarnpkg.com/@babel/highlight/-/highlight-7.9.0.tgz#4e9b45ccb82b79607271b2979ad82c7b68163079"
    integrity sha512-lJZPilxX7Op3Nv/2cvFdnlepPXDxi29wxteT57Q965oc5R9v86ztx0jfxVrTcBk8C2kcPkkDa2Z4T3ZsPPVWsQ==
    dependencies:
      "@babel/helper-validator-identifier" "^7.9.0"
      chalk "^2.0.0"
      js-tokens "^4.0.0"
  
  "@babel/parser@^7.0.0", "@babel/parser@^7.7.4", "@babel/parser@^7.7.7":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/parser/-/parser-7.7.7.tgz#1b886595419cf92d811316d5b715a53ff38b4937"
    integrity sha512-WtTZMZAZLbeymhkd/sEaPD8IQyGAhmuTuvTzLiCFM7iXiVdY0gc0IaI+cW0fh1BnSMbJSzXX6/fHllgHKwHhXw==
  
  "@babel/parser@^7.8.6", "@babel/parser@^7.9.0":
    version "7.9.4"
    resolved "https://registry.yarnpkg.com/@babel/parser/-/parser-7.9.4.tgz#68a35e6b0319bbc014465be43828300113f2f2e8"
    integrity sha512-bC49otXX6N0/VYhgOMh4gnP26E9xnDZK3TmbNpxYzzz9BQLBosQwfyOe9/cXUU3txYhTzLCbcqd5c8y/OmCjHA==
  
  "@babel/plugin-external-helpers@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-external-helpers/-/plugin-external-helpers-7.7.4.tgz#8aa7aa402f0e2ecb924611cbf30942a497dfd17e"
    integrity sha512-RVGNajLaFlknbZLutaP/uv7Q+xmVs2LMlEWFXbcjLnwtBdPqAVpV3nzYIAJqri/VjJCUrhG5nALijtg0aND+XA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-proposal-async-generator-functions@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.7.4.tgz#0351c5ac0a9e927845fffd5b82af476947b7ce6d"
    integrity sha512-1ypyZvGRXriY/QP668+s8sFr2mqinhkRDMPSQLNghCQE+GAkFtp+wkHVvg2+Hdki8gwP+NFzJBJ/N1BfzCCDEw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-remap-async-to-generator" "^7.7.4"
      "@babel/plugin-syntax-async-generators" "^7.7.4"
  
  "@babel/plugin-proposal-class-properties@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.7.4.tgz#2f964f0cb18b948450362742e33e15211e77c2ba"
    integrity sha512-EcuXeV4Hv1X3+Q1TsuOmyyxeTRiSqurGJ26+I/FW1WbymmRRapVORm6x1Zl3iDIHyRxEs+VXWp6qnlcfcJSbbw==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-proposal-decorators@^7.6.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.7.4.tgz#58c1e21d21ea12f9f5f0a757e46e687b94a7ab2b"
    integrity sha512-GftcVDcLCwVdzKmwOBDjATd548+IE+mBo7ttgatqNDR7VG7GqIuZPtRWlMLHbhTXhcnFZiGER8iIYl1n/imtsg==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-decorators" "^7.7.4"
  
  "@babel/plugin-proposal-dynamic-import@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.7.4.tgz#dde64a7f127691758cbfed6cf70de0fa5879d52d"
    integrity sha512-StH+nGAdO6qDB1l8sZ5UBV8AC3F2VW2I8Vfld73TMKyptMU9DY5YsJAS8U81+vEtxcH3Y/La0wG0btDrhpnhjQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-dynamic-import" "^7.7.4"
  
  "@babel/plugin-proposal-export-default-from@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.7.4.tgz#890de3c0c475374638292df31f6582160b54d639"
    integrity sha512-1t6dh7BHYUz4zD1m4pozYYEZy/3m8dgOr9owx3r0mPPI3iGKRUKUbIxfYmcJ4hwljs/dhd0qOTr1ZDUp43ix+w==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-export-default-from" "^7.7.4"
  
  "@babel/plugin-proposal-json-strings@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.7.4.tgz#7700a6bfda771d8dc81973249eac416c6b4c697d"
    integrity sha512-wQvt3akcBTfLU/wYoqm/ws7YOAQKu8EVJEvHip/mzkNtjaclQoCCIqKXFP5/eyfnfbQCDV3OLRIK3mIVyXuZlw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-json-strings" "^7.7.4"
  
  "@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.7.4.tgz#7db302c83bc30caa89e38fee935635ef6bd11c28"
    integrity sha512-TbYHmr1Gl1UC7Vo2HVuj/Naci5BEGNZ0AJhzqD2Vpr6QPFWpUmBRLrIDjedzx7/CShq0bRDS2gI4FIs77VHLVQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-nullish-coalescing-operator" "^7.7.4"
  
  "@babel/plugin-proposal-object-rest-spread@^7.0.0", "@babel/plugin-proposal-object-rest-spread@^7.7.7":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.7.7.tgz#9f27075004ab99be08c5c1bd653a2985813cb370"
    integrity sha512-3qp9I8lelgzNedI3hrhkvhaEYree6+WHnyA/q4Dza9z7iEIs1eyhWyJnetk3jJ69RT0AT4G0UhEGwyGFJ7GUuQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-object-rest-spread" "^7.7.4"
  
  "@babel/plugin-proposal-optional-catch-binding@^7.0.0", "@babel/plugin-proposal-optional-catch-binding@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.7.4.tgz#ec21e8aeb09ec6711bc0a39ca49520abee1de379"
    integrity sha512-DyM7U2bnsQerCQ+sejcTNZh8KQEUuC3ufzdnVnSiUv/qoGJp2Z3hanKL18KDhsBT5Wj6a7CMT5mdyCNJsEaA9w==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-optional-catch-binding" "^7.7.4"
  
  "@babel/plugin-proposal-optional-chaining@^7.0.0":
    version "7.7.5"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.7.5.tgz#f0835f044cef85b31071a924010a2a390add11d4"
    integrity sha512-sOwFqT8JSchtJeDD+CjmWCaiFoLxY4Ps7NjvwHC/U7l4e9i5pTRNt8nDMIFSOUL+ncFbYSwruHM8WknYItWdXw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-optional-chaining" "^7.7.4"
  
  "@babel/plugin-proposal-optional-chaining@^7.9.0":
    version "7.9.0"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.9.0.tgz#31db16b154c39d6b8a645292472b98394c292a58"
    integrity sha512-NDn5tu3tcv4W30jNhmc2hyD5c56G6cXx4TesJubhxrJeCvuuMpttxr0OnNCqbZGhFjLrg+NIhxxC+BK5F6yS3w==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.3"
      "@babel/plugin-syntax-optional-chaining" "^7.8.0"
  
  "@babel/plugin-proposal-unicode-property-regex@^7.7.7":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.7.7.tgz#433fa9dac64f953c12578b29633f456b68831c4e"
    integrity sha512-80PbkKyORBUVm1fbTLrHpYdJxMThzM1UqFGh0ALEhO9TYbG86Ah9zQYAB/84axz2vcxefDLdZwWwZNlYARlu9w==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-async-generators@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.7.4.tgz#331aaf310a10c80c44a66b238b6e49132bd3c889"
    integrity sha512-Li4+EjSpBgxcsmeEF8IFcfV/+yJGxHXDirDkEoyFjumuwbmfCVHUt0HuowD/iGM7OhIRyXJH9YXxqiH6N815+g==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-class-properties@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.7.4.tgz#6048c129ea908a432a1ff85f1dc794dc62ddaa5e"
    integrity sha512-JH3v5ZOeKT0qqdJ9BeBcZTFQiJOMax8RopSr1bH6ASkZKo2qWsvBML7W1mp89sszBRDBBRO8snqcByGdrMTdMg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-decorators@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.7.4.tgz#3c91cfee2a111663ff3ac21b851140f5a52a4e0b"
    integrity sha512-0oNLWNH4k5ZbBVfAwiTU53rKFWIeTh6ZlaWOXWJc4ywxs0tjz5fc3uZ6jKAnZSxN98eXVgg7bJIuzjX+3SXY+A==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-dynamic-import@^7.0.0", "@babel/plugin-syntax-dynamic-import@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.7.4.tgz#29ca3b4415abfe4a5ec381e903862ad1a54c3aec"
    integrity sha512-jHQW0vbRGvwQNgyVxwDh4yuXu4bH1f5/EICJLAhl1SblLs2CDhrsmCk+v5XLdE9wxtAFRyxx+P//Iw+a5L/tTg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.7.4.tgz#897f05808298060b52873fa804ff853540790ea1"
    integrity sha512-j888jpjATLEzOWhKawq46UrpXnCRDbdhBd5io4jgwjJ3+CHHGCRb6PNAVEgs+BXIb+dNRAmnkv36zfB992PRVw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.2.0", "@babel/plugin-syntax-flow@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.7.4.tgz#6d91b59e1a0e4c17f36af2e10dd64ef220919d7b"
    integrity sha512-2AMAWl5PsmM5KPkB22cvOkUyWk6MjUaqhHNU5nSPUl/ns3j5qLfw2SuYP5RbVZ0tfLvePr4zUScbICtDP2CUNw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-json-strings@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.7.4.tgz#86e63f7d2e22f9e27129ac4e83ea989a382e86cc"
    integrity sha512-QpGupahTQW1mHRXddMG5srgpHWqRLwJnJZKXTigB9RPFCCGbDGCgBeM/iC82ICXp414WeYx/tD54w7M2qRqTMg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.7.4.tgz#dab2b56a36fb6c3c222a1fbc71f7bf97f327a9ec"
    integrity sha512-wuy6fiMe9y7HeZBWXYCGt2RGxZOj0BImZ9EyXJVnVGBKO/Br592rbR3rtIQn0eQhAk9vqaKP5n8tVqEFBQMfLg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-nullish-coalescing-operator@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.7.4.tgz#e53b751d0c3061b1ba3089242524b65a7a9da12b"
    integrity sha512-XKh/yIRPiQTOeBg0QJjEus5qiSKucKAiApNtO1psqG7D17xmE+X2i5ZqBEuSvo0HRuyPaKaSN/Gy+Ha9KFQolw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.7.4.tgz#47cf220d19d6d0d7b154304701f468fc1cc6ff46"
    integrity sha512-mObR+r+KZq0XhRVS2BrBKBpr5jqrqzlPvS9C9vuOf5ilSwzloAl7RPWLrgKdWS6IreaVrjHxTjtyqFiOisaCwg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-optional-catch-binding@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.7.4.tgz#a3e38f59f4b6233867b4a92dcb0ee05b2c334aa6"
    integrity sha512-4ZSuzWgFxqHRE31Glu+fEr/MirNZOMYmD/0BhBWyLyOOQz/gTAl7QmWm2hX1QxEIXsr2vkdlwxIzTyiYRC4xcQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-optional-chaining@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.7.4.tgz#c91fdde6de85d2eb8906daea7b21944c3610c901"
    integrity sha512-2MqYD5WjZSbJdUagnJvIdSfkb/ucOC9/1fRJxm7GAxY6YQLWlUvkfxoNbUPcPLHJyetKUDQ4+yyuUyAoc0HriA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-optional-chaining@^7.8.0":
    version "7.8.3"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
    integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.8.0"
  
  "@babel/plugin-syntax-top-level-await@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.7.4.tgz#bd7d8fa7b9fee793a36e4027fd6dd1aa32f946da"
    integrity sha512-wdsOw0MvkL1UIgiQ/IFr3ETcfv1xb8RMM0H9wbiDyLaJFyiDg5oZvDLCXosIXmFeIlweML5iOBXAkqddkYNizg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-syntax-typescript@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.7.4.tgz#5d037ffa10f3b25a16f32570ebbe7a8c2efa304b"
    integrity sha512-77blgY18Hud4NM1ggTA8xVT/dBENQf17OpiToSa2jSmEY3fWXD2jwrdVlO4kq5yzUTeF15WSQ6b4fByNvJcjpQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-arrow-functions@^7.0.0", "@babel/plugin-transform-arrow-functions@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.7.4.tgz#76309bd578addd8aee3b379d809c802305a98a12"
    integrity sha512-zUXy3e8jBNPiffmqkHRNDdZM2r8DWhCB7HhcoyZjiK1TxYEluLHAvQuYnTT+ARqRpabWqy/NHkO6e3MsYB5YfA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-async-to-generator@^7.0.0", "@babel/plugin-transform-async-to-generator@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.7.4.tgz#694cbeae6d613a34ef0292713fa42fb45c4470ba"
    integrity sha512-zpUTZphp5nHokuy8yLlyafxCJ0rSlFoSHypTUWgpdwoDXWQcseaect7cJ8Ppk6nunOM6+5rPMkod4OYKPR5MUg==
    dependencies:
      "@babel/helper-module-imports" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-remap-async-to-generator" "^7.7.4"
  
  "@babel/plugin-transform-block-scoped-functions@^7.0.0", "@babel/plugin-transform-block-scoped-functions@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.7.4.tgz#d0d9d5c269c78eaea76227ace214b8d01e4d837b"
    integrity sha512-kqtQzwtKcpPclHYjLK//3lH8OFsCDuDJBaFhVwf8kqdnF6MN4l618UDlcA7TfRs3FayrHj+svYnSX8MC9zmUyQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-block-scoping@^7.0.0", "@babel/plugin-transform-block-scoping@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.7.4.tgz#200aad0dcd6bb80372f94d9e628ea062c58bf224"
    integrity sha512-2VBe9u0G+fDt9B5OV5DQH4KBf5DoiNkwFKOz0TCvBWvdAN2rOykCTkrL+jTLxfCAm76l9Qo5OqL7HBOx2dWggg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      lodash "^4.17.13"
  
  "@babel/plugin-transform-classes@^7.0.0", "@babel/plugin-transform-classes@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.7.4.tgz#c92c14be0a1399e15df72667067a8f510c9400ec"
    integrity sha512-sK1mjWat7K+buWRuImEzjNf68qrKcrddtpQo3swi9j7dUcG6y6R6+Di039QN2bD1dykeswlagupEmpOatFHHUg==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.7.4"
      "@babel/helper-define-map" "^7.7.4"
      "@babel/helper-function-name" "^7.7.4"
      "@babel/helper-optimise-call-expression" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-replace-supers" "^7.7.4"
      "@babel/helper-split-export-declaration" "^7.7.4"
      globals "^11.1.0"
  
  "@babel/plugin-transform-computed-properties@^7.0.0", "@babel/plugin-transform-computed-properties@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.7.4.tgz#e856c1628d3238ffe12d668eb42559f79a81910d"
    integrity sha512-bSNsOsZnlpLLyQew35rl4Fma3yKWqK3ImWMSC/Nc+6nGjC9s5NFWAer1YQ899/6s9HxO2zQC1WoFNfkOqRkqRQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-destructuring@^7.0.0", "@babel/plugin-transform-destructuring@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.7.4.tgz#2b713729e5054a1135097b6a67da1b6fe8789267"
    integrity sha512-4jFMXI1Cu2aXbcXXl8Lr6YubCn6Oc7k9lLsu8v61TZh+1jny2BWmdtvY9zSUlLdGUvcy9DMAWyZEOqjsbeg/wA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-dotall-regex@^7.7.7":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.7.7.tgz#3e9713f1b69f339e87fa796b097d73ded16b937b"
    integrity sha512-b4in+YlTeE/QmTgrllnb3bHA0HntYvjz8O3Mcbx75UBPJA2xhb5A8nle498VhxSXJHQefjtQxpnLPehDJ4TRlg==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-duplicate-keys@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.7.4.tgz#3d21731a42e3f598a73835299dd0169c3b90ac91"
    integrity sha512-g1y4/G6xGWMD85Tlft5XedGaZBCIVN+/P0bs6eabmcPP9egFleMAo65OOjlhcz1njpwagyY3t0nsQC9oTFegJA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-exponentiation-operator@^7.0.0", "@babel/plugin-transform-exponentiation-operator@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.7.4.tgz#dd30c0191e3a1ba19bcc7e389bdfddc0729d5db9"
    integrity sha512-MCqiLfCKm6KEA1dglf6Uqq1ElDIZwFuzz1WH5mTf8k2uQSxEJMbOIEh7IZv7uichr7PMfi5YVSrr1vz+ipp7AQ==
    dependencies:
      "@babel/helper-builder-binary-assignment-operator-visitor" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-flow-strip-types@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.7.4.tgz#cc73f85944782df1d77d80977bc097920a8bf31a"
    integrity sha512-w9dRNlHY5ElNimyMYy0oQowvQpwt/PRHI0QS98ZJCTZU2bvSnKXo5zEiD5u76FBPigTm8TkqzmnUTg16T7qbkA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-flow" "^7.7.4"
  
  "@babel/plugin-transform-for-of@^7.0.0", "@babel/plugin-transform-for-of@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.7.4.tgz#248800e3a5e507b1f103d8b4ca998e77c63932bc"
    integrity sha512-zZ1fD1B8keYtEcKF+M1TROfeHTKnijcVQm0yO/Yu1f7qoDoxEIc/+GX6Go430Bg84eM/xwPFp0+h4EbZg7epAA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-function-name@^7.0.0", "@babel/plugin-transform-function-name@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.7.4.tgz#75a6d3303d50db638ff8b5385d12451c865025b1"
    integrity sha512-E/x09TvjHNhsULs2IusN+aJNRV5zKwxu1cpirZyRPw+FyyIKEHPXTsadj48bVpc1R5Qq1B5ZkzumuFLytnbT6g==
    dependencies:
      "@babel/helper-function-name" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-literals@^7.0.0", "@babel/plugin-transform-literals@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.7.4.tgz#27fe87d2b5017a2a5a34d1c41a6b9f6a6262643e"
    integrity sha512-X2MSV7LfJFm4aZfxd0yLVFrEXAgPqYoDG53Br/tCKiKYfX0MjVjQeWPIhPHHsCqzwQANq+FLN786fF5rgLS+gw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-member-expression-literals@^7.0.0", "@babel/plugin-transform-member-expression-literals@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.7.4.tgz#aee127f2f3339fc34ce5e3055d7ffbf7aa26f19a"
    integrity sha512-9VMwMO7i69LHTesL0RdGy93JU6a+qOPuvB4F4d0kR0zyVjJRVJRaoaGjhtki6SzQUu8yen/vxPKN6CWnCUw6bA==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-modules-amd@^7.7.5":
    version "7.7.5"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.7.5.tgz#39e0fb717224b59475b306402bb8eedab01e729c"
    integrity sha512-CT57FG4A2ZUNU1v+HdvDSDrjNWBrtCmSH6YbbgN3Lrf0Di/q/lWRxZrE72p3+HCCz9UjfZOEBdphgC0nzOS6DQ==
    dependencies:
      "@babel/helper-module-transforms" "^7.7.5"
      "@babel/helper-plugin-utils" "^7.0.0"
      babel-plugin-dynamic-import-node "^2.3.0"
  
  "@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.7.5":
    version "7.7.5"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.7.5.tgz#1d27f5eb0bcf7543e774950e5b2fa782e637b345"
    integrity sha512-9Cq4zTFExwFhQI6MT1aFxgqhIsMWQWDVwOgLzl7PTWJHsNaqFvklAU+Oz6AQLAS0dJKTwZSOCo20INwktxpi3Q==
    dependencies:
      "@babel/helper-module-transforms" "^7.7.5"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-simple-access" "^7.7.4"
      babel-plugin-dynamic-import-node "^2.3.0"
  
  "@babel/plugin-transform-modules-systemjs@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.7.4.tgz#cd98152339d3e763dfe838b7d4273edaf520bb30"
    integrity sha512-y2c96hmcsUi6LrMqvmNDPBBiGCiQu0aYqpHatVVu6kD4mFEXKjyNxd/drc18XXAf9dv7UXjrZwBVmTTGaGP8iw==
    dependencies:
      "@babel/helper-hoist-variables" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      babel-plugin-dynamic-import-node "^2.3.0"
  
  "@babel/plugin-transform-modules-umd@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.7.4.tgz#1027c355a118de0aae9fee00ad7813c584d9061f"
    integrity sha512-u2B8TIi0qZI4j8q4C51ktfO7E3cQ0qnaXFI1/OXITordD40tt17g/sXqgNNCcMTcBFKrUPcGDx+TBJuZxLx7tw==
    dependencies:
      "@babel/helper-module-transforms" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-named-capturing-groups-regex@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.7.4.tgz#fb3bcc4ee4198e7385805007373d6b6f42c98220"
    integrity sha512-jBUkiqLKvUWpv9GLSuHUFYdmHg0ujC1JEYoZUfeOOfNydZXp1sXObgyPatpcwjWgsdBGsagWW0cdJpX/DO2jMw==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.7.4"
  
  "@babel/plugin-transform-new-target@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.7.4.tgz#4a0753d2d60639437be07b592a9e58ee00720167"
    integrity sha512-CnPRiNtOG1vRodnsyGX37bHQleHE14B9dnnlgSeEs3ek3fHN1A1SScglTCg1sfbe7sRQ2BUcpgpTpWSfMKz3gg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-object-assign@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-object-assign/-/plugin-transform-object-assign-7.7.4.tgz#a31b70c434a00a078b2d4d10dbd59992fa70afca"
    integrity sha512-0TpeUlnhQDwKxPLTIckdaWt46L2s61c/5w5snw1OUod5ehOJywZD98Ha3dFHVjeqkfOFtOTH7cqxddjxUuvcmg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-object-super@^7.0.0", "@babel/plugin-transform-object-super@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.7.4.tgz#48488937a2d586c0148451bf51af9d7dda567262"
    integrity sha512-ho+dAEhC2aRnff2JCA0SAK7V2R62zJd/7dmtoe7MHcso4C2mS+vZjn1Pb1pCVZvJs1mgsvv5+7sT+m3Bysb6eg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-replace-supers" "^7.7.4"
  
  "@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.7.7":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.7.7.tgz#7a884b2460164dc5f194f668332736584c760007"
    integrity sha512-OhGSrf9ZBrr1fw84oFXj5hgi8Nmg+E2w5L7NhnG0lPvpDtqd7dbyilM2/vR8CKbJ907RyxPh2kj6sBCSSfI9Ew==
    dependencies:
      "@babel/helper-call-delegate" "^7.7.4"
      "@babel/helper-get-function-arity" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-property-literals@^7.0.0", "@babel/plugin-transform-property-literals@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.7.4.tgz#2388d6505ef89b266103f450f9167e6bd73f98c2"
    integrity sha512-MatJhlC4iHsIskWYyawl53KuHrt+kALSADLQQ/HkhTjX954fkxIEh4q5slL4oRAnsm/eDoZ4q0CIZpcqBuxhJQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-react-display-name@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.7.4.tgz#9f2b80b14ebc97eef4a9b29b612c58ed9c0d10dd"
    integrity sha512-sBbIvqYkthai0X0vkD2xsAwluBp+LtNHH+/V4a5ydifmTtb8KOVOlrMIk/MYmIc4uTYDnjZUHQildYNo36SRJw==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-react-jsx-source@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.7.4.tgz#8994b1bf6014b133f5a46d3b7d1ee5f5e3e72c10"
    integrity sha512-5ZU9FnPhqtHsOXxutRtXZAzoEJwDaP32QcobbMP1/qt7NYcsCNK8XgzJcJfoEr/ZnzVvUNInNjIW22Z6I8p9mg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-jsx" "^7.7.4"
  
  "@babel/plugin-transform-react-jsx@^7.0.0":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.7.7.tgz#5cbaa7445b4a09f774029f3cc7bb448ff3122a5d"
    integrity sha512-SlPjWPbva2+7/ZJbGcoqjl4LsQaLpKEzxW9hcxU7675s24JmdotJOSJ4cgAbV82W3FcZpHIGmRZIlUL8ayMvjw==
    dependencies:
      "@babel/helper-builder-react-jsx" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-jsx" "^7.7.4"
  
  "@babel/plugin-transform-regenerator@^7.0.0", "@babel/plugin-transform-regenerator@^7.7.5":
    version "7.7.5"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.7.5.tgz#3a8757ee1a2780f390e89f246065ecf59c26fce9"
    integrity sha512-/8I8tPvX2FkuEyWbjRCt4qTAgZK0DVy8QRguhA524UH48RfGJy94On2ri+dCuwOpcerPRl9O4ebQkRcVzIaGBw==
    dependencies:
      regenerator-transform "^0.14.0"
  
  "@babel/plugin-transform-reserved-words@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.7.4.tgz#6a7cf123ad175bb5c69aec8f6f0770387ed3f1eb"
    integrity sha512-OrPiUB5s5XvkCO1lS7D8ZtHcswIC57j62acAnJZKqGGnHP+TIc/ljQSrgdX/QyOTdEK5COAhuc820Hi1q2UgLQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-runtime@^7.0.0":
    version "7.7.6"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.7.6.tgz#4f2b548c88922fb98ec1c242afd4733ee3e12f61"
    integrity sha512-tajQY+YmXR7JjTwRvwL4HePqoL3DYxpYXIHKVvrOIvJmeHe2y1w4tz5qz9ObUDC9m76rCzIMPyn4eERuwA4a4A==
    dependencies:
      "@babel/helper-module-imports" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      resolve "^1.8.1"
      semver "^5.5.1"
  
  "@babel/plugin-transform-shorthand-properties@^7.0.0", "@babel/plugin-transform-shorthand-properties@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.7.4.tgz#74a0a9b2f6d67a684c6fbfd5f0458eb7ba99891e"
    integrity sha512-q+suddWRfIcnyG5YiDP58sT65AJDZSUhXQDZE3r04AuqD6d/XLaQPPXSBzP2zGerkgBivqtQm9XKGLuHqBID6Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-spread@^7.0.0", "@babel/plugin-transform-spread@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.7.4.tgz#aa673b356fe6b7e70d69b6e33a17fef641008578"
    integrity sha512-8OSs0FLe5/80cndziPlg4R0K6HcWSM0zyNhHhLsmw/Nc5MaA49cAsnoJ/t/YZf8qkG7fD+UjTRaApVDB526d7Q==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-sticky-regex@^7.0.0", "@babel/plugin-transform-sticky-regex@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.7.4.tgz#ffb68c05090c30732076b1285dc1401b404a123c"
    integrity sha512-Ls2NASyL6qtVe1H1hXts9yuEeONV2TJZmplLONkMPUG158CtmnrzW5Q5teibM5UVOFjG0D3IC5mzXR6pPpUY7A==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/helper-regex" "^7.0.0"
  
  "@babel/plugin-transform-template-literals@^7.0.0", "@babel/plugin-transform-template-literals@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.7.4.tgz#1eb6411736dd3fe87dbd20cc6668e5121c17d604"
    integrity sha512-sA+KxLwF3QwGj5abMHkHgshp9+rRz+oY9uoRil4CyLtgEuE/88dpkeWgNk5qKVsJE9iSfly3nvHapdRiIS2wnQ==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-typeof-symbol@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.7.4.tgz#3174626214f2d6de322882e498a38e8371b2140e"
    integrity sha512-KQPUQ/7mqe2m0B8VecdyaW5XcQYaePyl9R7IsKd+irzj6jvbhoGnRE+M0aNkyAzI07VfUQ9266L5xMARitV3wg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/plugin-transform-typescript@^7.0.0":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.4.tgz#2974fd05f4e85c695acaf497f432342de9fc0636"
    integrity sha512-X8e3tcPEKnwwPVG+vP/vSqEShkwODOEeyQGod82qrIuidwIrfnsGn11qPM1jBLF4MqguTXXYzm58d0dY+/wdpg==
    dependencies:
      "@babel/helper-create-class-features-plugin" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-syntax-typescript" "^7.7.4"
  
  "@babel/plugin-transform-unicode-regex@^7.0.0", "@babel/plugin-transform-unicode-regex@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.7.4.tgz#a3c0f65b117c4c81c5b6484f2a5e7b95346b83ae"
    integrity sha512-N77UUIV+WCvE+5yHw+oks3m18/umd7y392Zv7mYTpFqHtkpcc+QUz+gLJNTWVlWROIWeLqY0f3OjZxV5TcXnRw==
    dependencies:
      "@babel/helper-create-regexp-features-plugin" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
  
  "@babel/preset-env@^7.6.3":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/preset-env/-/preset-env-7.7.7.tgz#c294167b91e53e7e36d820e943ece8d0c7fe46ac"
    integrity sha512-pCu0hrSSDVI7kCVUOdcMNQEbOPJ52E+LrQ14sN8uL2ALfSqePZQlKrOy+tM4uhEdYlCHi4imr8Zz2cZe9oSdIg==
    dependencies:
      "@babel/helper-module-imports" "^7.7.4"
      "@babel/helper-plugin-utils" "^7.0.0"
      "@babel/plugin-proposal-async-generator-functions" "^7.7.4"
      "@babel/plugin-proposal-dynamic-import" "^7.7.4"
      "@babel/plugin-proposal-json-strings" "^7.7.4"
      "@babel/plugin-proposal-object-rest-spread" "^7.7.7"
      "@babel/plugin-proposal-optional-catch-binding" "^7.7.4"
      "@babel/plugin-proposal-unicode-property-regex" "^7.7.7"
      "@babel/plugin-syntax-async-generators" "^7.7.4"
      "@babel/plugin-syntax-dynamic-import" "^7.7.4"
      "@babel/plugin-syntax-json-strings" "^7.7.4"
      "@babel/plugin-syntax-object-rest-spread" "^7.7.4"
      "@babel/plugin-syntax-optional-catch-binding" "^7.7.4"
      "@babel/plugin-syntax-top-level-await" "^7.7.4"
      "@babel/plugin-transform-arrow-functions" "^7.7.4"
      "@babel/plugin-transform-async-to-generator" "^7.7.4"
      "@babel/plugin-transform-block-scoped-functions" "^7.7.4"
      "@babel/plugin-transform-block-scoping" "^7.7.4"
      "@babel/plugin-transform-classes" "^7.7.4"
      "@babel/plugin-transform-computed-properties" "^7.7.4"
      "@babel/plugin-transform-destructuring" "^7.7.4"
      "@babel/plugin-transform-dotall-regex" "^7.7.7"
      "@babel/plugin-transform-duplicate-keys" "^7.7.4"
      "@babel/plugin-transform-exponentiation-operator" "^7.7.4"
      "@babel/plugin-transform-for-of" "^7.7.4"
      "@babel/plugin-transform-function-name" "^7.7.4"
      "@babel/plugin-transform-literals" "^7.7.4"
      "@babel/plugin-transform-member-expression-literals" "^7.7.4"
      "@babel/plugin-transform-modules-amd" "^7.7.5"
      "@babel/plugin-transform-modules-commonjs" "^7.7.5"
      "@babel/plugin-transform-modules-systemjs" "^7.7.4"
      "@babel/plugin-transform-modules-umd" "^7.7.4"
      "@babel/plugin-transform-named-capturing-groups-regex" "^7.7.4"
      "@babel/plugin-transform-new-target" "^7.7.4"
      "@babel/plugin-transform-object-super" "^7.7.4"
      "@babel/plugin-transform-parameters" "^7.7.7"
      "@babel/plugin-transform-property-literals" "^7.7.4"
      "@babel/plugin-transform-regenerator" "^7.7.5"
      "@babel/plugin-transform-reserved-words" "^7.7.4"
      "@babel/plugin-transform-shorthand-properties" "^7.7.4"
      "@babel/plugin-transform-spread" "^7.7.4"
      "@babel/plugin-transform-sticky-regex" "^7.7.4"
      "@babel/plugin-transform-template-literals" "^7.7.4"
      "@babel/plugin-transform-typeof-symbol" "^7.7.4"
      "@babel/plugin-transform-unicode-regex" "^7.7.4"
      "@babel/types" "^7.7.4"
      browserslist "^4.6.0"
      core-js-compat "^3.6.0"
      invariant "^2.2.2"
      js-levenshtein "^1.1.3"
      semver "^5.5.0"
  
  "@babel/register@^7.0.0":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/register/-/register-7.7.7.tgz#46910c4d1926b9c6096421b23d1f9e159c1dcee1"
    integrity sha512-S2mv9a5dc2pcpg/ConlKZx/6wXaEwHeqfo7x/QbXsdCAZm+WJC1ekVvL1TVxNsedTs5y/gG63MhJTEsmwmjtiA==
    dependencies:
      find-cache-dir "^2.0.0"
      lodash "^4.17.13"
      make-dir "^2.1.0"
      pirates "^4.0.0"
      source-map-support "^0.5.16"
  
  "@babel/runtime-corejs3@^7.7.4":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/runtime-corejs3/-/runtime-corejs3-7.7.7.tgz#78fcbd472daec13abc42678bfc319e58a62235a3"
    integrity sha512-kr3W3Fw8mB/CTru2M5zIRQZZgC/9zOxNSoJ/tVCzjPt3H1/p5uuGbz6WwmaQy/TLQcW31rUhUUWKY28sXFRelA==
    dependencies:
      core-js-pure "^3.0.0"
      regenerator-runtime "^0.13.2"
  
  "@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.4.5", "@babel/runtime@^7.5.5", "@babel/runtime@^7.7.4":
    version "7.7.7"
    resolved "https://registry.yarnpkg.com/@babel/runtime/-/runtime-7.7.7.tgz#194769ca8d6d7790ec23605af9ee3e42a0aa79cf"
    integrity sha512-uCnC2JEVAu8AKB5do1WRIsvrdJ0flYx/A/9f/6chdacnEZ7LmavjdsDXr5ksYBegxtuTPR5Va9/+13QF/kFkCA==
    dependencies:
      regenerator-runtime "^0.13.2"
  
  "@babel/template@^7.0.0", "@babel/template@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/template/-/template-7.7.4.tgz#428a7d9eecffe27deac0a98e23bf8e3675d2a77b"
    integrity sha512-qUzihgVPguAzXCK7WXw8pqs6cEwi54s3E+HrejlkuWO6ivMKx9hZl3Y2fSXp9i5HgyWmj7RKP+ulaYnKM4yYxw==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      "@babel/parser" "^7.7.4"
      "@babel/types" "^7.7.4"
  
  "@babel/template@^7.8.3", "@babel/template@^7.8.6":
    version "7.8.6"
    resolved "https://registry.yarnpkg.com/@babel/template/-/template-7.8.6.tgz#86b22af15f828dfb086474f964dcc3e39c43ce2b"
    integrity sha512-zbMsPMy/v0PWFZEhQJ66bqjhH+z0JgMoBWuikXybgG3Gkd/3t5oQ1Rw2WQhnSrsOmsKXnZOx15tkC4qON/+JPg==
    dependencies:
      "@babel/code-frame" "^7.8.3"
      "@babel/parser" "^7.8.6"
      "@babel/types" "^7.8.6"
  
  "@babel/traverse@^7.0.0", "@babel/traverse@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/traverse/-/traverse-7.7.4.tgz#9c1e7c60fb679fe4fcfaa42500833333c2058558"
    integrity sha512-P1L58hQyupn8+ezVA2z5KBm4/Zr4lCC8dwKCMYzsa5jFMDMQAzaBNy9W5VjB+KAmBjb40U7a/H6ao+Xo+9saIw==
    dependencies:
      "@babel/code-frame" "^7.5.5"
      "@babel/generator" "^7.7.4"
      "@babel/helper-function-name" "^7.7.4"
      "@babel/helper-split-export-declaration" "^7.7.4"
      "@babel/parser" "^7.7.4"
      "@babel/types" "^7.7.4"
      debug "^4.1.0"
      globals "^11.1.0"
      lodash "^4.17.13"
  
  "@babel/traverse@^7.8.6", "@babel/traverse@^7.9.0":
    version "7.9.5"
    resolved "https://registry.yarnpkg.com/@babel/traverse/-/traverse-7.9.5.tgz#6e7c56b44e2ac7011a948c21e283ddd9d9db97a2"
    integrity sha512-c4gH3jsvSuGUezlP6rzSJ6jf8fYjLj3hsMZRx/nX0h+fmHN0w+ekubRrHPqnMec0meycA2nwCsJ7dC8IPem2FQ==
    dependencies:
      "@babel/code-frame" "^7.8.3"
      "@babel/generator" "^7.9.5"
      "@babel/helper-function-name" "^7.9.5"
      "@babel/helper-split-export-declaration" "^7.8.3"
      "@babel/parser" "^7.9.0"
      "@babel/types" "^7.9.5"
      debug "^4.1.0"
      globals "^11.1.0"
      lodash "^4.17.13"
  
  "@babel/types@^7.0.0", "@babel/types@^7.7.4":
    version "7.7.4"
    resolved "https://registry.yarnpkg.com/@babel/types/-/types-7.7.4.tgz#516570d539e44ddf308c07569c258ff94fde9193"
    integrity sha512-cz5Ji23KCi4T+YIE/BolWosrJuSmoZeN1EFnRtBwF+KKLi8GG/Z2c2hOJJeCXPk4mwk4QFvTmwIodJowXgttRA==
    dependencies:
      esutils "^2.0.2"
      lodash "^4.17.13"
      to-fast-properties "^2.0.0"
  
  "@babel/types@^7.4.4", "@babel/types@^7.8.3", "@babel/types@^7.8.6", "@babel/types@^7.9.0", "@babel/types@^7.9.5":
    version "7.9.5"
    resolved "https://registry.yarnpkg.com/@babel/types/-/types-7.9.5.tgz#89231f82915a8a566a703b3b20133f73da6b9444"
    integrity sha512-XjnvNqenk818r5zMaba+sLQjnbda31UfUURv3ei0qPQw4u+j2jMyJ5b11y8ZHYTRSI3NnInQkkkRT4fLqqPdHg==
    dependencies:
      "@babel/helper-validator-identifier" "^7.9.5"
      lodash "^4.17.13"
      to-fast-properties "^2.0.0"
  
  "@cnakazawa/watch@^1.0.3":
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/@cnakazawa/watch/-/watch-1.0.3.tgz#099139eaec7ebf07a27c1786a3ff64f39464d2ef"
    integrity sha512-r5160ogAvGyHsal38Kux7YYtodEKOj89RGb28ht1jh3SJb08VwRwAKKJL0bGb04Zd/3r9FL3BFIc3bBidYffCA==
    dependencies:
      exec-sh "^0.3.2"
      minimist "^1.2.0"
  
  "@egjs/hammerjs@^2.0.17":
    version "2.0.17"
    resolved "https://registry.yarnpkg.com/@egjs/hammerjs/-/hammerjs-2.0.17.tgz#5dc02af75a6a06e4c2db0202cae38c9263895124"
    integrity sha512-XQsZgjm2EcVUiZQf11UBJQfmZeEmOW8DpI1gsFeln6w0ae0ii4dMQEQ0kjl6DspdWX1aGY1/loyXnP0JS06e/A==
    dependencies:
      "@types/hammerjs" "^2.0.36"
  
  "@expo/react-native-action-sheet@^3.7.0":
    version "3.7.0"
    resolved "https://registry.yarnpkg.com/@expo/react-native-action-sheet/-/react-native-action-sheet-3.7.0.tgz#2123ee37228a9a4f73828c41a5ca45c0631235ce"
    integrity sha512-Hm2CYdhh6bH7rU+Xqk1hSlTVSKqqlYA7fBXWEU5s/k+6S8U3hHKBEoM3I08cmj1iNS3jFxpOBJU16AH3KSYkRg==
    dependencies:
      "@types/hoist-non-react-statics" "^3.3.1"
      hoist-non-react-statics "^3.3.0"
  
  "@expo/spawn-async@^1.2.8":
    version "1.5.0"
    resolved "https://registry.yarnpkg.com/@expo/spawn-async/-/spawn-async-1.5.0.tgz#799827edd8c10ef07eb1a2ff9dcfe081d596a395"
    integrity sha512-LB7jWkqrHo+5fJHNrLAFdimuSXQ2MQ4lA7SQW5bf/HbsXuV2VrT/jN/M8f/KoWt0uJMGN4k/j7Opx4AvOOxSew==
    dependencies:
      cross-spawn "^6.0.5"
  
  "@expo/vector-icons@^10.0.2":
    version "10.0.6"
    resolved "https://registry.yarnpkg.com/@expo/vector-icons/-/vector-icons-10.0.6.tgz#5718953ff0b97827d11dae5787976fa8ce5caaed"
    integrity sha512-qNlKPNdf073LpeEpyClxAh0D3mmIK4TGAQzeKR0HVwf14RIEe17+mLW5Z6Ka5Ho/lUtKMRPDHumSllFyKvpeGg==
    dependencies:
      lodash "^4.17.4"
  
  "@expo/websql@^1.0.1":
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/@expo/websql/-/websql-1.0.1.tgz#fff0cf9c1baa1f70f9e1d658b7c39a420d9b10a9"
    integrity sha1-//DPnBuqH3D54dZYt8OaQg2bEKk=
    dependencies:
      argsarray "^0.0.1"
      immediate "^3.2.2"
      noop-fn "^1.0.0"
      pouchdb-collections "^1.0.1"
      tiny-queue "^0.2.1"
  
  "@hapi/address@2.x.x":
    version "2.1.4"
    resolved "https://registry.yarnpkg.com/@hapi/address/-/address-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
    integrity sha512-QD1PhQk+s31P1ixsX0H0Suoupp3VMXzIVMSwobR3F3MSUO2YCV0B7xqLcUw/Bh8yuvd3LhpyqLQWTNcRmp6IdQ==
  
  "@hapi/bourne@1.x.x":
    version "1.3.2"
    resolved "https://registry.yarnpkg.com/@hapi/bourne/-/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
    integrity sha512-1dVNHT76Uu5N3eJNTYcvxee+jzX4Z9lfciqRRHCU27ihbUcYi+iSc2iml5Ke1LXe1SyJCLA0+14Jh4tXJgOppA==
  
  "@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
    version "8.5.0"
    resolved "https://registry.yarnpkg.com/@hapi/hoek/-/hoek-8.5.0.tgz#2f9ce301c8898e1c3248b0a8564696b24d1a9a5a"
    integrity sha512-7XYT10CZfPsH7j9F1Jmg1+d0ezOux2oM2GfArAzLwWe4mE2Dr3hVjsAL6+TFY49RRJlCdJDMw3nJsLFroTc8Kw==
  
  "@hapi/joi@^15.0.3":
    version "15.1.1"
    resolved "https://registry.yarnpkg.com/@hapi/joi/-/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
    integrity sha512-entf8ZMOK8sc+8YfeOlM8pCfg3b5+WZIKBfUaaJT8UsjAAPjartzxIYm3TIbjvA4u+u++KbcXD38k682nVHDAQ==
    dependencies:
      "@hapi/address" "2.x.x"
      "@hapi/bourne" "1.x.x"
      "@hapi/hoek" "8.x.x"
      "@hapi/topo" "3.x.x"
  
  "@hapi/topo@3.x.x":
    version "3.1.6"
    resolved "https://registry.yarnpkg.com/@hapi/topo/-/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
    integrity sha512-tAag0jEcjwH+P2quUfipd7liWCNX2F8NvYjQp2wtInsZxnMlypdw0FtAOLxtvvkO+GSRRbmNi8m/5y42PQJYCQ==
    dependencies:
      "@hapi/hoek" "^8.3.0"
  
  "@jest/console@^24.9.0":
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/@jest/console/-/console-24.9.0.tgz#79b1bc06fb74a8cfb01cbdedf945584b1b9707f0"
    integrity sha512-Zuj6b8TnKXi3q4ymac8EQfc3ea/uhLeCGThFqXeC8H9/raaH8ARPUTdId+XyGd03Z4In0/VjD2OYFcBF09fNLQ==
    dependencies:
      "@jest/source-map" "^24.9.0"
      chalk "^2.0.1"
      slash "^2.0.0"
  
  "@jest/fake-timers@^24.9.0":
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/@jest/fake-timers/-/fake-timers-24.9.0.tgz#ba3e6bf0eecd09a636049896434d306636540c93"
    integrity sha512-eWQcNa2YSwzXWIMC5KufBh3oWRIijrQFROsIqt6v/NS9Io/gknw1jsAC9c+ih/RQX4A3O7SeWAhQeN0goKhT9A==
    dependencies:
      "@jest/types" "^24.9.0"
      jest-message-util "^24.9.0"
      jest-mock "^24.9.0"
  
  "@jest/source-map@^24.9.0":
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/@jest/source-map/-/source-map-24.9.0.tgz#0e263a94430be4b41da683ccc1e6bffe2a191714"
    integrity sha512-/Xw7xGlsZb4MJzNDgB7PW5crou5JqWiBQaz6xyPd3ArOg2nfn/PunV8+olXbbEZzNl591o5rWKE9BRDaFAuIBg==
    dependencies:
      callsites "^3.0.0"
      graceful-fs "^4.1.15"
      source-map "^0.6.0"
  
  "@jest/test-result@^24.9.0":
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/@jest/test-result/-/test-result-24.9.0.tgz#11796e8aa9dbf88ea025757b3152595ad06ba0ca"
    integrity sha512-XEFrHbBonBJ8dGp2JmF8kP/nQI/ImPpygKHwQ/SY+es59Z3L5PI4Qb9TQQMAEeYsThG1xF0k6tmG0tIKATNiiA==
    dependencies:
      "@jest/console" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/istanbul-lib-coverage" "^2.0.0"
  
  "@jest/types@^24.9.0":
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/@jest/types/-/types-24.9.0.tgz#63cb26cb7500d069e5a389441a7c6ab5e909fc59"
    integrity sha512-XKK7ze1apu5JWQ5eZjHITP66AX+QsLlbaJRBGYr8pNzwcAE2JVkwnf0yqjHTsDRcjR0mujy/NmZMXw5kl+kGBw==
    dependencies:
      "@types/istanbul-lib-coverage" "^2.0.0"
      "@types/istanbul-reports" "^1.1.1"
      "@types/yargs" "^13.0.0"
  
  "@ptomasroos/react-native-multi-slider@^2.2.2":
    version "2.2.2"
    resolved "https://registry.yarnpkg.com/@ptomasroos/react-native-multi-slider/-/react-native-multi-slider-2.2.2.tgz#35a97fb8c355627c6a2ded010b360ac5728b44ad"
    integrity sha512-HWyCnRD3Z3SbHK2FLWYmBBqd1B4iXipeKv1+AK0FoY/CElEDTEixHE8hN60TsqxalPrznn798LE2Q4tHuCiyaA==
  
  "@react-native-community/cli-debugger-ui@^3.0.0":
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/@react-native-community/cli-debugger-ui/-/cli-debugger-ui-3.0.0.tgz#d01d08d1e5ddc1633d82c7d84d48fff07bd39416"
    integrity sha512-m3X+iWLsK/H7/b7PpbNO33eQayR/+M26la4ZbYe1KRke5Umg4PIWsvg21O8Tw4uJcY8LA5hsP+rBi/syBkBf0g==
    dependencies:
      serve-static "^1.13.1"
  
  "@react-native-community/cli-platform-android@^3.0.0-alpha.1":
    version "3.0.3"
    resolved "https://registry.yarnpkg.com/@react-native-community/cli-platform-android/-/cli-platform-android-3.0.3.tgz#e652abce79a7c1e3a8280228123e99df2c4b97b6"
    integrity sha512-rNO9DmRiVhB6aP2DVUjEJv7ecriTARDZND88ny3xNVUkrD1Y+zwF6aZu3eoT52VXOxLCSLiJzz19OiyGmfqxYg==
    dependencies:
      "@react-native-community/cli-tools" "^3.0.0"
      chalk "^2.4.2"
      execa "^1.0.0"
      jetifier "^1.6.2"
      logkitty "^0.6.0"
      slash "^3.0.0"
      xmldoc "^1.1.2"
  
  "@react-native-community/cli-platform-ios@^3.0.0-alpha.1":
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/@react-native-community/cli-platform-ios/-/cli-platform-ios-3.0.0.tgz#3a48a449c0c33af3b0b3d19d3256de99388fe15f"
    integrity sha512-QoNVlDj8eMXRZk9uktPFsctHurQpv9jKmiu6mQii4NEtT2npE7g1hbWpRNojutBsfgmCdQGDHd9uB54eeCnYgg==
    dependencies:
      "@react-native-community/cli-tools" "^3.0.0"
      chalk "^2.4.2"
      js-yaml "^3.13.1"
      xcode "^2.0.0"
  
  "@react-native-community/cli-tools@^3.0.0":
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/@react-native-community/cli-tools/-/cli-tools-3.0.0.tgz#fe48b80822ed7e49b8af051f9fe41e22a2a710b1"
    integrity sha512-8IhQKZdf3E4CR8T7HhkPGgorot/cLkRDgneJFDSWk/wCYZAuUh4NEAdumQV7N0jLSMWX7xxiWUPi94lOBxVY9g==
    dependencies:
      chalk "^2.4.2"
      lodash "^4.17.5"
      mime "^2.4.1"
      node-fetch "^2.5.0"
  
  "@react-native-community/cli-types@^3.0.0":
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/@react-native-community/cli-types/-/cli-types-3.0.0.tgz#488d46605cb05e88537e030f38da236eeda74652"
    integrity sha512-ng6Tm537E/M42GjE4TRUxQyL8sRfClcL7bQWblOCoxPZzJ2J3bdALsjeG3vDnVCIfI/R0AeFalN9KjMt0+Z/Zg==
  
  "@react-native-community/cli@^3.0.0-alpha.1":
    version "3.0.4"
    resolved "https://registry.yarnpkg.com/@react-native-community/cli/-/cli-3.0.4.tgz#a9dba1bc77855a6e45fccaabb017360645d936bb"
    integrity sha512-kt+ENtC+eRUSfWPbbpx3r7fAQDcFwgM03VW/lBdVAUjkNxffPFT2GGdK23CJSBOXTjRSiGuwhvwH4Z28PdrlRA==
    dependencies:
      "@hapi/joi" "^15.0.3"
      "@react-native-community/cli-debugger-ui" "^3.0.0"
      "@react-native-community/cli-tools" "^3.0.0"
      "@react-native-community/cli-types" "^3.0.0"
      chalk "^2.4.2"
      command-exists "^1.2.8"
      commander "^2.19.0"
      compression "^1.7.1"
      connect "^3.6.5"
      cosmiconfig "^5.1.0"
      deepmerge "^3.2.0"
      envinfo "^7.1.0"
      errorhandler "^1.5.0"
      execa "^1.0.0"
      find-up "^4.1.0"
      fs-extra "^7.0.1"
      glob "^7.1.1"
      graceful-fs "^4.1.3"
      inquirer "^3.0.6"
      lodash "^4.17.5"
      metro "^0.56.0"
      metro-config "^0.56.0"
      metro-core "^0.56.0"
      metro-react-native-babel-transformer "^0.56.0"
      minimist "^1.2.0"
      mkdirp "^0.5.1"
      morgan "^1.9.0"
      node-notifier "^5.2.1"
      open "^6.2.0"
      ora "^3.4.0"
      plist "^3.0.0"
      semver "^6.3.0"
      serve-static "^1.13.1"
      shell-quote "1.6.1"
      strip-ansi "^5.2.0"
      sudo-prompt "^9.0.0"
      wcwidth "^1.0.1"
      ws "^1.1.0"
  
  "@react-native-community/eslint-config@^0.0.6":
    version "0.0.6"
    resolved "https://registry.yarnpkg.com/@react-native-community/eslint-config/-/eslint-config-0.0.6.tgz#9f1dffde994a14db7504a969f21cdf49f470eb34"
    integrity sha512-jtMZ1fzYYykrllbTCATZ8n5HS6Npwv9gusPk/+lMUBrmLXolGFiHFJQIpdcQzWFQb6POigAxo0O+xSiwlsT7Rw==
    dependencies:
      "@typescript-eslint/eslint-plugin" "^1.5.0"
      "@typescript-eslint/parser" "^1.5.0"
      babel-eslint "10.0.3"
      eslint-config-prettier "^6.0.0"
      eslint-plugin-eslint-comments "^3.1.2"
      eslint-plugin-flowtype "2.50.3"
      eslint-plugin-jest "22.4.1"
      eslint-plugin-prettier "2.6.2"
      eslint-plugin-react "7.16.0"
      eslint-plugin-react-hooks "^2.0.1"
      eslint-plugin-react-native "3.8.1"
      prettier "1.17.0"
  
  "@react-native-community/eslint-plugin@^1.0.0":
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/@react-native-community/eslint-plugin/-/eslint-plugin-1.0.0.tgz#ae9a430f2c5795debca491f15a989fce86ea75a0"
    integrity sha512-GLhSN8dRt4lpixPQh+8prSCy6PYk/MT/mvji/ojAd5yshowDo6HFsimCSTD/uWAdjpUq91XK9tVdTNWfGRlKQA==
  
  "@react-native-community/masked-view@0.1.6":
    version "0.1.6"
    resolved "https://registry.yarnpkg.com/@react-native-community/masked-view/-/masked-view-0.1.6.tgz#c7f2ac187c1f25aa8c30d11baa8f4398eca3bb84"
    integrity sha512-PpMoeXwPUoldCRKDuSi+zK5rT+sJTW6ri6RdGPkSKRzU77Q1d9IaR0O5IKvBj0XSdL3p+dcOa05gk35aGDffBQ==
  
  "@react-native-community/netinfo@5.5.1":
    version "5.5.1"
    resolved "https://registry.yarnpkg.com/@react-native-community/netinfo/-/netinfo-5.5.1.tgz#6433d4d9d5fbe836f019e5a88a6a24b6e2dc50b9"
    integrity sha512-6NKX/WzzC5FP2RSzoq+7CW/iIiRL2S6yN0JKiGvZ8EWAzJ43dbX5KG4kRa1JiKopAal5Vyh80dymbygeylwekQ==
  
  "@react-native-community/viewpager@3.3.0":
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/@react-native-community/viewpager/-/viewpager-3.3.0.tgz#e613747a43a31a6f3278f817ba96fdaaa7941f23"
    integrity sha512-tyzh79l4t/hxiyS9QD3LRmWMs8KVkZzjrkQ8U8+8To1wmvVCBtp8BenvNsDLTBO7CpO/YmiThpmIdEZMr1WuVw==
  
  "@react-navigation/core@^3.5.1":
    version "3.5.1"
    resolved "https://registry.yarnpkg.com/@react-navigation/core/-/core-3.5.1.tgz#7a2339fca3496979305fb3a8ab88c2ca8d8c214d"
    integrity sha512-q7NyhWVYOhVIWqL2GZKa6G78YarXaVTTtOlSDkvy4ZIggo40wZzamlnrJRvsaQX46gsgw45FAWb5SriHh8o7eA==
    dependencies:
      hoist-non-react-statics "^3.3.0"
      path-to-regexp "^1.7.0"
      query-string "^6.4.2"
      react-is "^16.8.6"
  
  "@react-navigation/native@^3.6.2":
    version "3.6.2"
    resolved "https://registry.yarnpkg.com/@react-navigation/native/-/native-3.6.2.tgz#3634697b6350cc5189657ae4551f2d52b57fbbf0"
    integrity sha512-Cybeou6N82ZeRmgnGlu+wzlV3z5BZQR2dmYaNFV1TNLUGHqtvv8E7oNw9uYcz9Ox5LFbiX+FdNTn2d6ZPlK0kg==
    dependencies:
      hoist-non-react-statics "^3.0.1"
      react-native-safe-area-view "^0.14.1"
      react-native-screens "^1.0.0 || ^1.0.0-alpha"
  
  "@sentry/browser@^5.15.4":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/browser/-/browser-5.15.4.tgz#5a7e7bad088556665ed8e69bceb0e18784e4f6c7"
    integrity sha512-l/auT1HtZM3KxjCGQHYO/K51ygnlcuOrM+7Ga8gUUbU9ZXDYw6jRi0+Af9aqXKmdDw1naNxr7OCSy6NBrLWVZw==
    dependencies:
      "@sentry/core" "5.15.4"
      "@sentry/types" "5.15.4"
      "@sentry/utils" "5.15.4"
      tslib "^1.9.3"
  
  "@sentry/cli@^1.51.0":
    version "1.52.1"
    resolved "https://registry.yarnpkg.com/@sentry/cli/-/cli-1.52.1.tgz#1c216f2e4aa6a368081e88b20cb7518936724910"
    integrity sha512-XocAy3opa7bxWEbYQ9R/whbIb4BAX2YHXvfMoCwZRzLRy9cf85FYGQCMi8JA7wQd5PBmcxUh31AxcX7jAfMPCQ==
    dependencies:
      fs-copy-file-sync "^1.1.1"
      https-proxy-agent "^4.0.0"
      mkdirp "^0.5.4"
      node-fetch "^2.1.2"
      progress "2.0.0"
      proxy-from-env "^1.0.0"
  
  "@sentry/core@5.15.4", "@sentry/core@^5.15.4":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/core/-/core-5.15.4.tgz#08b617e093a636168be5aebad141d1f744217085"
    integrity sha512-9KP4NM4SqfV5NixpvAymC7Nvp36Zj4dU2fowmxiq7OIbzTxGXDhwuN/t0Uh8xiqlkpkQqSECZ1OjSFXrBldetQ==
    dependencies:
      "@sentry/hub" "5.15.4"
      "@sentry/minimal" "5.15.4"
      "@sentry/types" "5.15.4"
      "@sentry/utils" "5.15.4"
      tslib "^1.9.3"
  
  "@sentry/hub@5.15.4":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/hub/-/hub-5.15.4.tgz#cb64473725a60eec63b0be58ed1143eaaf894bee"
    integrity sha512-1XJ1SVqadkbUT4zLS0TVIVl99si7oHizLmghR8LMFl5wOkGEgehHSoOydQkIAX2C7sJmaF5TZ47ORBHgkqclUg==
    dependencies:
      "@sentry/types" "5.15.4"
      "@sentry/utils" "5.15.4"
      tslib "^1.9.3"
  
  "@sentry/integrations@^5.15.4", "@sentry/integrations@^5.5.0":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/integrations/-/integrations-5.15.4.tgz#c650ee5589d8234de7795b3e8f71c5d54603a103"
    integrity sha512-GaEVQf4R+WBJvTOGptOHIFSylnH1JAvBQZ7c45jGIDBp+upqzeI67KD+HoM4sSNT2Y2i8DLTJCWibe34knz5Kw==
    dependencies:
      "@sentry/types" "5.15.4"
      "@sentry/utils" "5.15.4"
      tslib "^1.9.3"
  
  "@sentry/minimal@5.15.4":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/minimal/-/minimal-5.15.4.tgz#113f01fefb86b7830994c3dfa7ad4889ba7b2003"
    integrity sha512-GL4GZ3drS9ge+wmxkHBAMEwulaE7DMvAEfKQPDAjg2p3MfcCMhAYfuY4jJByAC9rg9OwBGGehz7UmhWMFjE0tw==
    dependencies:
      "@sentry/hub" "5.15.4"
      "@sentry/types" "5.15.4"
      tslib "^1.9.3"
  
  "@sentry/react-native@^1.0.0":
    version "1.3.7"
    resolved "https://registry.yarnpkg.com/@sentry/react-native/-/react-native-1.3.7.tgz#3372b1c1c391f4d8fcff501e4eaf166b81d4917d"
    integrity sha512-Nv19bQOimEU4v8tvaiRD4M9mhbiU4HL8JJypA84HCYAG3CeBIQAwrA0lHIgk08ieBz7VKYzOJHBzOANDw3mAjg==
    dependencies:
      "@sentry/browser" "^5.15.4"
      "@sentry/core" "^5.15.4"
      "@sentry/integrations" "^5.15.4"
      "@sentry/types" "^5.15.4"
      "@sentry/utils" "^5.15.4"
      "@sentry/wizard" "^1.1.1"
  
  "@sentry/types@5.15.4", "@sentry/types@^5.15.4":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/types/-/types-5.15.4.tgz#37f30e35b06e8e12ad1101f1beec3e9b88ca1aab"
    integrity sha512-quPHPpeAuwID48HLPmqBiyXE3xEiZLZ5D3CEbU3c3YuvvAg8qmfOOTI6z4Z3Eedi7flvYpnx3n7N3dXIEz30Eg==
  
  "@sentry/utils@5.15.4", "@sentry/utils@^5.15.4":
    version "5.15.4"
    resolved "https://registry.yarnpkg.com/@sentry/utils/-/utils-5.15.4.tgz#02865ab3c9b745656cea0ab183767ec26c96f6e6"
    integrity sha512-lO8SLBjrUDGADl0LOkd55R5oL510d/1SaI08/IBHZCxCUwI4TiYo5EPECq8mrj3XGfgCyq9osw33bymRlIDuSQ==
    dependencies:
      "@sentry/types" "5.15.4"
      tslib "^1.9.3"
  
  "@sentry/wizard@^1.1.1":
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/@sentry/wizard/-/wizard-1.1.2.tgz#95ba10c892ac144dc60f131fdcc7b2e79fb681be"
    integrity sha512-z7Ck5uli91omT+xSGzOXA3XNj0IUFritzZ5Qjf/KcuSUZuyqLCH2olAR6pXl262tC6kBbWw/xb+AOgPsAQ7u/Q==
    dependencies:
      "@sentry/cli" "^1.51.0"
      chalk "^2.4.1"
      glob "^7.1.3"
      inquirer "^6.2.0"
      lodash "^4.17.15"
      opn "^5.4.0"
      r2 "^2.0.1"
      read-env "^1.3.0"
      xcode "2.0.0"
      yargs "^12.0.2"
  
  "@svgr/babel-plugin-add-jsx-attribute@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-4.2.0.tgz#dadcb6218503532d6884b210e7f3c502caaa44b1"
    integrity sha512-j7KnilGyZzYr/jhcrSYS3FGWMZVaqyCG0vzMCwzvei0coIkczuYMcniK07nI0aHJINciujjH11T72ICW5eL5Ig==
  
  "@svgr/babel-plugin-remove-jsx-attribute@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-4.2.0.tgz#297550b9a8c0c7337bea12bdfc8a80bb66f85abc"
    integrity sha512-3XHLtJ+HbRCH4n28S7y/yZoEQnRpl0tvTZQsHqvaeNXPra+6vE5tbRliH3ox1yZYPCxrlqaJT/Mg+75GpDKlvQ==
  
  "@svgr/babel-plugin-remove-jsx-empty-expression@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-4.2.0.tgz#c196302f3e68eab6a05e98af9ca8570bc13131c7"
    integrity sha512-yTr2iLdf6oEuUE9MsRdvt0NmdpMBAkgK8Bjhl6epb+eQWk6abBaX3d65UZ3E3FWaOwePyUgNyNCMVG61gGCQ7w==
  
  "@svgr/babel-plugin-replace-jsx-attribute-value@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-4.2.0.tgz#310ec0775de808a6a2e4fd4268c245fd734c1165"
    integrity sha512-U9m870Kqm0ko8beHawRXLGLvSi/ZMrl89gJ5BNcT452fAjtF2p4uRzXkdzvGJJJYBgx7BmqlDjBN/eCp5AAX2w==
  
  "@svgr/babel-plugin-svg-dynamic-title@^4.3.3":
    version "4.3.3"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-4.3.3.tgz#2cdedd747e5b1b29ed4c241e46256aac8110dd93"
    integrity sha512-w3Be6xUNdwgParsvxkkeZb545VhXEwjGMwExMVBIdPQJeyMQHqm9Msnb2a1teHBqUYL66qtwfhNkbj1iarCG7w==
  
  "@svgr/babel-plugin-svg-em-dimensions@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-4.2.0.tgz#9a94791c9a288108d20a9d2cc64cac820f141391"
    integrity sha512-C0Uy+BHolCHGOZ8Dnr1zXy/KgpBOkEUYY9kI/HseHVPeMbluaX3CijJr7D4C5uR8zrc1T64nnq/k63ydQuGt4w==
  
  "@svgr/babel-plugin-transform-react-native-svg@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-4.2.0.tgz#151487322843359a1ca86b21a3815fd21a88b717"
    integrity sha512-7YvynOpZDpCOUoIVlaaOUU87J4Z6RdD6spYN4eUb5tfPoKGSF9OG2NuhgYnq4jSkAxcpMaXWPf1cePkzmqTPNw==
  
  "@svgr/babel-plugin-transform-svg-component@^4.2.0":
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-4.2.0.tgz#5f1e2f886b2c85c67e76da42f0f6be1b1767b697"
    integrity sha512-hYfYuZhQPCBVotABsXKSCfel2slf/yvJY8heTVX1PCTaq/IgASq1IyxPPKJ0chWREEKewIU/JMSsIGBtK1KKxw==
  
  "@svgr/babel-preset@^4.3.3":
    version "4.3.3"
    resolved "https://registry.yarnpkg.com/@svgr/babel-preset/-/babel-preset-4.3.3.tgz#a75d8c2f202ac0e5774e6bfc165d028b39a1316c"
    integrity sha512-6PG80tdz4eAlYUN3g5GZiUjg2FMcp+Wn6rtnz5WJG9ITGEF1pmFdzq02597Hn0OmnQuCVaBYQE1OVFAnwOl+0A==
    dependencies:
      "@svgr/babel-plugin-add-jsx-attribute" "^4.2.0"
      "@svgr/babel-plugin-remove-jsx-attribute" "^4.2.0"
      "@svgr/babel-plugin-remove-jsx-empty-expression" "^4.2.0"
      "@svgr/babel-plugin-replace-jsx-attribute-value" "^4.2.0"
      "@svgr/babel-plugin-svg-dynamic-title" "^4.3.3"
      "@svgr/babel-plugin-svg-em-dimensions" "^4.2.0"
      "@svgr/babel-plugin-transform-react-native-svg" "^4.2.0"
      "@svgr/babel-plugin-transform-svg-component" "^4.2.0"
  
  "@svgr/core@^4.3.3":
    version "4.3.3"
    resolved "https://registry.yarnpkg.com/@svgr/core/-/core-4.3.3.tgz#b37b89d5b757dc66e8c74156d00c368338d24293"
    integrity sha512-qNuGF1QON1626UCaZamWt5yedpgOytvLj5BQZe2j1k1B8DUG4OyugZyfEwBeXozCUwhLEpsrgPrE+eCu4fY17w==
    dependencies:
      "@svgr/plugin-jsx" "^4.3.3"
      camelcase "^5.3.1"
      cosmiconfig "^5.2.1"
  
  "@svgr/hast-util-to-babel-ast@^4.3.2":
    version "4.3.2"
    resolved "https://registry.yarnpkg.com/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-4.3.2.tgz#1d5a082f7b929ef8f1f578950238f630e14532b8"
    integrity sha512-JioXclZGhFIDL3ddn4Kiq8qEqYM2PyDKV0aYno8+IXTLuYt6TOgHUbUAAFvqtb0Xn37NwP0BTHglejFoYr8RZg==
    dependencies:
      "@babel/types" "^7.4.4"
  
  "@svgr/plugin-jsx@^4.3.3":
    version "4.3.3"
    resolved "https://registry.yarnpkg.com/@svgr/plugin-jsx/-/plugin-jsx-4.3.3.tgz#e2ba913dbdfbe85252a34db101abc7ebd50992fa"
    integrity sha512-cLOCSpNWQnDB1/v+SUENHH7a0XY09bfuMKdq9+gYvtuwzC2rU4I0wKGFEp1i24holdQdwodCtDQdFtJiTCWc+w==
    dependencies:
      "@babel/core" "^7.4.5"
      "@svgr/babel-preset" "^4.3.3"
      "@svgr/hast-util-to-babel-ast" "^4.3.2"
      svg-parser "^2.0.0"
  
  "@svgr/plugin-svgo@^4.3.1":
    version "4.3.1"
    resolved "https://registry.yarnpkg.com/@svgr/plugin-svgo/-/plugin-svgo-4.3.1.tgz#daac0a3d872e3f55935c6588dd370336865e9e32"
    integrity sha512-PrMtEDUWjX3Ea65JsVCwTIXuSqa3CG9px+DluF1/eo9mlDrgrtFE7NE/DjdhjJgSM9wenlVBzkzneSIUgfUI/w==
    dependencies:
      cosmiconfig "^5.2.1"
      merge-deep "^3.0.2"
      svgo "^1.2.2"
  
  "@types/eslint-visitor-keys@^1.0.0":
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/@types/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz#1ee30d79544ca84d68d4b3cdb0af4f205663dd2d"
    integrity sha512-OCutwjDZ4aFS6PB1UZ988C4YgwlBHJd6wCeQqaLdmadZ/7e+w79+hbMUFC1QXDNCmdyoRfAFdm0RypzwR+Qpag==
  
  "@types/fbemitter@^2.0.32":
    version "2.0.32"
    resolved "https://registry.yarnpkg.com/@types/fbemitter/-/fbemitter-2.0.32.tgz#8ed204da0f54e9c8eaec31b1eec91e25132d082c"
    integrity sha1-jtIE2g9U6cjq7DGx7skeJRMtCCw=
  
  "@types/hammerjs@^2.0.36":
    version "2.0.36"
    resolved "https://registry.yarnpkg.com/@types/hammerjs/-/hammerjs-2.0.36.tgz#17ce0a235e9ffbcdcdf5095646b374c2bf615a4c"
    integrity sha512-7TUK/k2/QGpEAv/BCwSHlYu3NXZhQ9ZwBYpzr9tjlPIL2C5BeGhH3DmVavRx3ZNyELX5TLC91JTz/cen6AAtIQ==
  
  "@types/hoist-non-react-statics@^3.3.0", "@types/hoist-non-react-statics@^3.3.1":
    version "3.3.1"
    resolved "https://registry.yarnpkg.com/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz#1124aafe5118cb591977aeb1ceaaed1070eb039f"
    integrity sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA==
    dependencies:
      "@types/react" "*"
      hoist-non-react-statics "^3.3.0"
  
  "@types/i18n-js@^3.0.1":
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/@types/i18n-js/-/i18n-js-3.0.1.tgz#e027d35513b2703e5d48b82771e7fcb3b664e2fb"
    integrity sha512-m7tWPh9zKpQoYenIpkmeREb2oMaZiZB8qfGv+2ucDg1LgFnETAxYIXkT8dASGyVRWZyEhNEPlfODP4+QfXI3KQ==
  
  "@types/invariant@^2.2.29":
    version "2.2.31"
    resolved "https://registry.yarnpkg.com/@types/invariant/-/invariant-2.2.31.tgz#4444c03004f215289dbca3856538434317dd28b2"
    integrity sha512-jMlgg9pIURvy9jgBHCjQp/CyBjYHUwj91etVcDdXkFl2CwTFiQlB+8tcsMeXpXf2PFE5X2pjk4Gm43hQSMHAdA==
  
  "@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.1.tgz#42995b446db9a48a11a07ec083499a860e9138ff"
    integrity sha512-hRJD2ahnnpLgsj6KWMYSrmXkM3rm2Dl1qkx6IOFD5FnuNPXJIG5L0dhgKXCYTRMGzU4n0wImQ/xfmRc4POUFlg==
  
  "@types/istanbul-lib-report@*":
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/@types/istanbul-lib-report/-/istanbul-lib-report-1.1.1.tgz#e5471e7fa33c61358dd38426189c037a58433b8c"
    integrity sha512-3BUTyMzbZa2DtDI2BkERNC6jJw2Mr2Y0oGI7mRxYNBPxppbtEK1F66u3bKwU2g+wxwWI7PAoRpJnOY1grJqzHg==
    dependencies:
      "@types/istanbul-lib-coverage" "*"
  
  "@types/istanbul-reports@^1.1.1":
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/@types/istanbul-reports/-/istanbul-reports-1.1.1.tgz#7a8cbf6a406f36c8add871625b278eaf0b0d255a"
    integrity sha512-UpYjBi8xefVChsCoBpKShdxTllC9pwISirfoZsUa2AAdQg/Jd2KQGtSbw+ya7GPo7x/wAPlH6JBhKhAsXUEZNA==
    dependencies:
      "@types/istanbul-lib-coverage" "*"
      "@types/istanbul-lib-report" "*"
  
  "@types/json-schema@^7.0.3":
    version "7.0.4"
    resolved "https://registry.yarnpkg.com/@types/json-schema/-/json-schema-7.0.4.tgz#38fd73ddfd9b55abb1e1b2ed578cb55bd7b7d339"
    integrity sha512-8+KAKzEvSUdeo+kmqnKrqgeE+LcA0tjYWFY7RPProVYwnqDjukzO+3b6dLD56rYX5TdWejnEOLJYOIeh4CXKuA==
  
  "@types/lodash.zipobject@^4.1.4":
    version "4.1.6"
    resolved "https://registry.yarnpkg.com/@types/lodash.zipobject/-/lodash.zipobject-4.1.6.tgz#75e140f44ac7d7682a18d3aae8ee4594fad094d7"
    integrity sha512-30khEHqHWaLgMZR35wtkg07OmHiNiDQyor0SK7oj8Sy05tg6jDjPmJybeZ64WKeFZUEgs1tdJwdT0xUl+2qUgQ==
    dependencies:
      "@types/lodash" "*"
  
  "@types/lodash@*":
    version "4.14.149"
    resolved "https://registry.yarnpkg.com/@types/lodash/-/lodash-4.14.149.tgz#1342d63d948c6062838fbf961012f74d4e638440"
    integrity sha512-ijGqzZt/b7BfzcK9vTrS6MFljQRPn5BFWOx8oE0GYxribu6uV+aA9zZuXI1zc/etK9E8nrgdoF2+LgUw7+9tJQ==
  
  "@types/minimatch@^3.0.3":
    version "3.0.3"
    resolved "https://registry.yarnpkg.com/@types/minimatch/-/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
    integrity sha512-tHq6qdbT9U1IRSGf14CL0pUlULksvY9OZ+5eEgl1N7t+OA3tGvNpxJCzuKQlsNgCVwbAs670L1vcVQi8j9HjnA==
  
  "@types/normalize-package-data@^2.4.0":
    version "2.4.0"
    resolved "https://registry.yarnpkg.com/@types/normalize-package-data/-/normalize-package-data-2.4.0.tgz#e486d0d97396d79beedd0a6e33f4534ff6b4973e"
    integrity sha512-f5j5b/Gf71L+dbqxIpQ4Z2WlmI/mPJ0fOkGGmFgtb6sAu97EPczzbS3/tJKxmcYDj55OX6ssqwDAWOHIYDRDGA==
  
  "@types/prop-types@*":
    version "15.7.3"
    resolved "https://registry.yarnpkg.com/@types/prop-types/-/prop-types-15.7.3.tgz#2ab0d5da2e5815f94b0b9d4b95d1e5f243ab2ca7"
    integrity sha512-KfRL3PuHmqQLOG+2tGpRO26Ctg+Cq1E01D2DMriKEATHgWLfeNDmq9e29Q9WIky0dQ3NPkd1mzYH8Lm936Z9qw==
  
  "@types/q@^1.5.1":
    version "1.5.2"
    resolved "https://registry.yarnpkg.com/@types/q/-/q-1.5.2.tgz#690a1475b84f2a884fd07cd797c00f5f31356ea8"
    integrity sha512-ce5d3q03Ex0sy4R14722Rmt6MT07Ua+k4FwDfdcToYJcMKNtRVQvJ6JCAPdAmAnbRb6CsX6aYb9m96NGod9uTw==
  
  "@types/qs@^6.5.1":
    version "6.9.0"
    resolved "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.0.tgz#2a5fa918786d07d3725726f7f650527e1cfeaffd"
    integrity sha512-c4zji5CjWv1tJxIZkz1oUtGcdOlsH3aza28Nqmm+uNDWBRHoMsjooBEN4czZp1V3iXPihE/VRUOBqg+4Xq0W4g==
  
  "@types/react-native-snap-carousel@^3.8.1":
    version "3.8.1"
    resolved "https://registry.yarnpkg.com/@types/react-native-snap-carousel/-/react-native-snap-carousel-3.8.1.tgz#1fabbae6053c2c234f11cec6e5622086677b4da4"
    integrity sha512-nKpCGtyvi0RB7iyrMf3Zyl9r4fvEVMnhBnHNWZe5FPvTiJ+XPPJw3lo9HOoqZIySVvYoASQdGnFLgWnugiUmHw==
    dependencies:
      "@types/react" "*"
      "@types/react-native" "*"
  
  "@types/react-native-vector-icons@^6.4.4":
    version "6.4.5"
    resolved "https://registry.yarnpkg.com/@types/react-native-vector-icons/-/react-native-vector-icons-6.4.5.tgz#74cbfc564bd8435e43ad6728572a0e5b49c335d1"
    integrity sha512-JBpcjWQE4n0GlE0p6HpDDclT+uXpFC453T5k4h+B38q0utlGJhvgNr8899BoJGc1xOktA2cgqFKmFMJd0h7YaA==
    dependencies:
      "@types/react" "*"
      "@types/react-native" "*"
  
  "@types/react-native@*":
    version "0.60.28"
    resolved "https://registry.yarnpkg.com/@types/react-native/-/react-native-0.60.28.tgz#5982bc1e96defb033bc243adcb9ed315bdacd737"
    integrity sha512-X9oC4gPoHhYbAUxaddeBRVHqq307zSR6SzaIoQFjghe7a/90mJ1bqXnv49AkisN1LKI66i8dkIrb5IOvQEJ/0Q==
    dependencies:
      "@types/prop-types" "*"
      "@types/react" "*"
  
  "@types/react-native@^0.60.22":
    version "0.60.31"
    resolved "https://registry.yarnpkg.com/@types/react-native/-/react-native-0.60.31.tgz#a7af12197f884ad8dd22cda2df9862ed72973ded"
    integrity sha512-Y0Q+nv50KHnLL+jM0UH68gQQv7Wt6v2KuNepiHKwK1DoWGVd1oYun/GJCnvUje+/V8pMQQWW6QuBvHZz1pV7tQ==
    dependencies:
      "@types/prop-types" "*"
      "@types/react" "*"
  
  "@types/react-redux@^7.1.5":
    version "7.1.5"
    resolved "https://registry.yarnpkg.com/@types/react-redux/-/react-redux-7.1.5.tgz#c7a528d538969250347aa53c52241051cf886bd3"
    integrity sha512-ZoNGQMDxh5ENY7PzU7MVonxDzS1l/EWiy8nUhDqxFqUZn4ovboCyvk4Djf68x6COb7vhGTKjyjxHxtFdAA5sUA==
    dependencies:
      "@types/hoist-non-react-statics" "^3.3.0"
      "@types/react" "*"
      hoist-non-react-statics "^3.3.0"
      redux "^4.0.0"
  
  "@types/react@*":
    version "16.9.17"
    resolved "https://registry.yarnpkg.com/@types/react/-/react-16.9.17.tgz#58f0cc0e9ec2425d1441dd7b623421a867aa253e"
    integrity sha512-UP27In4fp4sWF5JgyV6pwVPAQM83Fj76JOcg02X5BZcpSu5Wx+fP9RMqc2v0ssBoQIFvD5JdKY41gjJJKmw6Bg==
    dependencies:
      "@types/prop-types" "*"
      csstype "^2.2.0"
  
  "@types/react@^16.9.11":
    version "16.9.34"
    resolved "https://registry.yarnpkg.com/@types/react/-/react-16.9.34.tgz#f7d5e331c468f53affed17a8a4d488cd44ea9349"
    integrity sha512-8AJlYMOfPe1KGLKyHpflCg5z46n0b5DbRfqDksxBLBTUpB75ypDBAO9eCUcjNwE6LCUslwTz00yyG/X9gaVtow==
    dependencies:
      "@types/prop-types" "*"
      csstype "^2.2.0"
  
  "@types/redux-api-middleware@^3.0.7":
    version "3.0.7"
    resolved "https://registry.yarnpkg.com/@types/redux-api-middleware/-/redux-api-middleware-3.0.7.tgz#238a57fddf8aaad4506c16f070860e25478154da"
    integrity sha512-o+I04ymVP/eaLizLTiM4TirP+j7W6e2Qm2ZJL24O6OdLAxuLTU9QyA1V33exlZVXxrFLtVoqwuOKu/JNvhFbBQ==
    dependencies:
      redux "^4.0.1"
  
  "@types/redux-logger@^3.0.7":
    version "3.0.7"
    resolved "https://registry.yarnpkg.com/@types/redux-logger/-/redux-logger-3.0.7.tgz#163f6f6865c69c21d56f9356dc8d741718ec0db0"
    integrity sha512-oV9qiCuowhVR/ehqUobWWkXJjohontbDGLV88Be/7T4bqMQ3kjXwkFNL7doIIqlbg3X2PC5WPziZ8/j/QHNQ4A==
    dependencies:
      redux "^3.6.0"
  
  "@types/stack-utils@^1.0.1":
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/@types/stack-utils/-/stack-utils-1.0.1.tgz#0a851d3bd96498fa25c33ab7278ed3bd65f06c3e"
    integrity sha512-l42BggppR6zLmpfU6fq9HEa2oGPEI8yrSPL3GITjfRInppYFahObbIQOQK3UGxEnyQpltZLaPe75046NOZQikw==
  
  "@types/tinycolor2@^1.4.2":
    version "1.4.2"
    resolved "https://registry.yarnpkg.com/@types/tinycolor2/-/tinycolor2-1.4.2.tgz#721ca5c5d1a2988b4a886e35c2ffc5735b6afbdf"
    integrity sha512-PeHg/AtdW6aaIO2a+98Xj7rWY4KC1E6yOy7AFknJQ7VXUGNrMlyxDFxJo7HqLtjQms/ZhhQX52mLVW/EX3JGOw==
  
  "@types/websql@^0.0.27":
    version "0.0.27"
    resolved "https://registry.yarnpkg.com/@types/websql/-/websql-0.0.27.tgz#621a666a7f02018e7cbb4abab956a25736c27d71"
    integrity sha1-Yhpman8CAY58u0q6uVaiVzbCfXE=
  
  "@types/yargs-parser@*":
    version "13.1.0"
    resolved "https://registry.yarnpkg.com/@types/yargs-parser/-/yargs-parser-13.1.0.tgz#c563aa192f39350a1d18da36c5a8da382bbd8228"
    integrity sha512-gCubfBUZ6KxzoibJ+SCUc/57Ms1jz5NjHe4+dI2krNmU5zCPAphyLJYyTOg06ueIyfj+SaCUqmzun7ImlxDcKg==
  
  "@types/yargs@^13.0.0":
    version "13.0.4"
    resolved "https://registry.yarnpkg.com/@types/yargs/-/yargs-13.0.4.tgz#53d231cebe1a540e7e13727fc1f0d13ad4a9ba3b"
    integrity sha512-Ke1WmBbIkVM8bpvsNEcGgQM70XcEh/nbpxQhW7FhrsbCsXSY9BmLB1+LHtD7r9zrsOcFlLiF+a/UeJsdfw3C5A==
    dependencies:
      "@types/yargs-parser" "*"
  
  "@typescript-eslint/eslint-plugin@^1.5.0":
    version "1.13.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-1.13.0.tgz#22fed9b16ddfeb402fd7bcde56307820f6ebc49f"
    integrity sha512-WQHCozMnuNADiqMtsNzp96FNox5sOVpU8Xt4meaT4em8lOG1SrOv92/mUbEHQVh90sldKSfcOc/I0FOb/14G1g==
    dependencies:
      "@typescript-eslint/experimental-utils" "1.13.0"
      eslint-utils "^1.3.1"
      functional-red-black-tree "^1.0.1"
      regexpp "^2.0.1"
      tsutils "^3.7.0"
  
  "@typescript-eslint/eslint-plugin@^2.14.0":
    version "2.14.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-2.14.0.tgz#c74447400537d4eb7aae1e31879ab43e6c662a8a"
    integrity sha512-sneOJ3Hu0m5whJiVIxGBZZZMxMJ7c0LhAJzeMJgHo+n5wFs+/6rSR/gl7crkdR2kNwfOOSdzdc0gMvatG4dX2Q==
    dependencies:
      "@typescript-eslint/experimental-utils" "2.14.0"
      eslint-utils "^1.4.3"
      functional-red-black-tree "^1.0.1"
      regexpp "^3.0.0"
      tsutils "^3.17.1"
  
  "@typescript-eslint/experimental-utils@1.13.0":
    version "1.13.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/experimental-utils/-/experimental-utils-1.13.0.tgz#b08c60d780c0067de2fb44b04b432f540138301e"
    integrity sha512-zmpS6SyqG4ZF64ffaJ6uah6tWWWgZ8m+c54XXgwFtUv0jNz8aJAVx8chMCvnk7yl6xwn8d+d96+tWp7fXzTuDg==
    dependencies:
      "@types/json-schema" "^7.0.3"
      "@typescript-eslint/typescript-estree" "1.13.0"
      eslint-scope "^4.0.0"
  
  "@typescript-eslint/experimental-utils@2.14.0":
    version "2.14.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/experimental-utils/-/experimental-utils-2.14.0.tgz#e9179fa3c44e00b3106b85d7b69342901fb43e3b"
    integrity sha512-KcyKS7G6IWnIgl3ZpyxyBCxhkBPV+0a5Jjy2g5HxlrbG2ZLQNFeneIBVXdaBCYOVjvGmGGFKom1kgiAY75SDeQ==
    dependencies:
      "@types/json-schema" "^7.0.3"
      "@typescript-eslint/typescript-estree" "2.14.0"
      eslint-scope "^5.0.0"
  
  "@typescript-eslint/parser@^1.5.0":
    version "1.13.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/parser/-/parser-1.13.0.tgz#61ac7811ea52791c47dc9fd4dd4a184fae9ac355"
    integrity sha512-ITMBs52PCPgLb2nGPoeT4iU3HdQZHcPaZVw+7CsFagRJHUhyeTgorEwHXhFf3e7Evzi8oujKNpHc8TONth8AdQ==
    dependencies:
      "@types/eslint-visitor-keys" "^1.0.0"
      "@typescript-eslint/experimental-utils" "1.13.0"
      "@typescript-eslint/typescript-estree" "1.13.0"
      eslint-visitor-keys "^1.0.0"
  
  "@typescript-eslint/parser@^2.14.0", "@typescript-eslint/parser@^2.3.0":
    version "2.14.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/parser/-/parser-2.14.0.tgz#30fa0523d86d74172a5e32274558404ba4262cd6"
    integrity sha512-haS+8D35fUydIs+zdSf4BxpOartb/DjrZ2IxQ5sR8zyGfd77uT9ZJZYF8+I0WPhzqHmfafUBx8MYpcp8pfaoSA==
    dependencies:
      "@types/eslint-visitor-keys" "^1.0.0"
      "@typescript-eslint/experimental-utils" "2.14.0"
      "@typescript-eslint/typescript-estree" "2.14.0"
      eslint-visitor-keys "^1.1.0"
  
  "@typescript-eslint/typescript-estree@1.13.0":
    version "1.13.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/typescript-estree/-/typescript-estree-1.13.0.tgz#8140f17d0f60c03619798f1d628b8434913dc32e"
    integrity sha512-b5rCmd2e6DCC6tCTN9GSUAuxdYwCM/k/2wdjHGrIRGPSJotWMCe/dGpi66u42bhuh8q3QBzqM4TMA1GUUCJvdw==
    dependencies:
      lodash.unescape "4.0.1"
      semver "5.5.0"
  
  "@typescript-eslint/typescript-estree@2.14.0":
    version "2.14.0"
    resolved "https://registry.yarnpkg.com/@typescript-eslint/typescript-estree/-/typescript-estree-2.14.0.tgz#c67698acdc14547f095eeefe908958d93e1a648d"
    integrity sha512-pnLpUcMNG7GfFFfNQbEX6f1aPa5fMnH2G9By+A1yovYI4VIOK2DzkaRuUlIkbagpAcrxQHLqovI1YWqEcXyRnA==
    dependencies:
      debug "^4.1.1"
      eslint-visitor-keys "^1.1.0"
      glob "^7.1.6"
      is-glob "^4.0.1"
      lodash.unescape "4.0.1"
      semver "^6.3.0"
      tsutils "^3.17.1"
  
  "@unimodules/core@~5.1.0":
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/@unimodules/core/-/core-5.1.0.tgz#eae3706e1ecc99d4b992ad39970c43d92418d394"
    integrity sha512-gaamGkJ4PVwusWEfsZyPo4uhrVWPDE0BmHc/lTYfkZCv2oIAswC7gG/ULRdtZpYdwnYqFIZng+WQxwuVrJUNDw==
    dependencies:
      compare-versions "^3.4.0"
  
  "@unimodules/react-native-adapter@~5.1.1":
    version "5.1.1"
    resolved "https://registry.yarnpkg.com/@unimodules/react-native-adapter/-/react-native-adapter-5.1.1.tgz#bb67a021c722772e716eaa3817084cd988642e4e"
    integrity sha512-PlP6QQ2Z3ckORhS07tWcIweK+CkkxyzitJ1j1FD+N+G7G/CB99/vSfCEQ7BFVAPRO5vPrcS2QcwSDgvz06wKVA==
    dependencies:
      invariant "^2.2.4"
      lodash "^4.5.0"
      prop-types "^15.6.1"
  
  "@welldone-software/why-did-you-render@^4.1.2":
    version "4.1.2"
    resolved "https://registry.yarnpkg.com/@welldone-software/why-did-you-render/-/why-did-you-render-4.1.2.tgz#d88f55b6d7eed11271a24ec949fd156b5789db8a"
    integrity sha512-bYFOe9zPTNpr10XpP48xID93eiw3TrVyWEFKt4dnG35RghhtxetKfTSmt/bx+ZLV9NuSJQUxiig6VUGxWpUP4Q==
    dependencies:
      lodash "^4"
  
  abort-controller@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/abort-controller/-/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
    integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
    dependencies:
      event-target-shim "^5.0.0"
  
  abs-svg-path@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/abs-svg-path/-/abs-svg-path-0.1.1.tgz#df601c8e8d2ba10d4a76d625e236a9a39c2723bf"
    integrity sha1-32Acjo0roQ1KdtYl4japo5wnI78=
  
  absolute-path@^0.0.0:
    version "0.0.0"
    resolved "https://registry.yarnpkg.com/absolute-path/-/absolute-path-0.0.0.tgz#a78762fbdadfb5297be99b15d35a785b2f095bf7"
    integrity sha1-p4di+9rftSl76ZsV01p4Wy8JW/c=
  
  accepts@~1.3.5, accepts@~1.3.7:
    version "1.3.7"
    resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
    integrity sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==
    dependencies:
      mime-types "~2.1.24"
      negotiator "0.6.2"
  
  acorn-jsx@^5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/acorn-jsx/-/acorn-jsx-5.1.0.tgz#294adb71b57398b0680015f0a38c563ee1db5384"
    integrity sha512-tMUqwBWfLFbJbizRmEcWSLw6HnFzfdJs2sOJEOwwtVPMoH/0Ay+E703oZz78VSXZiiDcZrQ5XKjPIUQixhmgVw==
  
  acorn@^7.1.0:
    version "7.1.0"
    resolved "https://registry.yarnpkg.com/acorn/-/acorn-7.1.0.tgz#949d36f2c292535da602283586c2477c57eb2d6c"
    integrity sha512-kL5CuoXA/dgxlBbVrflsflzQ3PAas7RYZB52NOm/6839iVYJgKMJ3cQJD+t2i5+qFa8h3MDpEOJiS64E8JLnSQ==
  
  agent-base@5:
    version "5.1.1"
    resolved "https://registry.yarnpkg.com/agent-base/-/agent-base-5.1.1.tgz#e8fb3f242959db44d63be665db7a8e739537a32c"
    integrity sha512-TMeqbNl2fMW0nMjTEPOwe3J/PRFP4vqeoNuQMG0HlMrtm5QxKqdvAkZ1pRBQ/ulIyDD5Yq0nJ7YbdD8ey0TO3g==
  
  ajv@^6.10.0, ajv@^6.10.2:
    version "6.10.2"
    resolved "https://registry.yarnpkg.com/ajv/-/ajv-6.10.2.tgz#d3cea04d6b017b2894ad69040fec8b623eb4bd52"
    integrity sha512-TXtUUEYHuaTEbLZWIKUr5pmBuhDLy+8KYtPYdcV8qC+pOZL+NKqYwvWSRrVXHn+ZmRRAu8vJTAznH7Oag6RVRw==
    dependencies:
      fast-deep-equal "^2.0.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ansi-colors@^1.0.1:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/ansi-colors/-/ansi-colors-1.1.0.tgz#6374b4dd5d4718ff3ce27a671a3b1cad077132a9"
    integrity sha512-SFKX67auSNoVR38N3L+nvsPjOE0bybKTYbkf5tRvushrAPQ9V75huw0ZxBkKVeRU9kqH3d6HA4xTckbwZ4ixmA==
    dependencies:
      ansi-wrap "^0.1.0"
  
  ansi-cyan@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/ansi-cyan/-/ansi-cyan-0.1.1.tgz#538ae528af8982f28ae30d86f2f17456d2609873"
    integrity sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM=
    dependencies:
      ansi-wrap "0.1.0"
  
  ansi-escapes@^3.0.0, ansi-escapes@^3.2.0:
    version "3.2.0"
    resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
    integrity sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==
  
  ansi-escapes@^4.2.1:
    version "4.3.0"
    resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-4.3.0.tgz#a4ce2b33d6b214b7950d8595c212f12ac9cc569d"
    integrity sha512-EiYhwo0v255HUL6eDyuLrXEkTi7WwVCLAw+SeOQ7M7qdun1z1pum4DEm/nuqIVbPvi9RPPc9k9LbyBv6H0DwVg==
    dependencies:
      type-fest "^0.8.1"
  
  ansi-fragments@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/ansi-fragments/-/ansi-fragments-0.2.1.tgz#24409c56c4cc37817c3d7caa99d8969e2de5a05e"
    integrity sha512-DykbNHxuXQwUDRv5ibc2b0x7uw7wmwOGLBUd5RmaQ5z8Lhx19vwvKV+FAsM5rEA6dEcHxX+/Ad5s9eF2k2bB+w==
    dependencies:
      colorette "^1.0.7"
      slice-ansi "^2.0.0"
      strip-ansi "^5.0.0"
  
  ansi-gray@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/ansi-gray/-/ansi-gray-0.1.1.tgz#2962cf54ec9792c48510a3deb524436861ef7251"
    integrity sha1-KWLPVOyXksSFEKPetSRDaGHvclE=
    dependencies:
      ansi-wrap "0.1.0"
  
  ansi-red@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/ansi-red/-/ansi-red-0.1.1.tgz#8c638f9d1080800a353c9c28c8a81ca4705d946c"
    integrity sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw=
    dependencies:
      ansi-wrap "0.1.0"
  
  ansi-regex@^2.0.0:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
    integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=
  
  ansi-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
    integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=
  
  ansi-regex@^4.0.0, ansi-regex@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
    integrity sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg==
  
  ansi-regex@^5.0.0:
    version "5.0.0"
    resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
    integrity sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg==
  
  ansi-styles@^3.2.0, ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
    integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
    dependencies:
      color-convert "^1.9.0"
  
  ansi-wrap@0.1.0, ansi-wrap@^0.1.0:
    version "0.1.0"
    resolved "https://registry.yarnpkg.com/ansi-wrap/-/ansi-wrap-0.1.0.tgz#a82250ddb0015e9a27ca82e82ea603bbfa45efaf"
    integrity sha1-qCJQ3bABXponyoLoLqYDu/pF768=
  
  anymatch@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/anymatch/-/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
    integrity sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==
    dependencies:
      micromatch "^3.1.4"
      normalize-path "^2.1.1"
  
  argparse@^1.0.7:
    version "1.0.10"
    resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
    integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
    dependencies:
      sprintf-js "~1.0.2"
  
  argsarray@^0.0.1:
    version "0.0.1"
    resolved "https://registry.yarnpkg.com/argsarray/-/argsarray-0.0.1.tgz#6e7207b4ecdb39b0af88303fa5ae22bda8df61cb"
    integrity sha1-bnIHtOzbObCviDA/pa4ivajfYcs=
  
  aria-query@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/aria-query/-/aria-query-3.0.0.tgz#65b3fcc1ca1155a8c9ae64d6eee297f15d5133cc"
    integrity sha1-ZbP8wcoRVajJrmTW7uKX8V1RM8w=
    dependencies:
      ast-types-flow "0.0.7"
      commander "^2.11.0"
  
  arr-diff@^1.0.1:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/arr-diff/-/arr-diff-1.1.0.tgz#687c32758163588fef7de7b36fabe495eb1a399a"
    integrity sha1-aHwydYFjWI/vfeezb6vklesaOZo=
    dependencies:
      arr-flatten "^1.0.1"
      array-slice "^0.2.3"
  
  arr-diff@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/arr-diff/-/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
    integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=
  
  arr-flatten@^1.0.1, arr-flatten@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
    integrity sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==
  
  arr-union@^2.0.1:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/arr-union/-/arr-union-2.1.0.tgz#20f9eab5ec70f5c7d215b1077b1c39161d292c7d"
    integrity sha1-IPnqtexw9cfSFbEHexw5Fh0pLH0=
  
  arr-union@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/arr-union/-/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
    integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=
  
  array-differ@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/array-differ/-/array-differ-3.0.0.tgz#3cbb3d0f316810eafcc47624734237d6aee4ae6b"
    integrity sha512-THtfYS6KtME/yIAhKjZ2ul7XI96lQGHRputJQHO80LAWQnuGP4iCIN8vdMRboGbIEYBwU33q8Tch1os2+X0kMg==
  
  array-filter@~0.0.0:
    version "0.0.1"
    resolved "https://registry.yarnpkg.com/array-filter/-/array-filter-0.0.1.tgz#7da8cf2e26628ed732803581fd21f67cacd2eeec"
    integrity sha1-fajPLiZijtcygDWB/SH2fKzS7uw=
  
  array-find-index@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/array-find-index/-/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
    integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=
  
  array-includes@^3.0.3:
    version "3.1.1"
    resolved "https://registry.yarnpkg.com/array-includes/-/array-includes-3.1.1.tgz#cdd67e6852bdf9c1215460786732255ed2459348"
    integrity sha512-c2VXaCHl7zPsvpkFsw4nxvFie4fh1ur9bpcgsVkIjqn0H/Xwdg+7fv3n2r/isyS8EBj5b06M9kHyZuIr4El6WQ==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.0"
      is-string "^1.0.5"
  
  array-map@~0.0.0:
    version "0.0.0"
    resolved "https://registry.yarnpkg.com/array-map/-/array-map-0.0.0.tgz#88a2bab73d1cf7bcd5c1b118a003f66f665fa662"
    integrity sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI=
  
  array-reduce@~0.0.0:
    version "0.0.0"
    resolved "https://registry.yarnpkg.com/array-reduce/-/array-reduce-0.0.0.tgz#173899d3ffd1c7d9383e4479525dbe278cab5f2b"
    integrity sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys=
  
  array-slice@^0.2.3:
    version "0.2.3"
    resolved "https://registry.yarnpkg.com/array-slice/-/array-slice-0.2.3.tgz#dd3cfb80ed7973a75117cdac69b0b99ec86186f5"
    integrity sha1-3Tz7gO15c6dRF82sabC5nshhhvU=
  
  array-union@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
    integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==
  
  array-unique@^0.3.2:
    version "0.3.2"
    resolved "https://registry.yarnpkg.com/array-unique/-/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
    integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=
  
  array.prototype.flat@^1.2.1:
    version "1.2.3"
    resolved "https://registry.yarnpkg.com/array.prototype.flat/-/array.prototype.flat-1.2.3.tgz#0de82b426b0318dbfdb940089e38b043d37f6c7b"
    integrity sha512-gBlRZV0VSmfPIeWfuuy56XZMvbVfbEUnOXUvt3F/eUUUSyzlgLxhEX4YAEpxNAogRGehPSnfXyPtYyKAhkzQhQ==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.0-next.1"
  
  arrify@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/arrify/-/arrify-2.0.1.tgz#c9655e9331e0abcd588d2a7cad7e9956f66701fa"
    integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==
  
  art@^0.10.0:
    version "0.10.3"
    resolved "https://registry.yarnpkg.com/art/-/art-0.10.3.tgz#b01d84a968ccce6208df55a733838c96caeeaea2"
    integrity sha512-HXwbdofRTiJT6qZX/FnchtldzJjS3vkLJxQilc3Xj+ma2MXjY4UAyQ0ls1XZYVnDvVIBiFZbC6QsvtW86TD6tQ==
  
  asap@~2.0.3:
    version "2.0.6"
    resolved "https://registry.yarnpkg.com/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
    integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=
  
  assign-symbols@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/assign-symbols/-/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
    integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=
  
  ast-types-flow@0.0.7, ast-types-flow@^0.0.7:
    version "0.0.7"
    resolved "https://registry.yarnpkg.com/ast-types-flow/-/ast-types-flow-0.0.7.tgz#f70b735c6bca1a5c9c22d982c3e39e7feba3bdad"
    integrity sha1-9wtzXGvKGlycItmCw+Oef+ujva0=
  
  astral-regex@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/astral-regex/-/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
    integrity sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==
  
  async-limiter@~1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/async-limiter/-/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
    integrity sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==
  
  async@^2.4.0:
    version "2.6.3"
    resolved "https://registry.yarnpkg.com/async/-/async-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
    integrity sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==
    dependencies:
      lodash "^4.17.14"
  
  atob@^2.1.2:
    version "2.1.2"
    resolved "https://registry.yarnpkg.com/atob/-/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
    integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==
  
  axobject-query@^2.0.2:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/axobject-query/-/axobject-query-2.1.1.tgz#2a3b1271ec722d48a4cd4b3fcc20c853326a49a7"
    integrity sha512-lF98xa/yvy6j3fBHAgQXIYl+J4eZadOSqsPojemUqClzNbBV38wWGpUbQbVEyf4eUF5yF7eHmGgGA2JiHyjeqw==
    dependencies:
      "@babel/runtime" "^7.7.4"
      "@babel/runtime-corejs3" "^7.7.4"
  
  babel-eslint@10.0.3:
    version "10.0.3"
    resolved "https://registry.yarnpkg.com/babel-eslint/-/babel-eslint-10.0.3.tgz#81a2c669be0f205e19462fed2482d33e4687a88a"
    integrity sha512-z3U7eMY6r/3f3/JB9mTsLjyxrv0Yb1zb8PCWCLpguxfCzBIZUwy23R1t/XKewP+8mEN2Ck8Dtr4q20z6ce6SoA==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      "@babel/parser" "^7.0.0"
      "@babel/traverse" "^7.0.0"
      "@babel/types" "^7.0.0"
      eslint-visitor-keys "^1.0.0"
      resolve "^1.12.0"
  
  babel-plugin-dynamic-import-node@^2.3.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.0.tgz#f00f507bdaa3c3e3ff6e7e5e98d90a7acab96f7f"
    integrity sha512-o6qFkpeQEBxcqt0XYlWzAVxNCSCZdUgcR8IRlhD/8DylxjjO4foPcvTW0GGKa/cVt3rvxZ7o5ippJ+/0nvLhlQ==
    dependencies:
      object.assign "^4.1.0"
  
  babel-plugin-module-resolver@^3.2.0:
    version "3.2.0"
    resolved "https://registry.yarnpkg.com/babel-plugin-module-resolver/-/babel-plugin-module-resolver-3.2.0.tgz#ddfa5e301e3b9aa12d852a9979f18b37881ff5a7"
    integrity sha512-tjR0GvSndzPew/Iayf4uICWZqjBwnlMWjSx6brryfQ81F9rxBVqwDJtFCV8oOs0+vJeefK9TmdZtkIFdFe1UnA==
    dependencies:
      find-babel-config "^1.1.0"
      glob "^7.1.2"
      pkg-up "^2.0.0"
      reselect "^3.0.1"
      resolve "^1.4.0"
  
  babel-plugin-module-resolver@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/babel-plugin-module-resolver/-/babel-plugin-module-resolver-4.0.0.tgz#8f3a3d9d48287dc1d3b0d5595113adabd36a847f"
    integrity sha512-3pdEq3PXALilSJ6dnC4wMWr0AZixHRM4utpdpBR9g5QG7B7JwWyukQv7a9hVxkbGFl+nQbrHDqqQOIBtTXTP/Q==
    dependencies:
      find-babel-config "^1.2.0"
      glob "^7.1.6"
      pkg-up "^3.1.0"
      reselect "^4.0.0"
      resolve "^1.13.1"
  
  babel-plugin-react-native-web@^0.11.7:
    version "0.11.7"
    resolved "https://registry.yarnpkg.com/babel-plugin-react-native-web/-/babel-plugin-react-native-web-0.11.7.tgz#15b578c0731bd7d65d334f9c759d95e8e4a602e2"
    integrity sha512-CxE7uhhqkzAFkwV2X7+Mc/UVPujQQDtja/EGxCXRJvdYRi72QTmaJYKbK1lV9qgTZuB+TDguU89coaA9Z1BNbg==
  
  babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0:
    version "7.0.0-beta.0"
    resolved "https://registry.yarnpkg.com/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz#aa213c1435e2bffeb6fca842287ef534ad05d5cf"
    integrity sha512-Xj9XuRuz3nTSbaTXWv3itLOcxyF4oPD8douBBmj7U9BBC6nEBYfyOJYQMf/8PJAFotC62UY5dFfIGEPr7WswzQ==
  
  babel-preset-expo@^8.1.0, babel-preset-expo@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/babel-preset-expo/-/babel-preset-expo-8.1.0.tgz#7dfdebe6dad80edd1901e0f733ca6a6cce2ad605"
    integrity sha512-ZlGIo8OlO0b7S//QrqHGIIf2BY9HId5efxgBxyic5ZbKo6NHICThjSpEz4rRyQRGka7HixBq/Jyjsn4M2D/n/g==
    dependencies:
      "@babel/plugin-proposal-decorators" "^7.6.0"
      "@babel/preset-env" "^7.6.3"
      babel-plugin-module-resolver "^3.2.0"
      babel-plugin-react-native-web "^0.11.7"
      metro-react-native-babel-preset "^0.56.0"
  
  babel-preset-fbjs@^3.1.2, babel-preset-fbjs@^3.2.0:
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/babel-preset-fbjs/-/babel-preset-fbjs-3.3.0.tgz#a6024764ea86c8e06a22d794ca8b69534d263541"
    integrity sha512-7QTLTCd2gwB2qGoi5epSULMHugSVgpcVt5YAeiFO9ABLrutDQzKfGwzxgZHLpugq8qMdg/DhRZDZ5CLKxBkEbw==
    dependencies:
      "@babel/plugin-proposal-class-properties" "^7.0.0"
      "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
      "@babel/plugin-syntax-class-properties" "^7.0.0"
      "@babel/plugin-syntax-flow" "^7.0.0"
      "@babel/plugin-syntax-jsx" "^7.0.0"
      "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
      "@babel/plugin-transform-arrow-functions" "^7.0.0"
      "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
      "@babel/plugin-transform-block-scoping" "^7.0.0"
      "@babel/plugin-transform-classes" "^7.0.0"
      "@babel/plugin-transform-computed-properties" "^7.0.0"
      "@babel/plugin-transform-destructuring" "^7.0.0"
      "@babel/plugin-transform-flow-strip-types" "^7.0.0"
      "@babel/plugin-transform-for-of" "^7.0.0"
      "@babel/plugin-transform-function-name" "^7.0.0"
      "@babel/plugin-transform-literals" "^7.0.0"
      "@babel/plugin-transform-member-expression-literals" "^7.0.0"
      "@babel/plugin-transform-modules-commonjs" "^7.0.0"
      "@babel/plugin-transform-object-super" "^7.0.0"
      "@babel/plugin-transform-parameters" "^7.0.0"
      "@babel/plugin-transform-property-literals" "^7.0.0"
      "@babel/plugin-transform-react-display-name" "^7.0.0"
      "@babel/plugin-transform-react-jsx" "^7.0.0"
      "@babel/plugin-transform-shorthand-properties" "^7.0.0"
      "@babel/plugin-transform-spread" "^7.0.0"
      "@babel/plugin-transform-template-literals" "^7.0.0"
      babel-plugin-syntax-trailing-function-commas "^7.0.0-beta.0"
  
  badgin@^1.1.2:
    version "1.1.4"
    resolved "https://registry.yarnpkg.com/badgin/-/badgin-1.1.4.tgz#5a8e2b81e812d3c4af7bacc2e62b36a9a5e40de5"
    integrity sha512-BQ1m7TA7IehXb3/9b3cNH6TwIKcdqqJa/E4Z4fO40tSs6HPZWopPvx9QgHeUEd6Aays1BxQXjBpO+yrSYuRSOw==
  
  balanced-match@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
    integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=
  
  base64-js@^1.1.2, base64-js@^1.2.3:
    version "1.3.1"
    resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"
    integrity sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g==
  
  base@^0.11.1:
    version "0.11.2"
    resolved "https://registry.yarnpkg.com/base/-/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
    integrity sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==
    dependencies:
      cache-base "^1.0.1"
      class-utils "^0.3.5"
      component-emitter "^1.2.1"
      define-property "^1.0.0"
      isobject "^3.0.1"
      mixin-deep "^1.2.0"
      pascalcase "^0.1.1"
  
  basic-auth@~2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/basic-auth/-/basic-auth-2.0.1.tgz#b998279bf47ce38344b4f3cf916d4679bbf51e3a"
    integrity sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==
    dependencies:
      safe-buffer "5.1.2"
  
  big-integer@^1.6.44:
    version "1.6.48"
    resolved "https://registry.yarnpkg.com/big-integer/-/big-integer-1.6.48.tgz#8fd88bd1632cba4a1c8c3e3d7159f08bb95b4b9e"
    integrity sha512-j51egjPa7/i+RdiRuJbPdJ2FIUYYPhvYLjzoYbcMMm62ooO6F94fETG4MTs46zPAF9Brs04OajboA/qTGuz78w==
  
  bindings@^1.5.0:
    version "1.5.0"
    resolved "https://registry.yarnpkg.com/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
    integrity sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==
    dependencies:
      file-uri-to-path "1.0.0"
  
  blueimp-md5@^2.10.0:
    version "2.12.0"
    resolved "https://registry.yarnpkg.com/blueimp-md5/-/blueimp-md5-2.12.0.tgz#be7367938a889dec3ffbb71138617c117e9c130a"
    integrity sha512-zo+HIdIhzojv6F1siQPqPFROyVy7C50KzHv/k/Iz+BtvtVzSHXiMXOpq2wCfNkeBqdCv+V8XOV96tsEt2W/3rQ==
  
  boolbase@^1.0.0, boolbase@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
    integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=
  
  bplist-creator@0.0.8:
    version "0.0.8"
    resolved "https://registry.yarnpkg.com/bplist-creator/-/bplist-creator-0.0.8.tgz#56b2a6e79e9aec3fc33bf831d09347d73794e79c"
    integrity sha512-Za9JKzD6fjLC16oX2wsXfc+qBEhJBJB1YPInoAQpMLhDuj5aVOv1baGeIQSq1Fr3OCqzvsoQcSBSwGId/Ja2PA==
    dependencies:
      stream-buffers "~2.2.0"
  
  bplist-parser@0.2.0:
    version "0.2.0"
    resolved "https://registry.yarnpkg.com/bplist-parser/-/bplist-parser-0.2.0.tgz#43a9d183e5bf9d545200ceac3e712f79ebbe8d0e"
    integrity sha512-z0M+byMThzQmD9NILRniCUXYsYpjwnlO8N5uCFaCqIOpqRsJCrQL9NK3JsD67CN5a08nF5oIL2bD6loTdHOuKw==
    dependencies:
      big-integer "^1.6.44"
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  braces@^2.3.1:
    version "2.3.2"
    resolved "https://registry.yarnpkg.com/braces/-/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
    integrity sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==
    dependencies:
      arr-flatten "^1.1.0"
      array-unique "^0.3.2"
      extend-shallow "^2.0.1"
      fill-range "^4.0.0"
      isobject "^3.0.1"
      repeat-element "^1.1.2"
      snapdragon "^0.8.1"
      snapdragon-node "^2.0.1"
      split-string "^3.0.2"
      to-regex "^3.0.1"
  
  browserslist@^4.6.0, browserslist@^4.8.2:
    version "4.8.3"
    resolved "https://registry.yarnpkg.com/browserslist/-/browserslist-4.8.3.tgz#65802fcd77177c878e015f0e3189f2c4f627ba44"
    integrity sha512-iU43cMMknxG1ClEZ2MDKeonKE1CCrFVkQK2AqO2YWFmvIrx4JWrvQ4w4hQez6EpVI8rHTtqh/ruHHDHSOKxvUg==
    dependencies:
      caniuse-lite "^1.0.30001017"
      electron-to-chromium "^1.3.322"
      node-releases "^1.1.44"
  
  bser@2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/bser/-/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
    integrity sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==
    dependencies:
      node-int64 "^0.4.0"
  
  buffer-alloc-unsafe@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"
    integrity sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg==
  
  buffer-alloc@^1.1.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/buffer-alloc/-/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
    integrity sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==
    dependencies:
      buffer-alloc-unsafe "^1.1.0"
      buffer-fill "^1.0.0"
  
  buffer-crc32@^0.2.13:
    version "0.2.13"
    resolved "https://registry.yarnpkg.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
    integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=
  
  buffer-fill@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/buffer-fill/-/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"
    integrity sha1-+PeLdniYiO858gXNY39o5wISKyw=
  
  buffer-from@^1.0.0:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/buffer-from/-/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
    integrity sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==
  
  bytes@3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
    integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=
  
  cache-base@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/cache-base/-/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
    integrity sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==
    dependencies:
      collection-visit "^1.0.0"
      component-emitter "^1.2.1"
      get-value "^2.0.6"
      has-value "^1.0.0"
      isobject "^3.0.1"
      set-value "^2.0.0"
      to-object-path "^0.3.0"
      union-value "^1.0.0"
      unset-value "^1.0.0"
  
  caller-callsite@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/caller-callsite/-/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
    integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
    dependencies:
      callsites "^2.0.0"
  
  caller-path@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/caller-path/-/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
    integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
    dependencies:
      caller-callsite "^2.0.0"
  
  callsites@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/callsites/-/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
    integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
    integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
  
  camelcase@5.0.0:
    version "5.0.0"
    resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-5.0.0.tgz#03295527d58bd3cd4aa75363f35b2e8d97be2f42"
    integrity sha512-faqwZqnWxbxn+F1d399ygeamQNy3lPp/H9H6rNrqYh4FSVCtcY+3cub1MxA8o9mDd55mM8Aghuu/kuyYA6VTsA==
  
  camelcase@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
    integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=
  
  camelcase@^5.0.0, camelcase@^5.3.1:
    version "5.3.1"
    resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
    integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==
  
  caniuse-lite@^1.0.30001017:
    version "1.0.30001019"
    resolved "https://registry.yarnpkg.com/caniuse-lite/-/caniuse-lite-1.0.30001019.tgz#857e3fccaad2b2feb3f1f6d8a8f62d747ea648e1"
    integrity sha512-6ljkLtF1KM5fQ+5ZN0wuyVvvebJxgJPTmScOMaFuQN2QuOzvRJnWSKfzQskQU5IOU4Gap3zasYPIinzwUjoj/g==
  
  capture-exit@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/capture-exit/-/capture-exit-2.0.0.tgz#fb953bfaebeb781f62898239dabb426d08a509a4"
    integrity sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g==
    dependencies:
      rsvp "^4.8.4"
  
  caseless@^0.12.0:
    version "0.12.0"
    resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
    integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=
  
  chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
    version "2.4.2"
    resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
    integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chardet@^0.4.0:
    version "0.4.2"
    resolved "https://registry.yarnpkg.com/chardet/-/chardet-0.4.2.tgz#b5473b33dc97c424e5d98dc87d55d4d8a29c8bf2"
    integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=
  
  chardet@^0.7.0:
    version "0.7.0"
    resolved "https://registry.yarnpkg.com/chardet/-/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
    integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==
  
  ci-info@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/ci-info/-/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
    integrity sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==
  
  class-utils@^0.3.5:
    version "0.3.6"
    resolved "https://registry.yarnpkg.com/class-utils/-/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
    integrity sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==
    dependencies:
      arr-union "^3.1.0"
      define-property "^0.2.5"
      isobject "^3.0.0"
      static-extend "^0.1.1"
  
  cli-cursor@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
    integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
    dependencies:
      restore-cursor "^2.0.0"
  
  cli-cursor@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
    integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
    dependencies:
      restore-cursor "^3.1.0"
  
  cli-spinners@^2.0.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/cli-spinners/-/cli-spinners-2.2.0.tgz#e8b988d9206c692302d8ee834e7a85c0144d8f77"
    integrity sha512-tgU3fKwzYjiLEQgPMD9Jt+JjHVL9kW93FiIMX/l7rivvOD4/LL0Mf7gda3+4U2KJBloybwgj5KEoQgGRioMiKQ==
  
  cli-width@^2.0.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/cli-width/-/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"
    integrity sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=
  
  cliui@^3.2.0:
    version "3.2.0"
    resolved "https://registry.yarnpkg.com/cliui/-/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
    integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
    dependencies:
      string-width "^1.0.1"
      strip-ansi "^3.0.1"
      wrap-ansi "^2.0.0"
  
  cliui@^4.0.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/cliui/-/cliui-4.1.0.tgz#348422dbe82d800b3022eef4f6ac10bf2e4d1b49"
    integrity sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ==
    dependencies:
      string-width "^2.1.1"
      strip-ansi "^4.0.0"
      wrap-ansi "^2.0.0"
  
  clone-deep@^0.2.4:
    version "0.2.4"
    resolved "https://registry.yarnpkg.com/clone-deep/-/clone-deep-0.2.4.tgz#4e73dd09e9fb971cc38670c5dced9c1896481cc6"
    integrity sha1-TnPdCen7lxzDhnDF3O2cGJZIHMY=
    dependencies:
      for-own "^0.1.3"
      is-plain-object "^2.0.1"
      kind-of "^3.0.2"
      lazy-cache "^1.0.3"
      shallow-clone "^0.1.2"
  
  clone@^1.0.2:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/clone/-/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
    integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=
  
  coa@^2.0.2:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/coa/-/coa-2.0.2.tgz#43f6c21151b4ef2bf57187db0d73de229e3e7ec3"
    integrity sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA==
    dependencies:
      "@types/q" "^1.5.1"
      chalk "^2.4.1"
      q "^1.1.2"
  
  code-point-at@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
    integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=
  
  collection-visit@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/collection-visit/-/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
    integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
    dependencies:
      map-visit "^1.0.0"
      object-visit "^1.0.0"
  
  color-convert@^1.9.0, color-convert@^1.9.1:
    version "1.9.3"
    resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
    integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
    dependencies:
      color-name "1.1.3"
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
    integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=
  
  color-name@^1.0.0:
    version "1.1.4"
    resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
    integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
  
  color-string@^1.5.2:
    version "1.5.3"
    resolved "https://registry.yarnpkg.com/color-string/-/color-string-1.5.3.tgz#c9bbc5f01b58b5492f3d6857459cb6590ce204cc"
    integrity sha512-dC2C5qeWoYkxki5UAXapdjqO672AM4vZuPGRQfO8b5HKuKGBbKWpITyDYN7TOFKvRW7kOgAn3746clDBMDJyQw==
    dependencies:
      color-name "^1.0.0"
      simple-swizzle "^0.2.2"
  
  color-support@^1.1.3:
    version "1.1.3"
    resolved "https://registry.yarnpkg.com/color-support/-/color-support-1.1.3.tgz#93834379a1cc9a0c61f82f52f0d04322251bd5a2"
    integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==
  
  color@^3.1.0:
    version "3.1.2"
    resolved "https://registry.yarnpkg.com/color/-/color-3.1.2.tgz#68148e7f85d41ad7649c5fa8c8106f098d229e10"
    integrity sha512-vXTJhHebByxZn3lDvDJYw4lR5+uB3vuoHsuYA5AKuxRVn5wzzIfQKGLBmgdVRHKTJYeK5rvJcHnrd0Li49CFpg==
    dependencies:
      color-convert "^1.9.1"
      color-string "^1.5.2"
  
  colorette@^1.0.7:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/colorette/-/colorette-1.1.0.tgz#1f943e5a357fac10b4e0f5aaef3b14cdc1af6ec7"
    integrity sha512-6S062WDQUXi6hOfkO/sBPVwE5ASXY4G2+b4atvhJfSsuUUhIaUKlkjLe9692Ipyt5/a+IPF5aVTu3V5gvXq5cg==
  
  command-exists@^1.2.8:
    version "1.2.8"
    resolved "https://registry.yarnpkg.com/command-exists/-/command-exists-1.2.8.tgz#715acefdd1223b9c9b37110a149c6392c2852291"
    integrity sha512-PM54PkseWbiiD/mMsbvW351/u+dafwTJ0ye2qB60G1aGQP9j3xK2gmMDc+R34L3nDtx4qMCitXT75mkbkGJDLw==
  
  commander@^2.11.0, commander@^2.19.0:
    version "2.20.3"
    resolved "https://registry.yarnpkg.com/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
    integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==
  
  commander@~2.13.0:
    version "2.13.0"
    resolved "https://registry.yarnpkg.com/commander/-/commander-2.13.0.tgz#6964bca67685df7c1f1430c584f07d7597885b9c"
    integrity sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA==
  
  commondir@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
    integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=
  
  compare-versions@^3.4.0:
    version "3.5.1"
    resolved "https://registry.yarnpkg.com/compare-versions/-/compare-versions-3.5.1.tgz#26e1f5cf0d48a77eced5046b9f67b6b61075a393"
    integrity sha512-9fGPIB7C6AyM18CJJBHt5EnCZDG3oiTJYy0NjfIAGjKpzv0tkxWko7TNQHF5ymqm7IH03tqmeuBxtvD+Izh6mg==
  
  component-emitter@^1.2.1:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
    integrity sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==
  
  compressible@~2.0.16:
    version "2.0.18"
    resolved "https://registry.yarnpkg.com/compressible/-/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
    integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
    dependencies:
      mime-db ">= 1.43.0 < 2"
  
  compression@^1.7.1:
    version "1.7.4"
    resolved "https://registry.yarnpkg.com/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
    integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
    dependencies:
      accepts "~1.3.5"
      bytes "3.0.0"
      compressible "~2.0.16"
      debug "2.6.9"
      on-headers "~1.0.2"
      safe-buffer "5.1.2"
      vary "~1.1.2"
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
    integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=
  
  concat-stream@^1.6.0:
    version "1.6.2"
    resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
    integrity sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==
    dependencies:
      buffer-from "^1.0.0"
      inherits "^2.0.3"
      readable-stream "^2.2.2"
      typedarray "^0.0.6"
  
  confusing-browser-globals@^1.0.7:
    version "1.0.9"
    resolved "https://registry.yarnpkg.com/confusing-browser-globals/-/confusing-browser-globals-1.0.9.tgz#72bc13b483c0276801681871d4898516f8f54fdd"
    integrity sha512-KbS1Y0jMtyPgIxjO7ZzMAuUpAKMt1SzCL9fsrKsX6b0zJPTaT0SiSPmewwVZg9UAO83HVIlEhZF84LIjZ0lmAw==
  
  connect@^3.6.5:
    version "3.7.0"
    resolved "https://registry.yarnpkg.com/connect/-/connect-3.7.0.tgz#5d49348910caa5e07a01800b030d0c35f20484f8"
    integrity sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ==
    dependencies:
      debug "2.6.9"
      finalhandler "1.1.2"
      parseurl "~1.3.3"
      utils-merge "1.0.1"
  
  contains-path@^0.1.0:
    version "0.1.0"
    resolved "https://registry.yarnpkg.com/contains-path/-/contains-path-0.1.0.tgz#fe8cf184ff6670b6baef01a9d4861a5cbec4120a"
    integrity sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=
  
  convert-source-map@^1.7.0:
    version "1.7.0"
    resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
    integrity sha512-4FJkXzKXEDB1snCFZlLP4gpC3JILicCpGbzG9f9G7tGqGCzETQ2hWPrcinA9oU4wtf2biUaEH5065UnMeR33oA==
    dependencies:
      safe-buffer "~5.1.1"
  
  copy-descriptor@^0.1.0:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/copy-descriptor/-/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
    integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=
  
  core-js-compat@^3.6.0:
    version "3.6.1"
    resolved "https://registry.yarnpkg.com/core-js-compat/-/core-js-compat-3.6.1.tgz#39638c935c83c93a793abb628b252ec43e85783a"
    integrity sha512-2Tl1EuxZo94QS2VeH28Ebf5g3xbPZG/hj/N5HDDy4XMP/ImR0JIer/nggQRiMN91Q54JVkGbytf42wO29oXVHg==
    dependencies:
      browserslist "^4.8.2"
      semver "7.0.0"
  
  core-js-pure@^3.0.0:
    version "3.6.1"
    resolved "https://registry.yarnpkg.com/core-js-pure/-/core-js-pure-3.6.1.tgz#59acfb71caf2fb495aae4c1a0b2a7f2c1b65267e"
    integrity sha512-yKiUdvQWq66xUc408duxUCxFHuDfz5trF5V4xnQzb8C7P/5v2gFUdyNWQoevyAeGYB1hl1X/pzGZ20R3WxZQBA==
  
  core-js@^1.0.0:
    version "1.2.7"
    resolved "https://registry.yarnpkg.com/core-js/-/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"
    integrity sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=
  
  core-js@^2.2.2, core-js@^2.4.1:
    version "2.6.11"
    resolved "https://registry.yarnpkg.com/core-js/-/core-js-2.6.11.tgz#38831469f9922bded8ee21c9dc46985e0399308c"
    integrity sha512-5wjnpaT/3dV+XB4borEsnAYQchn00XSgTAWKDkEqv+K8KevjbzmofK6hfJ9TZIlpj2N0xQpazy7PiRQiWHqzWg==
  
  core-util-is@~1.0.0:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
    integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=
  
  cosmiconfig@^5.0.5, cosmiconfig@^5.1.0, cosmiconfig@^5.2.1:
    version "5.2.1"
    resolved "https://registry.yarnpkg.com/cosmiconfig/-/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
    integrity sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==
    dependencies:
      import-fresh "^2.0.0"
      is-directory "^0.3.1"
      js-yaml "^3.13.1"
      parse-json "^4.0.0"
  
  create-react-class@^15.6.2, create-react-class@^15.6.3:
    version "15.6.3"
    resolved "https://registry.yarnpkg.com/create-react-class/-/create-react-class-15.6.3.tgz#2d73237fb3f970ae6ebe011a9e66f46dbca80036"
    integrity sha512-M+/3Q6E6DLO6Yx3OwrWjwHBnvfXXYA7W+dFjt/ZDBemHO1DDZhsalX/NUtnTYclN6GfnBDRh4qRHjcDHmlJBJg==
    dependencies:
      fbjs "^0.8.9"
      loose-envify "^1.3.1"
      object-assign "^4.1.1"
  
  cross-spawn@^5.0.1, cross-spawn@^5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
    integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
    dependencies:
      lru-cache "^4.0.1"
      shebang-command "^1.2.0"
      which "^1.2.9"
  
  cross-spawn@^6.0.0, cross-spawn@^6.0.5:
    version "6.0.5"
    resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
    integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
    dependencies:
      nice-try "^1.0.4"
      path-key "^2.0.1"
      semver "^5.5.0"
      shebang-command "^1.2.0"
      which "^1.2.9"
  
  cross-spawn@^7.0.0:
    version "7.0.1"
    resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-7.0.1.tgz#0ab56286e0f7c24e153d04cc2aa027e43a9a5d14"
    integrity sha512-u7v4o84SwFpD32Z8IIcPZ6z1/ie24O6RU3RbtL5Y316l3KuHVPx9ItBgWQ6VlfAFnRnTtMUrsQ9MUUTuEZjogg==
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  crypto-js@^3.1.9-1:
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/crypto-js/-/crypto-js-3.3.0.tgz#846dd1cce2f68aacfa156c8578f926a609b7976b"
    integrity sha512-DIT51nX0dCfKltpRiXV+/TVZq+Qq2NgF4644+K7Ttnla7zEzqc+kjJyiB96BHNyUTBxyjzRcZYpUdZa+QAqi6Q==
  
  css-in-js-utils@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/css-in-js-utils/-/css-in-js-utils-2.0.1.tgz#****************************************"
    integrity sha512-PJF0SpJT+WdbVVt0AOYp9C8GnuruRlL/UFW7932nLWmFLQTaWEzTBQEx7/hn4BuV+WON75iAViSUJLiU3PKbpA==
    dependencies:
      hyphenate-style-name "^1.0.2"
      isobject "^3.0.1"
  
  css-select-base-adapter@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz#3b2ff4972cc362ab88561507a95408a1432135d7"
    integrity sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w==
  
  css-select@^2.0.0, css-select@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/css-select/-/css-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef"
    integrity sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==
    dependencies:
      boolbase "^1.0.0"
      css-what "^3.2.1"
      domutils "^1.7.0"
      nth-check "^1.0.2"
  
  css-tree@1.0.0-alpha.37:
    version "1.0.0-alpha.37"
    resolved "https://registry.yarnpkg.com/css-tree/-/css-tree-1.0.0-alpha.37.tgz#98bebd62c4c1d9f960ec340cf9f7522e30709a22"
    integrity sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==
    dependencies:
      mdn-data "2.0.4"
      source-map "^0.6.1"
  
  css-tree@1.0.0-alpha.39, css-tree@^1.0.0-alpha.39:
    version "1.0.0-alpha.39"
    resolved "https://registry.yarnpkg.com/css-tree/-/css-tree-1.0.0-alpha.39.tgz#2bff3ffe1bb3f776cf7eefd91ee5cba77a149eeb"
    integrity sha512-7UvkEYgBAHRG9Nt980lYxjsTrCyHFN53ky3wVsDkiMdVqylqRt+Zc+jm5qw7/qyOvN2dHSYtX0e4MbCCExSvnA==
    dependencies:
      mdn-data "2.0.6"
      source-map "^0.6.1"
  
  css-what@^3.2.1:
    version "3.2.1"
    resolved "https://registry.yarnpkg.com/css-what/-/css-what-3.2.1.tgz#f4a8f12421064621b456755e34a03a2c22df5da1"
    integrity sha512-WwOrosiQTvyms+Ti5ZC5vGEK0Vod3FTt1ca+payZqvKuGJF+dq7bG63DstxtN0dpm6FxY27a/zS3Wten+gEtGw==
  
  csso@^4.0.2:
    version "4.0.3"
    resolved "https://registry.yarnpkg.com/csso/-/csso-4.0.3.tgz#0d9985dc852c7cc2b2cacfbbe1079014d1a8e903"
    integrity sha512-NL3spysxUkcrOgnpsT4Xdl2aiEiBG6bXswAABQVHcMrfjjBisFOKwLDOmf4wf32aPdcJws1zds2B0Rg+jqMyHQ==
    dependencies:
      css-tree "1.0.0-alpha.39"
  
  csstype@^2.2.0:
    version "2.6.8"
    resolved "https://registry.yarnpkg.com/csstype/-/csstype-2.6.8.tgz#0fb6fc2417ffd2816a418c9336da74d7f07db431"
    integrity sha512-msVS9qTuMT5zwAGCVm4mxfrZ18BNc6Csd0oJAtiFMZ1FAx1CCvy2+5MDmYoix63LM/6NDbNtodCiGYGmFgO0dA==
  
  damerau-levenshtein@^1.0.4:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/damerau-levenshtein/-/damerau-levenshtein-1.0.5.tgz#780cf7144eb2e8dbd1c3bb83ae31100ccc31a414"
    integrity sha512-CBCRqFnpu715iPmw1KrdOrzRqbdFwQTwAWyyyYS42+iAgHCuXZ+/TdMgQkUENPomxEz9z1BEzuQU2Xw0kUuAgA==
  
  dayjs@^1.8.15:
    version "1.8.19"
    resolved "https://registry.yarnpkg.com/dayjs/-/dayjs-1.8.19.tgz#5117dc390d8f8e586d53891dbff3fa308f51abfe"
    integrity sha512-7kqOoj3oQSmqbvtvGFLU5iYqies+SqUiEGNT0UtUPPxcPYgY1BrkXR0Cq2R9HYSimBXN+xHkEN4Hi399W+Ovlg==
  
  debounce@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/debounce/-/debounce-1.2.0.tgz#44a540abc0ea9943018dc0eaa95cce87f65cd131"
    integrity sha512-mYtLl1xfZLi1m4RtQYlZgJUNQjl4ZxVnHzIR8nLLgi4q1YT8o/WM+MK/f8yfcc9s5Ir5zRaPZyZU6xs1Syoocg==
  
  debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.9:
    version "2.6.9"
    resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
    integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
    dependencies:
      ms "2.0.0"
  
  debug@4, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
    version "4.1.1"
    resolved "https://registry.yarnpkg.com/debug/-/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
    integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
    dependencies:
      ms "^2.1.1"
  
  decamelize@^1.1.1, decamelize@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
    integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=
  
  decode-uri-component@^0.2.0:
    version "0.2.0"
    resolved "https://registry.yarnpkg.com/decode-uri-component/-/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
    integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=
  
  deep-assign@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/deep-assign/-/deep-assign-3.0.0.tgz#c8e4c4d401cba25550a2f0f486a2e75bc5f219a2"
    integrity sha512-YX2i9XjJ7h5q/aQ/IM9PEwEnDqETAIYbggmdDB3HLTlSgo1CxPsj6pvhPG68rq6SVE0+p+6Ywsm5fTYNrYtBWw==
    dependencies:
      is-obj "^1.0.0"
  
  deep-diff@^0.3.5:
    version "0.3.8"
    resolved "https://registry.yarnpkg.com/deep-diff/-/deep-diff-0.3.8.tgz#c01de63efb0eec9798801d40c7e0dae25b582c84"
    integrity sha1-wB3mPvsO7JeYgB1Ax+Da4ltYLIQ=
  
  deep-is@~0.1.3:
    version "0.1.3"
    resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
    integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=
  
  deepmerge@^3.1.0, deepmerge@^3.2.0:
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/deepmerge/-/deepmerge-3.3.0.tgz#d3c47fd6f3a93d517b14426b0628a17b0125f5f7"
    integrity sha512-GRQOafGHwMHpjPx9iCvTgpu9NojZ49q794EEL94JVEw6VaeA8XTUyBKvAkOOjBX9oJNiV6G3P+T+tihFjo2TqA==
  
  defaults@^1.0.3:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/defaults/-/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
    integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
    dependencies:
      clone "^1.0.2"
  
  define-properties@^1.1.2, define-properties@^1.1.3:
    version "1.1.3"
    resolved "https://registry.yarnpkg.com/define-properties/-/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
    integrity sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==
    dependencies:
      object-keys "^1.0.12"
  
  define-property@^0.2.5:
    version "0.2.5"
    resolved "https://registry.yarnpkg.com/define-property/-/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
    integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
    dependencies:
      is-descriptor "^0.1.0"
  
  define-property@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/define-property/-/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
    integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
    dependencies:
      is-descriptor "^1.0.0"
  
  define-property@^2.0.2:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/define-property/-/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
    integrity sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==
    dependencies:
      is-descriptor "^1.0.2"
      isobject "^3.0.1"
  
  denodeify@^1.2.1:
    version "1.2.1"
    resolved "https://registry.yarnpkg.com/denodeify/-/denodeify-1.2.1.tgz#3a36287f5034e699e7577901052c2e6c94251631"
    integrity sha1-OjYof1A05pnnV3kBBSwubJQlFjE=
  
  depd@~1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
    integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=
  
  destroy@~1.0.4:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
    integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=
  
  doctrine@1.5.0:
    version "1.5.0"
    resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
    integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
    dependencies:
      esutils "^2.0.2"
      isarray "^1.0.0"
  
  doctrine@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
    integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
    dependencies:
      esutils "^2.0.2"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
    integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
    dependencies:
      esutils "^2.0.2"
  
  dom-serializer@0:
    version "0.2.2"
    resolved "https://registry.yarnpkg.com/dom-serializer/-/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
    integrity sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==
    dependencies:
      domelementtype "^2.0.1"
      entities "^2.0.0"
  
  domelementtype@1:
    version "1.3.1"
    resolved "https://registry.yarnpkg.com/domelementtype/-/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
    integrity sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==
  
  domelementtype@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/domelementtype/-/domelementtype-2.0.1.tgz#1f8bdfe91f5a78063274e803b4bdcedf6e94f94d"
    integrity sha512-5HOHUDsYZWV8FGWN0Njbr/Rn7f/eWSQi1v7+HsUVwXgn8nWWlL64zKDkS0n8ZmQ3mlWOMuXOnR+7Nx/5tMO5AQ==
  
  domutils@^1.7.0:
    version "1.7.0"
    resolved "https://registry.yarnpkg.com/domutils/-/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
    integrity sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==
    dependencies:
      dom-serializer "0"
      domelementtype "1"
  
  ee-first@1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
    integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=
  
  electron-to-chromium@^1.3.322:
    version "1.3.326"
    resolved "https://registry.yarnpkg.com/electron-to-chromium/-/electron-to-chromium-1.3.326.tgz#71715aca9afd328ea208a3bc4651c15b869f0d1b"
    integrity sha512-kaBmGWJlLW5bGEbm7/HWG9jt4oH+uecBIIfzFWfFkgqssPT2I6RDenGqo4wmKzm7seNu7DSCRZBXCuf7w8dtkQ==
  
  emoji-regex@^7.0.1, emoji-regex@^7.0.2:
    version "7.0.3"
    resolved "https://registry.yarnpkg.com/emoji-regex/-/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
    integrity sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==
  
  emoji-regex@^8.0.0:
    version "8.0.0"
    resolved "https://registry.yarnpkg.com/emoji-regex/-/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
    integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==
  
  encodeurl@~1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
    integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=
  
  encoding@^0.1.11:
    version "0.1.12"
    resolved "https://registry.yarnpkg.com/encoding/-/encoding-0.1.12.tgz#538b66f3ee62cd1ab51ec323829d1f9480c74beb"
    integrity sha1-U4tm8+5izRq1HsMjgp0flIDHS+s=
    dependencies:
      iconv-lite "~0.4.13"
  
  end-of-stream@^1.1.0:
    version "1.4.4"
    resolved "https://registry.yarnpkg.com/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
    integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
    dependencies:
      once "^1.4.0"
  
  entities@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/entities/-/entities-2.0.0.tgz#68d6084cab1b079767540d80e56a39b423e4abf4"
    integrity sha512-D9f7V0JSRwIxlRI2mjMqufDrRDnx8p+eEOz7aUM9SuvF8gsBzra0/6tbjl1m8eQHrZlYj6PxqE00hZ1SAIKPLw==
  
  envinfo@^7.1.0:
    version "7.5.0"
    resolved "https://registry.yarnpkg.com/envinfo/-/envinfo-7.5.0.tgz#91410bb6db262fb4f1409bd506e9ff57e91023f4"
    integrity sha512-jDgnJaF/Btomk+m3PZDTTCb5XIIIX3zYItnCRfF73zVgvinLoRomuhi75Y4su0PtQxWz4v66XnLLckyvyJTOIQ==
  
  error-ex@^1.2.0, error-ex@^1.3.1:
    version "1.3.2"
    resolved "https://registry.yarnpkg.com/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
    integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
    dependencies:
      is-arrayish "^0.2.1"
  
  errorhandler@^1.5.0:
    version "1.5.1"
    resolved "https://registry.yarnpkg.com/errorhandler/-/errorhandler-1.5.1.tgz#b9ba5d17cf90744cd1e851357a6e75bf806a9a91"
    integrity sha512-rcOwbfvP1WTViVoUjcfZicVzjhjTuhSMntHh6mW3IrEiyE6mJyXvsToJUJGlGlw/2xU9P5whlWNGlIDVeCiT4A==
    dependencies:
      accepts "~1.3.7"
      escape-html "~1.0.3"
  
  es-abstract@^1.17.0, es-abstract@^1.17.0-next.1:
    version "1.17.0"
    resolved "https://registry.yarnpkg.com/es-abstract/-/es-abstract-1.17.0.tgz#f42a517d0036a5591dbb2c463591dc8bb50309b1"
    integrity sha512-yYkE07YF+6SIBmg1MsJ9dlub5L48Ek7X0qz+c/CPCHS9EBXfESorzng4cJQjJW5/pB6vDF41u7F8vUhLVDqIug==
    dependencies:
      es-to-primitive "^1.2.1"
      function-bind "^1.1.1"
      has "^1.0.3"
      has-symbols "^1.0.1"
      is-callable "^1.1.5"
      is-regex "^1.0.5"
      object-inspect "^1.7.0"
      object-keys "^1.1.1"
      object.assign "^4.1.0"
      string.prototype.trimleft "^2.1.1"
      string.prototype.trimright "^2.1.1"
  
  es-abstract@^1.17.2:
    version "1.17.5"
    resolved "https://registry.yarnpkg.com/es-abstract/-/es-abstract-1.17.5.tgz#d8c9d1d66c8981fb9200e2251d799eee92774ae9"
    integrity sha512-BR9auzDbySxOcfog0tLECW8l28eRGpDpU3Dm3Hp4q/N+VtLTmyj4EUN088XZWQDW/hzj6sYRDXeOFsaAODKvpg==
    dependencies:
      es-to-primitive "^1.2.1"
      function-bind "^1.1.1"
      has "^1.0.3"
      has-symbols "^1.0.1"
      is-callable "^1.1.5"
      is-regex "^1.0.5"
      object-inspect "^1.7.0"
      object-keys "^1.1.1"
      object.assign "^4.1.0"
      string.prototype.trimleft "^2.1.1"
      string.prototype.trimright "^2.1.1"
  
  es-to-primitive@^1.2.1:
    version "1.2.1"
    resolved "https://registry.yarnpkg.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
    integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
    dependencies:
      is-callable "^1.1.4"
      is-date-object "^1.0.1"
      is-symbol "^1.0.2"
  
  escape-html@~1.0.3:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
    integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=
  
  escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
    integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=
  
  eslint-config-airbnb-base@^14.0.0:
    version "14.0.0"
    resolved "https://registry.yarnpkg.com/eslint-config-airbnb-base/-/eslint-config-airbnb-base-14.0.0.tgz#8a7bcb9643d13c55df4dd7444f138bf4efa61e17"
    integrity sha512-2IDHobw97upExLmsebhtfoD3NAKhV4H0CJWP3Uprd/uk+cHuWYOczPVxQ8PxLFUAw7o3Th1RAU8u1DoUpr+cMA==
    dependencies:
      confusing-browser-globals "^1.0.7"
      object.assign "^4.1.0"
      object.entries "^1.1.0"
  
  eslint-config-airbnb-typescript@^6.3.1:
    version "6.3.1"
    resolved "https://registry.yarnpkg.com/eslint-config-airbnb-typescript/-/eslint-config-airbnb-typescript-6.3.1.tgz#28bd09099355324353a074a71951dd879cce7df0"
    integrity sha512-+tkkVysaN63zXz+oiPfkfYSRMIY5QfHI4qFeyb1ZhRGF2jR6JslqDv5GkrW/eciySNTVTigFvf9hkqHT9vklJw==
    dependencies:
      "@typescript-eslint/parser" "^2.3.0"
      eslint-config-airbnb "^18.0.1"
      eslint-config-airbnb-base "^14.0.0"
  
  eslint-config-airbnb@^18.0.1:
    version "18.0.1"
    resolved "https://registry.yarnpkg.com/eslint-config-airbnb/-/eslint-config-airbnb-18.0.1.tgz#a3a74cc29b46413b6096965025381df8fb908559"
    integrity sha512-hLb/ccvW4grVhvd6CT83bECacc+s4Z3/AEyWQdIT2KeTsG9dR7nx1gs7Iw4tDmGKozCNHFn4yZmRm3Tgy+XxyQ==
    dependencies:
      eslint-config-airbnb-base "^14.0.0"
      object.assign "^4.1.0"
      object.entries "^1.1.0"
  
  eslint-config-prettier@^6.0.0, eslint-config-prettier@^6.9.0:
    version "6.9.0"
    resolved "https://registry.yarnpkg.com/eslint-config-prettier/-/eslint-config-prettier-6.9.0.tgz#430d24822e82f7deb1e22a435bfa3999fae4ad64"
    integrity sha512-k4E14HBtcLv0uqThaI6I/n1LEqROp8XaPu6SO9Z32u5NlGRC07Enu1Bh2KEFw4FNHbekH8yzbIU9kUGxbiGmCA==
    dependencies:
      get-stdin "^6.0.0"
  
  eslint-import-resolver-alias@^1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/eslint-import-resolver-alias/-/eslint-import-resolver-alias-1.1.2.tgz#297062890e31e4d6651eb5eba9534e1f6e68fc97"
    integrity sha512-WdviM1Eu834zsfjHtcGHtGfcu+F30Od3V7I9Fi57uhBEwPkjDcii7/yW8jAT+gOhn4P/vOxxNAXbFAKsrrc15w==
  
  eslint-import-resolver-node@^0.3.2:
    version "0.3.2"
    resolved "https://registry.yarnpkg.com/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.2.tgz#58f15fb839b8d0576ca980413476aab2472db66a"
    integrity sha512-sfmTqJfPSizWu4aymbPr4Iidp5yKm8yDkHp+Ir3YiTHiiDfxh69mOUsmiqW6RZ9zRXFaF64GtYmN7e+8GHBv6Q==
    dependencies:
      debug "^2.6.9"
      resolve "^1.5.0"
  
  eslint-module-utils@^2.4.1:
    version "2.5.0"
    resolved "https://registry.yarnpkg.com/eslint-module-utils/-/eslint-module-utils-2.5.0.tgz#cdf0b40d623032274ccd2abd7e64c4e524d6e19c"
    integrity sha512-kCo8pZaNz2dsAW7nCUjuVoI11EBXXpIzfNxmaoLhXoRDOnqXLC4iSGVRdZPhOitfbdEfMEfKOiENaK6wDPZEGw==
    dependencies:
      debug "^2.6.9"
      pkg-dir "^2.0.0"
  
  eslint-plugin-eslint-comments@^3.1.2:
    version "3.1.2"
    resolved "https://registry.yarnpkg.com/eslint-plugin-eslint-comments/-/eslint-plugin-eslint-comments-3.1.2.tgz#4ef6c488dbe06aa1627fea107b3e5d059fc8a395"
    integrity sha512-QexaqrNeteFfRTad96W+Vi4Zj1KFbkHHNMMaHZEYcovKav6gdomyGzaxSDSL3GoIyUOo078wRAdYlu1caiauIQ==
    dependencies:
      escape-string-regexp "^1.0.5"
      ignore "^5.0.5"
  
  eslint-plugin-eslint-plugin@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/eslint-plugin-eslint-plugin/-/eslint-plugin-eslint-plugin-2.1.0.tgz#a7a00f15a886957d855feacaafee264f039e62d5"
    integrity sha512-kT3A/ZJftt28gbl/Cv04qezb/NQ1dwYIbi8lyf806XMxkus7DvOVCLIfTXMrorp322Pnoez7+zabXH29tADIDg==
  
  eslint-plugin-flowtype@2.50.3:
    version "2.50.3"
    resolved "https://registry.yarnpkg.com/eslint-plugin-flowtype/-/eslint-plugin-flowtype-2.50.3.tgz#61379d6dce1d010370acd6681740fd913d68175f"
    integrity sha512-X+AoKVOr7Re0ko/yEXyM5SSZ0tazc6ffdIOocp2fFUlWoDt7DV0Bz99mngOkAFLOAWjqRA5jPwqUCbrx13XoxQ==
    dependencies:
      lodash "^4.17.10"
  
  eslint-plugin-import@^2.19.1:
    version "2.19.1"
    resolved "https://registry.yarnpkg.com/eslint-plugin-import/-/eslint-plugin-import-2.19.1.tgz#5654e10b7839d064dd0d46cd1b88ec2133a11448"
    integrity sha512-x68131aKoCZlCae7rDXKSAQmbT5DQuManyXo2sK6fJJ0aK5CWAkv6A6HJZGgqC8IhjQxYPgo6/IY4Oz8AFsbBw==
    dependencies:
      array-includes "^3.0.3"
      array.prototype.flat "^1.2.1"
      contains-path "^0.1.0"
      debug "^2.6.9"
      doctrine "1.5.0"
      eslint-import-resolver-node "^0.3.2"
      eslint-module-utils "^2.4.1"
      has "^1.0.3"
      minimatch "^3.0.4"
      object.values "^1.1.0"
      read-pkg-up "^2.0.0"
      resolve "^1.12.0"
  
  eslint-plugin-jest@22.4.1:
    version "22.4.1"
    resolved "https://registry.yarnpkg.com/eslint-plugin-jest/-/eslint-plugin-jest-22.4.1.tgz#a5fd6f7a2a41388d16f527073b778013c5189a9c"
    integrity sha512-gcLfn6P2PrFAVx3AobaOzlIEevpAEf9chTpFZz7bYfc7pz8XRv7vuKTIE4hxPKZSha6XWKKplDQ0x9Pq8xX2mg==
  
  eslint-plugin-jsx-a11y@^6.2.3:
    version "6.2.3"
    resolved "https://registry.yarnpkg.com/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.2.3.tgz#b872a09d5de51af70a97db1eea7dc933043708aa"
    integrity sha512-CawzfGt9w83tyuVekn0GDPU9ytYtxyxyFZ3aSWROmnRRFQFT2BiPJd7jvRdzNDi6oLWaS2asMeYSNMjWTV4eNg==
    dependencies:
      "@babel/runtime" "^7.4.5"
      aria-query "^3.0.0"
      array-includes "^3.0.3"
      ast-types-flow "^0.0.7"
      axobject-query "^2.0.2"
      damerau-levenshtein "^1.0.4"
      emoji-regex "^7.0.2"
      has "^1.0.3"
      jsx-ast-utils "^2.2.1"
  
  eslint-plugin-prettier@2.6.2:
    version "2.6.2"
    resolved "https://registry.yarnpkg.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.6.2.tgz#71998c60aedfa2141f7bfcbf9d1c459bf98b4fad"
    integrity sha512-tGek5clmW5swrAx1mdPYM8oThrBE83ePh7LeseZHBWfHVGrHPhKn7Y5zgRMbU/9D5Td9K4CEmUPjGxA7iw98Og==
    dependencies:
      fast-diff "^1.1.1"
      jest-docblock "^21.0.0"
  
  eslint-plugin-prettier@^3.1.2:
    version "3.1.2"
    resolved "https://registry.yarnpkg.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.2.tgz#432e5a667666ab84ce72f945c72f77d996a5c9ba"
    integrity sha512-GlolCC9y3XZfv3RQfwGew7NnuFDKsfI4lbvRK+PIIo23SFH+LemGs4cKwzAaRa+Mdb+lQO/STaIayno8T5sJJA==
    dependencies:
      prettier-linter-helpers "^1.0.0"
  
  eslint-plugin-react-hooks@^2.0.1:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-2.3.0.tgz#53e073961f1f5ccf8dd19558036c1fac8c29d99a"
    integrity sha512-gLKCa52G4ee7uXzdLiorca7JIQZPPXRAQDXV83J4bUEeUuc5pIEyZYAZ45Xnxe5IuupxEqHS+hUhSLIimK1EMw==
  
  eslint-plugin-react-native-globals@^0.1.1:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/eslint-plugin-react-native-globals/-/eslint-plugin-react-native-globals-0.1.2.tgz#ee1348bc2ceb912303ce6bdbd22e2f045ea86ea2"
    integrity sha512-9aEPf1JEpiTjcFAmmyw8eiIXmcNZOqaZyHO77wgm0/dWfT/oxC1SrIq8ET38pMxHYrcB6Uew+TzUVsBeczF88g==
  
  eslint-plugin-react-native@3.8.1:
    version "3.8.1"
    resolved "https://registry.yarnpkg.com/eslint-plugin-react-native/-/eslint-plugin-react-native-3.8.1.tgz#92811e37191ecb0d29c0f0a0c9e5c943ee573821"
    integrity sha512-6Z4s4nvgFRdda/1s1+uu4a6EMZwEjjJ9Bk/1yBImv0fd9U2CsGu2cUakAtV83cZKhizbWhSouXoaK4JtlScdFg==
    dependencies:
      eslint-plugin-react-native-globals "^0.1.1"
  
  eslint-plugin-react@7.16.0:
    version "7.16.0"
    resolved "https://registry.yarnpkg.com/eslint-plugin-react/-/eslint-plugin-react-7.16.0.tgz#9928e4f3e2122ed3ba6a5b56d0303ba3e41d8c09"
    integrity sha512-GacBAATewhhptbK3/vTP09CbFrgUJmBSaaRcWdbQLFvUZy9yVcQxigBNHGPU/KE2AyHpzj3AWXpxoMTsIDiHug==
    dependencies:
      array-includes "^3.0.3"
      doctrine "^2.1.0"
      has "^1.0.3"
      jsx-ast-utils "^2.2.1"
      object.entries "^1.1.0"
      object.fromentries "^2.0.0"
      object.values "^1.1.0"
      prop-types "^15.7.2"
      resolve "^1.12.0"
  
  eslint-plugin-react@^7.17.0:
    version "7.17.0"
    resolved "https://registry.yarnpkg.com/eslint-plugin-react/-/eslint-plugin-react-7.17.0.tgz#a31b3e134b76046abe3cd278e7482bd35a1d12d7"
    integrity sha512-ODB7yg6lxhBVMeiH1c7E95FLD4E/TwmFjltiU+ethv7KPdCwgiFuOZg9zNRHyufStTDLl/dEFqI2Q1VPmCd78A==
    dependencies:
      array-includes "^3.0.3"
      doctrine "^2.1.0"
      eslint-plugin-eslint-plugin "^2.1.0"
      has "^1.0.3"
      jsx-ast-utils "^2.2.3"
      object.entries "^1.1.0"
      object.fromentries "^2.0.1"
      object.values "^1.1.0"
      prop-types "^15.7.2"
      resolve "^1.13.1"
  
  eslint-scope@^4.0.0:
    version "4.0.3"
    resolved "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
    integrity sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==
    dependencies:
      esrecurse "^4.1.0"
      estraverse "^4.1.1"
  
  eslint-scope@^5.0.0:
    version "5.0.0"
    resolved "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-5.0.0.tgz#e87c8887c73e8d1ec84f1ca591645c358bfc8fb9"
    integrity sha512-oYrhJW7S0bxAFDvWqzvMPRm6pcgcnWc4QnofCAqRTRfQC0JcwenzGglTtsLyIuuWFfkqDG9vz67cnttSd53djw==
    dependencies:
      esrecurse "^4.1.0"
      estraverse "^4.1.1"
  
  eslint-utils@^1.3.1, eslint-utils@^1.4.3:
    version "1.4.3"
    resolved "https://registry.yarnpkg.com/eslint-utils/-/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
    integrity sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==
    dependencies:
      eslint-visitor-keys "^1.1.0"
  
  eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/eslint-visitor-keys/-/eslint-visitor-keys-1.1.0.tgz#e2a82cea84ff246ad6fb57f9bde5b46621459ec2"
    integrity sha512-8y9YjtM1JBJU/A9Kc+SbaOV4y29sSWckBwMHa+FGtVj5gN/sbnKDf6xJUl+8g7FAij9LVaP8C24DUiH/f/2Z9A==
  
  eslint@^6.8.0:
    version "6.8.0"
    resolved "https://registry.yarnpkg.com/eslint/-/eslint-6.8.0.tgz#62262d6729739f9275723824302fb227c8c93ffb"
    integrity sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      ajv "^6.10.0"
      chalk "^2.1.0"
      cross-spawn "^6.0.5"
      debug "^4.0.1"
      doctrine "^3.0.0"
      eslint-scope "^5.0.0"
      eslint-utils "^1.4.3"
      eslint-visitor-keys "^1.1.0"
      espree "^6.1.2"
      esquery "^1.0.1"
      esutils "^2.0.2"
      file-entry-cache "^5.0.1"
      functional-red-black-tree "^1.0.1"
      glob-parent "^5.0.0"
      globals "^12.1.0"
      ignore "^4.0.6"
      import-fresh "^3.0.0"
      imurmurhash "^0.1.4"
      inquirer "^7.0.0"
      is-glob "^4.0.0"
      js-yaml "^3.13.1"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.3.0"
      lodash "^4.17.14"
      minimatch "^3.0.4"
      mkdirp "^0.5.1"
      natural-compare "^1.4.0"
      optionator "^0.8.3"
      progress "^2.0.0"
      regexpp "^2.0.1"
      semver "^6.1.2"
      strip-ansi "^5.2.0"
      strip-json-comments "^3.0.1"
      table "^5.2.3"
      text-table "^0.2.0"
      v8-compile-cache "^2.0.3"
  
  espree@^6.1.2:
    version "6.1.2"
    resolved "https://registry.yarnpkg.com/espree/-/espree-6.1.2.tgz#6c272650932b4f91c3714e5e7b5f5e2ecf47262d"
    integrity sha512-2iUPuuPP+yW1PZaMSDM9eyVf8D5P0Hi8h83YtZ5bPc/zHYjII5khoixIUTMO794NOY8F/ThF1Bo8ncZILarUTA==
    dependencies:
      acorn "^7.1.0"
      acorn-jsx "^5.1.0"
      eslint-visitor-keys "^1.1.0"
  
  esprima@^4.0.0:
    version "4.0.1"
    resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
    integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==
  
  esquery@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/esquery/-/esquery-1.0.1.tgz#406c51658b1f5991a5f9b62b1dc25b00e3e5c708"
    integrity sha512-SmiyZ5zIWH9VM+SRUReLS5Q8a7GxtRdxEBVZpm98rJM7Sb+A9DVCndXfkeFUd3byderg+EbDkfnevfCwynWaNA==
    dependencies:
      estraverse "^4.0.0"
  
  esrecurse@^4.1.0:
    version "4.2.1"
    resolved "https://registry.yarnpkg.com/esrecurse/-/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
    integrity sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==
    dependencies:
      estraverse "^4.1.0"
  
  estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1:
    version "4.3.0"
    resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
    integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==
  
  esutils@^2.0.0, esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  etag@~1.8.1:
    version "1.8.1"
    resolved "https://registry.yarnpkg.com/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
    integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=
  
  event-target-shim@^5.0.0, event-target-shim@^5.0.1:
    version "5.0.1"
    resolved "https://registry.yarnpkg.com/event-target-shim/-/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
    integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==
  
  eventemitter3@^3.0.0:
    version "3.1.2"
    resolved "https://registry.yarnpkg.com/eventemitter3/-/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
    integrity sha512-tvtQIeLVHjDkJYnzf2dgVMxfuSGJeM/7UCG17TT4EumTfNtF+0nebF/4zWOIkCreAbtNqhGEboB6BWrwqNaw4Q==
  
  exec-sh@^0.3.2:
    version "0.3.4"
    resolved "https://registry.yarnpkg.com/exec-sh/-/exec-sh-0.3.4.tgz#3a018ceb526cc6f6df2bb504b2bfe8e3a4934ec5"
    integrity sha512-sEFIkc61v75sWeOe72qyrqg2Qg0OuLESziUDk/O/z2qgS15y2gWVFrI6f2Qn/qw/0/NCfCEsmNA4zOjkwEZT1A==
  
  execa@^0.7.0:
    version "0.7.0"
    resolved "https://registry.yarnpkg.com/execa/-/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
    integrity sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=
    dependencies:
      cross-spawn "^5.0.1"
      get-stream "^3.0.0"
      is-stream "^1.1.0"
      npm-run-path "^2.0.0"
      p-finally "^1.0.0"
      signal-exit "^3.0.0"
      strip-eof "^1.0.0"
  
  execa@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/execa/-/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
    integrity sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==
    dependencies:
      cross-spawn "^6.0.0"
      get-stream "^4.0.0"
      is-stream "^1.1.0"
      npm-run-path "^2.0.0"
      p-finally "^1.0.0"
      signal-exit "^3.0.0"
      strip-eof "^1.0.0"
  
  execa@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/execa/-/execa-2.1.0.tgz#e5d3ecd837d2a60ec50f3da78fd39767747bbe99"
    integrity sha512-Y/URAVapfbYy2Xp/gb6A0E7iR8xeqOCXsuuaoMn7A5PzrXUK84E1gyiEfq0wQd/GHA6GsoHWwhNq8anb0mleIw==
    dependencies:
      cross-spawn "^7.0.0"
      get-stream "^5.0.0"
      is-stream "^2.0.0"
      merge-stream "^2.0.0"
      npm-run-path "^3.0.0"
      onetime "^5.1.0"
      p-finally "^2.0.0"
      signal-exit "^3.0.2"
      strip-final-newline "^2.0.0"
  
  expand-brackets@^2.1.4:
    version "2.1.4"
    resolved "https://registry.yarnpkg.com/expand-brackets/-/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
    integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
    dependencies:
      debug "^2.3.3"
      define-property "^0.2.5"
      extend-shallow "^2.0.1"
      posix-character-classes "^0.1.0"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.1"
  
  expo-ads-admob@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-ads-admob/-/expo-ads-admob-8.1.0.tgz#95290122b90a938a0fa6d9639eeb922c3055077b"
    integrity sha512-QeKIEaa9UueusO2+YS9Snikn+H/if9zfBLnbOINypG1cx0QX7j8tP3gybmzt4Ra7H3lN1Hbe2Zq06tpaP2ySSw==
    dependencies:
      prop-types "^15.6.2"
  
  expo-asset@~8.1.4:
    version "8.1.4"
    resolved "https://registry.yarnpkg.com/expo-asset/-/expo-asset-8.1.4.tgz#5265c28c1c279b2f9779269195bec4caf250ba5d"
    integrity sha512-6YqW22F5issD1rgqBCja8TCA2dr+yf/89FdYs/jhQYpw5OJAnAvfpK3GL8h3jRCu1TvxZqhH5NeLG6f2b3oUqg==
    dependencies:
      blueimp-md5 "^2.10.0"
      invariant "^2.2.4"
      md5-file "^3.2.3"
      path-browserify "^1.0.0"
      url-parse "^1.4.4"
  
  expo-auth-session@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/expo-auth-session/-/expo-auth-session-1.0.1.tgz#f1d97e750731e8e717eb18341ebb209b113716b7"
    integrity sha512-AX8SmTfpuifKpACgqmOQD+6p/aoXqSpezkxF5XTYg6d8pFPBOqkile6gBVhb6SMtb1bh5QpLwaZiy/DdXwYgOQ==
    dependencies:
      expo-constants "~9.0.0"
      expo-web-browser "~8.1.1"
  
  expo-blur@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-blur/-/expo-blur-8.1.0.tgz#44823ddd1ed4db3864c20fa7d9b7b91b65af0d76"
    integrity sha512-X7UUmRPV6I/f+oKJqsgKFJnKshqseK8MukwdLpIwBh7VNHiYp5gK0ynq5KlLGG6kb9zo68acjsD27DYlerARJQ==
    dependencies:
      prop-types "^15.6.0"
  
  expo-constants@*, expo-constants@~9.0.0:
    version "9.0.0"
    resolved "https://registry.yarnpkg.com/expo-constants/-/expo-constants-9.0.0.tgz#35c600079ee91d38fe4f56375caae6e90f122fdd"
    integrity sha512-1kqZMM8Ez5JT3sTEx8I69fP6NYFLOJjeM6Z63dD/m2NiwvzSADiO5+BhghnWNGN1L3bxbgOjXS6EHtS7CdSfxA==
  
  expo-error-recovery@~1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/expo-error-recovery/-/expo-error-recovery-1.1.0.tgz#98b9de5400dbfd022d1631739f3bf5e7016565da"
    integrity sha512-33aRfPaXdAt0df1TL26JjM5qCAoEW8RAExjgMgunPcdQcf4sWiWFm3qYL8zrO/8DM4uUq4X2FCuPLHMlOYT/aw==
  
  expo-file-system@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-file-system/-/expo-file-system-8.1.0.tgz#d6aa66fa32c19982b94d0013f963c5ee972dfd6d"
    integrity sha512-xb4roeU8CotW8t3LkmsrliNbgFpY2KB+3sW1NnujnH39pFVwCd/kfujCYzRauj8aUy/HhSq+3xGkQTpC7pSjVw==
    dependencies:
      uuid "^3.4.0"
  
  expo-font@~8.1.0:
    version "8.1.1"
    resolved "https://registry.yarnpkg.com/expo-font/-/expo-font-8.1.1.tgz#40de629d7332887bdafae4756741f6979fc0043e"
    integrity sha512-z6008K7YSA7wpJ1mNyG2eSYUhEoFVjdL2uAbwaHFpsqwxDS4tcdKHoWkanIUiEnsjtHK7Uk0ywKJ8MRzmCaklw==
    dependencies:
      fbjs "1.0.0"
      fontfaceobserver "^2.1.0"
  
  expo-keep-awake@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-keep-awake/-/expo-keep-awake-8.1.0.tgz#3a1d8aa5a8395d40c7d79e1c93020ae5f848e664"
    integrity sha512-RNPwWvpwsJwJS8ZI1yklKyVQ6l2NNZBCN2aSgQMRza2SABnpFFzDLHQwMo7DC+nbmrOueMvCIDr0VI3xrzGfEg==
  
  expo-linear-gradient@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-linear-gradient/-/expo-linear-gradient-8.1.0.tgz#6933765cf1e76ef2928fd8495e779929699ac4c6"
    integrity sha512-AIy2pOXQRcgk2XE5IgAzd1S2jTFLutiDfveNm6m3fPAk00Rw4qFe98qzte1ayNrGYLJvQ2xq/Y7C0BmBP051mg==
  
  expo-localization@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-localization/-/expo-localization-8.1.0.tgz#6003133e75849022918ae5638af6d553a04cde37"
    integrity sha512-+wbsn9QLNG2GNOsbc+dfTQ39VEwrbn0TAungNqZ0UI9K2ZhrfQpAaAzAdN3TMOEhHluQKe0hiuULHpUgg8o7Kg==
    dependencies:
      rtl-detect "^1.0.2"
  
  expo-location@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-location/-/expo-location-8.1.0.tgz#6a71ad9a8b78d5f016a30a02013c3b28f46d0b1b"
    integrity sha512-G9JvsK1t9Z5Iybf+FCG81Jgm9Ee9leqpazxOPVabUJEWu/55Iex3yLGX04BuIA4ozAlJKBPzkhPdyqKdC7zrSw==
    dependencies:
      invariant "^2.2.4"
  
  expo-permissions@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-permissions/-/expo-permissions-8.1.0.tgz#a7f2ee91ba76ce3a467e7b10adaa9ca5201b226f"
    integrity sha512-QBHD+1J9+sGFnhoEGzMRchPweeEE0OJ9ehG/0l1BMRBA7qsLS9vRC1FTJ55NwjI0Kr4RTha9r6ZX1kZHT09f/w==
  
  expo-sqlite@~8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/expo-sqlite/-/expo-sqlite-8.1.0.tgz#858eb28e1143db281de8a49c99b4a953a81d0834"
    integrity sha512-ziw6dbV1/sZErDkoGjG0afokyuKQqDtUuJglbLz9rQ6zNS1ceF3AjuEyfsWPDc2LL+QEdcnQODW7VUJelIk+0Q==
    dependencies:
      "@expo/websql" "^1.0.1"
      "@types/websql" "^0.0.27"
      lodash "^4.17.15"
  
  expo-updates@~0.1.2:
    version "0.1.3"
    resolved "https://registry.yarnpkg.com/expo-updates/-/expo-updates-0.1.3.tgz#c0f44c98dd241b63c6b7dc122c67bf57e2ede8a1"
    integrity sha512-1uD7HeyJlJ5kNkuznZU/wt7MoHTfX3vHG1Hds0Bkj7cBkXOL4oZDB1X/sUBhIZRkfMccbBONZTDQsVkH1p8z4g==
    dependencies:
      fbemitter "^2.1.1"
  
  expo-web-browser@~8.1.0, expo-web-browser@~8.1.1:
    version "8.1.1"
    resolved "https://registry.yarnpkg.com/expo-web-browser/-/expo-web-browser-8.1.1.tgz#93a3d4af834e7bbd1afd54f0cc588016d592c5ae"
    integrity sha512-htiXVLo0mb0t8F2vFL3+QnmD97B9U0Ek9cp2BBKSCLeHaVry6RUMewT9HLLcBOJhz2Zdjwk7JmTJh6amF1h80Q==
  
  expo@^37.0.0:
    version "37.0.7"
    resolved "https://registry.yarnpkg.com/expo/-/expo-37.0.7.tgz#fdfb54cc91d06d58aa4839c3d0c1c4bd499f601e"
    integrity sha512-UI/5iWsL3isu1eJaDV1Iu7m/nepn2ywEDcW2Vl006RDF/z41hW/emBRpqNfBnOqyqlvHqN+Von4g/ANFTMZ5OA==
    dependencies:
      "@babel/runtime" "^7.1.2"
      "@expo/vector-icons" "^10.0.2"
      "@types/fbemitter" "^2.0.32"
      "@types/invariant" "^2.2.29"
      "@types/lodash.zipobject" "^4.1.4"
      "@types/qs" "^6.5.1"
      "@unimodules/core" "~5.1.0"
      "@unimodules/react-native-adapter" "~5.1.1"
      babel-preset-expo "~8.1.0"
      badgin "^1.1.2"
      cross-spawn "^6.0.5"
      expo-asset "~8.1.4"
      expo-constants "~9.0.0"
      expo-error-recovery "~1.1.0"
      expo-file-system "~8.1.0"
      expo-font "~8.1.0"
      expo-keep-awake "~8.1.0"
      expo-linear-gradient "~8.1.0"
      expo-location "~8.1.0"
      expo-permissions "~8.1.0"
      expo-sqlite "~8.1.0"
      expo-web-browser "~8.1.0"
      fbemitter "^2.1.1"
      invariant "^2.2.2"
      lodash "^4.6.0"
      md5-file "^3.2.3"
      nullthrows "^1.1.0"
      pretty-format "^23.6.0"
      prop-types "^15.6.0"
      qs "^6.5.0"
      react-native-view-shot "3.1.2"
      serialize-error "^2.1.0"
      unimodules-app-loader "~1.0.1"
      unimodules-barcode-scanner-interface "~5.1.0"
      unimodules-camera-interface "~5.1.0"
      unimodules-constants-interface "~5.1.0"
      unimodules-face-detector-interface "~5.1.0"
      unimodules-file-system-interface "~5.1.0"
      unimodules-font-interface "~5.1.0"
      unimodules-image-loader-interface "~5.1.0"
      unimodules-permissions-interface "~5.1.0"
      unimodules-sensors-interface "~5.1.0"
      unimodules-task-manager-interface "~5.1.0"
      uuid "^3.4.0"
  
  extend-shallow@^1.1.2:
    version "1.1.4"
    resolved "https://registry.yarnpkg.com/extend-shallow/-/extend-shallow-1.1.4.tgz#19d6bf94dfc09d76ba711f39b872d21ff4dd9071"
    integrity sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=
    dependencies:
      kind-of "^1.1.0"
  
  extend-shallow@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/extend-shallow/-/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
    integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
    dependencies:
      is-extendable "^0.1.0"
  
  extend-shallow@^3.0.0, extend-shallow@^3.0.2:
    version "3.0.2"
    resolved "https://registry.yarnpkg.com/extend-shallow/-/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
    integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
    dependencies:
      assign-symbols "^1.0.0"
      is-extendable "^1.0.1"
  
  external-editor@^2.0.4:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/external-editor/-/external-editor-2.2.0.tgz#045511cfd8d133f3846673d1047c154e214ad3d5"
    integrity sha512-bSn6gvGxKt+b7+6TKEv1ZycHleA7aHhRHyAqJyp5pbUFuYYNIzpZnQDk7AsYckyWdEnTeAnay0aCy2aV6iTk9A==
    dependencies:
      chardet "^0.4.0"
      iconv-lite "^0.4.17"
      tmp "^0.0.33"
  
  external-editor@^3.0.3:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/external-editor/-/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
    integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
    dependencies:
      chardet "^0.7.0"
      iconv-lite "^0.4.24"
      tmp "^0.0.33"
  
  extglob@^2.0.4:
    version "2.0.4"
    resolved "https://registry.yarnpkg.com/extglob/-/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
    integrity sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==
    dependencies:
      array-unique "^0.3.2"
      define-property "^1.0.0"
      expand-brackets "^2.1.4"
      extend-shallow "^2.0.1"
      fragment-cache "^0.2.1"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.1"
  
  fancy-log@^1.3.2:
    version "1.3.3"
    resolved "https://registry.yarnpkg.com/fancy-log/-/fancy-log-1.3.3.tgz#dbc19154f558690150a23953a0adbd035be45fc7"
    integrity sha512-k9oEhlyc0FrVh25qYuSELjr8oxsCoc4/LEZfg2iJJrfEk/tZL9bCoJE47gqAvI2m/AUjluCS4+3I0eTx8n3AEw==
    dependencies:
      ansi-gray "^0.1.1"
      color-support "^1.1.3"
      parse-node-version "^1.0.0"
      time-stamp "^1.0.0"
  
  fast-deep-equal@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"
    integrity sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=
  
  fast-diff@^1.1.1, fast-diff@^1.1.2:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/fast-diff/-/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
    integrity sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w==
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-levenshtein@~2.0.6:
    version "2.0.6"
    resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
    integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=
  
  fb-watchman@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/fb-watchman/-/fb-watchman-2.0.1.tgz#fc84fb39d2709cf3ff6d743706157bb5708a8a85"
    integrity sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg==
    dependencies:
      bser "2.1.1"
  
  fbemitter@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/fbemitter/-/fbemitter-2.1.1.tgz#523e14fdaf5248805bb02f62efc33be703f51865"
    integrity sha1-Uj4U/a9SSIBbsC9i78M75wP1GGU=
    dependencies:
      fbjs "^0.8.4"
  
  fbjs-css-vars@^1.0.0:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz#216551136ae02fe255932c3ec8775f18e2c078b8"
    integrity sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ==
  
  fbjs-scripts@^1.1.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/fbjs-scripts/-/fbjs-scripts-1.2.0.tgz#069a0c0634242d10031c6460ef1fccefcdae8b27"
    integrity sha512-5krZ8T0Bf8uky0abPoCLrfa7Orxd8UH4Qq8hRUF2RZYNMu+FmEOrBc7Ib3YVONmxTXTlLAvyrrdrVmksDb2OqQ==
    dependencies:
      "@babel/core" "^7.0.0"
      ansi-colors "^1.0.1"
      babel-preset-fbjs "^3.2.0"
      core-js "^2.4.1"
      cross-spawn "^5.1.0"
      fancy-log "^1.3.2"
      object-assign "^4.0.1"
      plugin-error "^0.1.2"
      semver "^5.1.0"
      through2 "^2.0.0"
  
  fbjs@1.0.0, fbjs@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/fbjs/-/fbjs-1.0.0.tgz#52c215e0883a3c86af2a7a776ed51525ae8e0a5a"
    integrity sha512-MUgcMEJaFhCaF1QtWGnmq9ZDRAzECTCRAF7O6UZIlAlkTs1SasiX9aP0Iw7wfD2mJ7wDTNfg2w7u5fSCwJk1OA==
    dependencies:
      core-js "^2.4.1"
      fbjs-css-vars "^1.0.0"
      isomorphic-fetch "^2.1.1"
      loose-envify "^1.0.0"
      object-assign "^4.1.0"
      promise "^7.1.1"
      setimmediate "^1.0.5"
      ua-parser-js "^0.7.18"
  
  fbjs@^0.8.4, fbjs@^0.8.9:
    version "0.8.17"
    resolved "https://registry.yarnpkg.com/fbjs/-/fbjs-0.8.17.tgz#c4d598ead6949112653d6588b01a5cdcd9f90fdd"
    integrity sha1-xNWY6taUkRJlPWWIsBpc3Nn5D90=
    dependencies:
      core-js "^1.0.0"
      isomorphic-fetch "^2.1.1"
      loose-envify "^1.0.0"
      object-assign "^4.1.0"
      promise "^7.1.1"
      setimmediate "^1.0.5"
      ua-parser-js "^0.7.18"
  
  figures@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
    integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
    dependencies:
      escape-string-regexp "^1.0.5"
  
  figures@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/figures/-/figures-3.1.0.tgz#4b198dd07d8d71530642864af2d45dd9e459c4ec"
    integrity sha512-ravh8VRXqHuMvZt/d8GblBeqDMkdJMBdv/2KntFH+ra5MXkO7nxNKpzQ3n6QD/2da1kH0aWmNISdvhM7gl2gVg==
    dependencies:
      escape-string-regexp "^1.0.5"
  
  file-entry-cache@^5.0.1:
    version "5.0.1"
    resolved "https://registry.yarnpkg.com/file-entry-cache/-/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
    integrity sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==
    dependencies:
      flat-cache "^2.0.1"
  
  file-uri-to-path@1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
    integrity sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==
  
  fill-range@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/fill-range/-/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
    integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
    dependencies:
      extend-shallow "^2.0.1"
      is-number "^3.0.0"
      repeat-string "^1.6.1"
      to-regex-range "^2.1.0"
  
  finalhandler@1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/finalhandler/-/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
    integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
    dependencies:
      debug "2.6.9"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      on-finished "~2.3.0"
      parseurl "~1.3.3"
      statuses "~1.5.0"
      unpipe "~1.0.0"
  
  find-babel-config@^1.1.0, find-babel-config@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/find-babel-config/-/find-babel-config-1.2.0.tgz#a9b7b317eb5b9860cda9d54740a8c8337a2283a2"
    integrity sha512-jB2CHJeqy6a820ssiqwrKMeyC6nNdmrcgkKWJWmpoxpE8RKciYJXCcXRq1h2AzCo5I5BJeN2tkGEO3hLTuePRA==
    dependencies:
      json5 "^0.5.1"
      path-exists "^3.0.0"
  
  find-cache-dir@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
    integrity sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==
    dependencies:
      commondir "^1.0.1"
      make-dir "^2.0.0"
      pkg-dir "^3.0.0"
  
  find-up@^2.0.0, find-up@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
    integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
    dependencies:
      locate-path "^2.0.0"
  
  find-up@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/find-up/-/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
    integrity sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==
    dependencies:
      locate-path "^3.0.0"
  
  find-up@^4.0.0, find-up@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
    integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
    dependencies:
      locate-path "^5.0.0"
      path-exists "^4.0.0"
  
  flat-cache@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/flat-cache/-/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
    integrity sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==
    dependencies:
      flatted "^2.0.0"
      rimraf "2.6.3"
      write "1.0.3"
  
  flatted@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/flatted/-/flatted-2.0.1.tgz#69e57caa8f0eacbc281d2e2cb458d46fdb449e08"
    integrity sha512-a1hQMktqW9Nmqr5aktAux3JMNqaucxGcjtjWnZLHX7yyPCmlSV3M54nGYbqT8K+0GhF3NBgmJCc3ma+WOgX8Jg==
  
  fontfaceobserver@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/fontfaceobserver/-/fontfaceobserver-2.1.0.tgz#e2705d293e2c585a6531c2a722905657317a2991"
    integrity sha512-ReOsO2F66jUa0jmv2nlM/s1MiutJx/srhAe2+TE8dJCMi02ZZOcCTxTCQFr3Yet+uODUtnr4Mewg+tNQ+4V1Ng==
  
  for-in@^0.1.3:
    version "0.1.8"
    resolved "https://registry.yarnpkg.com/for-in/-/for-in-0.1.8.tgz#d8773908e31256109952b1fdb9b3fa867d2775e1"
    integrity sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE=
  
  for-in@^1.0.1, for-in@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
    integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=
  
  for-own@^0.1.3:
    version "0.1.5"
    resolved "https://registry.yarnpkg.com/for-own/-/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
    integrity sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=
    dependencies:
      for-in "^1.0.1"
  
  fragment-cache@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/fragment-cache/-/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
    integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
    dependencies:
      map-cache "^0.2.2"
  
  fresh@0.5.2:
    version "0.5.2"
    resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
    integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=
  
  fs-copy-file-sync@^1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/fs-copy-file-sync/-/fs-copy-file-sync-1.1.1.tgz#11bf32c096c10d126e5f6b36d06eece776062918"
    integrity sha512-2QY5eeqVv4m2PfyMiEuy9adxNP+ajf+8AR05cEi+OAzPcOj90hvFImeZhTmKLBgSd9EvG33jsD7ZRxsx9dThkQ==
  
  fs-extra@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-1.0.0.tgz#cd3ce5f7e7cb6145883fcae3191e9877f8587950"
    integrity sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA=
    dependencies:
      graceful-fs "^4.1.2"
      jsonfile "^2.1.0"
      klaw "^1.0.0"
  
  fs-extra@^7.0.1:
    version "7.0.1"
    resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
    integrity sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw==
    dependencies:
      graceful-fs "^4.1.2"
      jsonfile "^4.0.0"
      universalify "^0.1.0"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
    integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=
  
  fsevents@^1.2.7:
    version "1.2.11"
    resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-1.2.11.tgz#67bf57f4758f02ede88fb2a1712fef4d15358be3"
    integrity sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw==
    dependencies:
      bindings "^1.5.0"
      nan "^2.12.1"
  
  function-bind@^1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
    integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==
  
  functional-red-black-tree@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
    integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=
  
  gensync@^1.0.0-beta.1:
    version "1.0.0-beta.1"
    resolved "https://registry.yarnpkg.com/gensync/-/gensync-1.0.0-beta.1.tgz#58f4361ff987e5ff6e1e7a210827aa371eaac269"
    integrity sha512-r8EC6NO1sngH/zdD9fiRDLdcgnbayXah+mLgManTaIZJqEC1MZstmnox8KpnI2/fxQwrp5OpCOYWLp4rBl4Jcg==
  
  get-caller-file@^1.0.1:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/get-caller-file/-/get-caller-file-1.0.3.tgz#f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a"
    integrity sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==
  
  get-stdin@^6.0.0:
    version "6.0.0"
    resolved "https://registry.yarnpkg.com/get-stdin/-/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
    integrity sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g==
  
  get-stdin@^7.0.0:
    version "7.0.0"
    resolved "https://registry.yarnpkg.com/get-stdin/-/get-stdin-7.0.0.tgz#8d5de98f15171a125c5e516643c7a6d0ea8a96f6"
    integrity sha512-zRKcywvrXlXsA0v0i9Io4KDRaAw7+a1ZpjRwl9Wox8PFlVCCHra7E9c4kqXCoCM9nR5tBkaTTZRBoCm60bFqTQ==
  
  get-stream@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
    integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=
  
  get-stream@^4.0.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
    integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
    dependencies:
      pump "^3.0.0"
  
  get-stream@^5.0.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-5.1.0.tgz#01203cdc92597f9b909067c3e656cc1f4d3c4dc9"
    integrity sha512-EXr1FOzrzTfGeL0gQdeFEvOMm2mzMOglyiOXSTpPC+iAjAKftbr3jpCMWynogwYnM+eSj9sHGc6wjIcDvYiygw==
    dependencies:
      pump "^3.0.0"
  
  get-value@^2.0.3, get-value@^2.0.6:
    version "2.0.6"
    resolved "https://registry.yarnpkg.com/get-value/-/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
    integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=
  
  glob-parent@^5.0.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-5.1.0.tgz#5f4c1d1e748d30cd73ad2944b3577a81b081e8c2"
    integrity sha512-qjtRgnIVmOfnKUE3NJAQEdk+lKrxfw8t5ke7SXtfMTHcjsBfOfWXCQfdb30zfDoZQ2IRSIiidmjtbHZPZ++Ihw==
    dependencies:
      is-glob "^4.0.1"
  
  glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.6:
    version "7.1.6"
    resolved "https://registry.yarnpkg.com/glob/-/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
    integrity sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.0.4"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  globals@^11.1.0:
    version "11.12.0"
    resolved "https://registry.yarnpkg.com/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
    integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==
  
  globals@^12.1.0:
    version "12.3.0"
    resolved "https://registry.yarnpkg.com/globals/-/globals-12.3.0.tgz#1e564ee5c4dded2ab098b0f88f24702a3c56be13"
    integrity sha512-wAfjdLgFsPZsklLJvOBUBmzYE8/CwhEqSBEMRXA3qxIiNtyqvjYurAtIfDh6chlEPUfmTY3MnZh5Hfh4q0UlIw==
    dependencies:
      type-fest "^0.8.1"
  
  graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9:
    version "4.2.3"
    resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.3.tgz#4a12ff1b60376ef09862c2093edd908328be8423"
    integrity sha512-a30VEBm4PEdx1dRB7MFK7BejejvCvBronbLjht+sHuGYj8PHs7M/5Z+rt5lw551vZ7yfTCj4Vuyy3mSJytDWRQ==
  
  growly@^1.3.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/growly/-/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"
    integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
    integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=
  
  has-symbols@^1.0.0, has-symbols@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8"
    integrity sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg==
  
  has-value@^0.3.1:
    version "0.3.1"
    resolved "https://registry.yarnpkg.com/has-value/-/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
    integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
    dependencies:
      get-value "^2.0.3"
      has-values "^0.1.4"
      isobject "^2.0.0"
  
  has-value@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/has-value/-/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
    integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
    dependencies:
      get-value "^2.0.6"
      has-values "^1.0.0"
      isobject "^3.0.0"
  
  has-values@^0.1.4:
    version "0.1.4"
    resolved "https://registry.yarnpkg.com/has-values/-/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
    integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=
  
  has-values@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/has-values/-/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
    integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
    dependencies:
      is-number "^3.0.0"
      kind-of "^4.0.0"
  
  has@^1.0.3:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
    integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
    dependencies:
      function-bind "^1.1.1"
  
  hermes-engine@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/hermes-engine/-/hermes-engine-0.2.1.tgz#25c0f1ff852512a92cb5c5cc47cf967e1e722ea2"
    integrity sha512-eNHUQHuadDMJARpaqvlCZoK/Nitpj6oywq3vQ3wCwEsww5morX34mW5PmKWQTO7aU0ck0hgulxR+EVDlXygGxQ==
  
  hoist-non-react-statics@^2.3.1:
    version "2.5.5"
    resolved "https://registry.yarnpkg.com/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
    integrity sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw==
  
  hoist-non-react-statics@^3.0.1, hoist-non-react-statics@^3.1.0, hoist-non-react-statics@^3.3.0:
    version "3.3.1"
    resolved "https://registry.yarnpkg.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz#101685d3aff3b23ea213163f6e8e12f4f111e19f"
    integrity sha512-wbg3bpgA/ZqWrZuMOeJi8+SKMhr7X9TesL/rXMjTzh0p0JUBo3II8DHboYbuIXWRlttrUFxwcu/5kygrCw8fJw==
    dependencies:
      react-is "^16.7.0"
  
  hosted-git-info@^2.1.4:
    version "2.8.5"
    resolved "https://registry.yarnpkg.com/hosted-git-info/-/hosted-git-info-2.8.5.tgz#759cfcf2c4d156ade59b0b2dfabddc42a6b9c70c"
    integrity sha512-kssjab8CvdXfcXMXVcvsXum4Hwdq9XGtRD3TteMEvEbq0LXyiNQr6AprqKqfeaDXze7SxWvRxdpwE6ku7ikLkg==
  
  http-errors@~1.7.2:
    version "1.7.3"
    resolved "https://registry.yarnpkg.com/http-errors/-/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
    integrity sha512-ZTTX0MWrsQ2ZAhA1cejAwDLycFsd7I7nVtnkT3Ol0aqodaKW+0CTZDQ1uBv5whptCnc8e8HeRRJxRs0kmm/Qfw==
    dependencies:
      depd "~1.1.2"
      inherits "2.0.4"
      setprototypeof "1.1.1"
      statuses ">= 1.5.0 < 2"
      toidentifier "1.0.0"
  
  https-proxy-agent@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/https-proxy-agent/-/https-proxy-agent-4.0.0.tgz#702b71fb5520a132a66de1f67541d9e62154d82b"
    integrity sha512-zoDhWrkR3of1l9QAL8/scJZyLu8j/gBkcwcaQOZh7Gyh/+uJQzGVETdgT30akuwkpL8HTRfssqI3BZuV18teDg==
    dependencies:
      agent-base "5"
      debug "4"
  
  husky@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/husky/-/husky-3.1.0.tgz#5faad520ab860582ed94f0c1a77f0f04c90b57c0"
    integrity sha512-FJkPoHHB+6s4a+jwPqBudBDvYZsoQW5/HBuMSehC8qDiCe50kpcxeqFoDSlow+9I6wg47YxBoT3WxaURlrDIIQ==
    dependencies:
      chalk "^2.4.2"
      ci-info "^2.0.0"
      cosmiconfig "^5.2.1"
      execa "^1.0.0"
      get-stdin "^7.0.0"
      opencollective-postinstall "^2.0.2"
      pkg-dir "^4.2.0"
      please-upgrade-node "^3.2.0"
      read-pkg "^5.2.0"
      run-node "^1.0.0"
      slash "^3.0.0"
  
  hyphenate-style-name@^1.0.2:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/hyphenate-style-name/-/hyphenate-style-name-1.0.3.tgz#097bb7fa0b8f1a9cf0bd5c734cf95899981a9b48"
    integrity sha512-EcuixamT82oplpoJ2XU4pDtKGWQ7b00CD9f1ug9IaQ3p1bkHMiKCZ9ut9QDI6qsa6cpUuB+A/I+zLtdNK4n2DQ==
  
  i18n-js@^3.5.1:
    version "3.5.1"
    resolved "https://registry.yarnpkg.com/i18n-js/-/i18n-js-3.5.1.tgz#9787450894059bec1af791123231e59898eb97c1"
    integrity sha512-nJgbE5Vj9qzOQfjdVd/uoMoO8ppVaB/3LB6KOmMfD8IQ1vNNh307iHyQLK8ZnLYWkAszfPvVpYmUt1Le/RuHMQ==
  
  iconv-lite@^0.4.17, iconv-lite@^0.4.24, iconv-lite@~0.4.13:
    version "0.4.24"
    resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
    integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  ignore@^4.0.6:
    version "4.0.6"
    resolved "https://registry.yarnpkg.com/ignore/-/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
    integrity sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==
  
  ignore@^5.0.5, ignore@^5.1.4:
    version "5.1.4"
    resolved "https://registry.yarnpkg.com/ignore/-/ignore-5.1.4.tgz#84b7b3dbe64552b6ef0eca99f6743dbec6d97adf"
    integrity sha512-MzbUSahkTW1u7JpKKjY7LCARd1fU5W2rLdxlM4kdkayuCwZImjkpluF9CM1aLewYJguPDqewLam18Y6AU69A8A==
  
  image-size@^0.6.0:
    version "0.6.3"
    resolved "https://registry.yarnpkg.com/image-size/-/image-size-0.6.3.tgz#e7e5c65bb534bd7cdcedd6cb5166272a85f75fb2"
    integrity sha512-47xSUiQioGaB96nqtp5/q55m0aBQSQdyIloMOc/x+QVTDZLNmXE892IIDrJ0hM1A5vcNUDD5tDffkSP5lCaIIA==
  
  immediate@^3.2.2:
    version "3.2.3"
    resolved "https://registry.yarnpkg.com/immediate/-/immediate-3.2.3.tgz#d140fa8f614659bd6541233097ddaac25cdd991c"
    integrity sha1-0UD6j2FGWb1lQSMwl92qwlzdmRw=
  
  import-fresh@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/import-fresh/-/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
    integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
    dependencies:
      caller-path "^2.0.0"
      resolve-from "^3.0.0"
  
  import-fresh@^3.0.0:
    version "3.2.1"
    resolved "https://registry.yarnpkg.com/import-fresh/-/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
    integrity sha512-6e1q1cnWP2RXD9/keSkxHScg508CdXqXWgWBaETNhyuBFz+kUZlKboh+ISK+bU++DmbHimVBrOz/zzPe0sZ3sQ==
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
    integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
    integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2, inherits@2.0.4, inherits@^2.0.3, inherits@~2.0.3:
    version "2.0.4"
    resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  inline-style-prefixer@^5.0.3:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/inline-style-prefixer/-/inline-style-prefixer-5.1.0.tgz#cb63195f9456dcda25cf59743e45c4d9815b0811"
    integrity sha512-giteQHPMrApQOSjNSjteO5ZGSGMRf9gas14fRy2lg2buSc1nRnj6o6xuNds5cMTKrkncyrTu3gJn/yflFMVdmg==
    dependencies:
      css-in-js-utils "^2.0.0"
  
  inquirer@^3.0.6:
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-3.3.0.tgz#9dd2f2ad765dcab1ff0443b491442a20ba227dc9"
    integrity sha512-h+xtnyk4EwKvFWHrUYsWErEVR+igKtLdchu+o0Z1RL7VU/jVMFbYir2bp6bAj8efFNxWqHX0dIss6fJQ+/+qeQ==
    dependencies:
      ansi-escapes "^3.0.0"
      chalk "^2.0.0"
      cli-cursor "^2.1.0"
      cli-width "^2.0.0"
      external-editor "^2.0.4"
      figures "^2.0.0"
      lodash "^4.3.0"
      mute-stream "0.0.7"
      run-async "^2.2.0"
      rx-lite "^4.0.8"
      rx-lite-aggregates "^4.0.8"
      string-width "^2.1.0"
      strip-ansi "^4.0.0"
      through "^2.3.6"
  
  inquirer@^6.2.0:
    version "6.5.2"
    resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-6.5.2.tgz#ad50942375d036d327ff528c08bd5fab089928ca"
    integrity sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ==
    dependencies:
      ansi-escapes "^3.2.0"
      chalk "^2.4.2"
      cli-cursor "^2.1.0"
      cli-width "^2.0.0"
      external-editor "^3.0.3"
      figures "^2.0.0"
      lodash "^4.17.12"
      mute-stream "0.0.7"
      run-async "^2.2.0"
      rxjs "^6.4.0"
      string-width "^2.1.0"
      strip-ansi "^5.1.0"
      through "^2.3.6"
  
  inquirer@^7.0.0:
    version "7.0.2"
    resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-7.0.2.tgz#b39b205b95c9424839a1fd991d60426cf9bbc0e9"
    integrity sha512-cZGvHaHwcR9E3xK9EGO5pHKELU+yaeJO7l2qGKIbqk4bCuDuAn15LCoUTS2nSkfv9JybFlnAGrOcVpCDZZOLhw==
    dependencies:
      ansi-escapes "^4.2.1"
      chalk "^2.4.2"
      cli-cursor "^3.1.0"
      cli-width "^2.0.0"
      external-editor "^3.0.3"
      figures "^3.0.0"
      lodash "^4.17.15"
      mute-stream "0.0.8"
      run-async "^2.2.0"
      rxjs "^6.5.3"
      string-width "^4.1.0"
      strip-ansi "^5.1.0"
      through "^2.3.6"
  
  invariant@^2.2.2, invariant@^2.2.4:
    version "2.2.4"
    resolved "https://registry.yarnpkg.com/invariant/-/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
    integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
    dependencies:
      loose-envify "^1.0.0"
  
  invert-kv@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"
    integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=
  
  invert-kv@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/invert-kv/-/invert-kv-2.0.0.tgz#7393f5afa59ec9ff5f67a27620d11c226e3eec02"
    integrity sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA==
  
  is-accessor-descriptor@^0.1.6:
    version "0.1.6"
    resolved "https://registry.yarnpkg.com/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
    integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
    dependencies:
      kind-of "^3.0.2"
  
  is-accessor-descriptor@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
    integrity sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==
    dependencies:
      kind-of "^6.0.0"
  
  is-arrayish@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
    integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=
  
  is-arrayish@^0.3.1:
    version "0.3.2"
    resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
    integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==
  
  is-buffer@^1.0.2, is-buffer@^1.1.5:
    version "1.1.6"
    resolved "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
    integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==
  
  is-callable@^1.1.4, is-callable@^1.1.5:
    version "1.1.5"
    resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.1.5.tgz#f7e46b596890456db74e7f6e976cb3273d06faab"
    integrity sha512-ESKv5sMCJB2jnHTWZ3O5itG+O128Hsus4K4Qh1h2/cgn2vbgnLSVqfV46AeJA9D5EeeLa9w81KUXMtn34zhX+Q==
  
  is-ci@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/is-ci/-/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
    integrity sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==
    dependencies:
      ci-info "^2.0.0"
  
  is-data-descriptor@^0.1.4:
    version "0.1.4"
    resolved "https://registry.yarnpkg.com/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
    integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
    dependencies:
      kind-of "^3.0.2"
  
  is-data-descriptor@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
    integrity sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==
    dependencies:
      kind-of "^6.0.0"
  
  is-date-object@^1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/is-date-object/-/is-date-object-1.0.2.tgz#bda736f2cd8fd06d32844e7743bfa7494c3bfd7e"
    integrity sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g==
  
  is-descriptor@^0.1.0:
    version "0.1.6"
    resolved "https://registry.yarnpkg.com/is-descriptor/-/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
    integrity sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==
    dependencies:
      is-accessor-descriptor "^0.1.6"
      is-data-descriptor "^0.1.4"
      kind-of "^5.0.0"
  
  is-descriptor@^1.0.0, is-descriptor@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/is-descriptor/-/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
    integrity sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==
    dependencies:
      is-accessor-descriptor "^1.0.0"
      is-data-descriptor "^1.0.0"
      kind-of "^6.0.2"
  
  is-directory@^0.3.1:
    version "0.3.1"
    resolved "https://registry.yarnpkg.com/is-directory/-/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
    integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=
  
  is-extendable@^0.1.0, is-extendable@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
    integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=
  
  is-extendable@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/is-extendable/-/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
    integrity sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==
    dependencies:
      is-plain-object "^2.0.4"
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
    integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=
  
  is-fullwidth-code-point@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
    integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
    dependencies:
      number-is-nan "^1.0.0"
  
  is-fullwidth-code-point@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
    integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=
  
  is-fullwidth-code-point@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
    integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==
  
  is-glob@^4.0.0, is-glob@^4.0.1:
    version "4.0.1"
    resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
    integrity sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==
    dependencies:
      is-extglob "^2.1.1"
  
  is-number@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
    integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
    dependencies:
      kind-of "^3.0.2"
  
  is-obj@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/is-obj/-/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
    integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=
  
  is-plain-object@^2.0.1, is-plain-object@^2.0.3, is-plain-object@^2.0.4:
    version "2.0.4"
    resolved "https://registry.yarnpkg.com/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
    integrity sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==
    dependencies:
      isobject "^3.0.1"
  
  is-promise@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/is-promise/-/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
    integrity sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=
  
  is-regex@^1.0.5:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/is-regex/-/is-regex-1.0.5.tgz#39d589a358bf18967f726967120b8fc1aed74eae"
    integrity sha512-vlKW17SNq44owv5AQR3Cq0bQPEb8+kF3UKZ2fiZNOWtztYE5i0CzCZxFDwO58qAOWtxdBRVO/V5Qin1wjCqFYQ==
    dependencies:
      has "^1.0.3"
  
  is-stream@^1.0.1, is-stream@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
    integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=
  
  is-stream@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-2.0.0.tgz#bde9c32680d6fae04129d6ac9d921ce7815f78e3"
    integrity sha512-XCoy+WlUr7d1+Z8GgSuXmpuUFC9fOhRXglJMx+dwLKTkL44Cjd4W1Z5P+BQZpr+cR93aGP4S/s7Ftw6Nd/kiEw==
  
  is-string@^1.0.5:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/is-string/-/is-string-1.0.5.tgz#40493ed198ef3ff477b8c7f92f644ec82a5cd3a6"
    integrity sha512-buY6VNRjhQMiF1qWDouloZlQbRhDPCebwxSjxMjxgemYT46YMd2NR0/H+fBhEfWX4A/w9TBJ+ol+okqJKFE6vQ==
  
  is-symbol@^1.0.2:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/is-symbol/-/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937"
    integrity sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ==
    dependencies:
      has-symbols "^1.0.1"
  
  is-typedarray@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
    integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=
  
  is-windows@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/is-windows/-/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
    integrity sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==
  
  is-wsl@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/is-wsl/-/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
    integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=
  
  isarray@0.0.1:
    version "0.0.1"
    resolved "https://registry.yarnpkg.com/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
    integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=
  
  isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
    integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
    integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=
  
  isobject@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
    integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
    dependencies:
      isarray "1.0.0"
  
  isobject@^3.0.0, isobject@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
    integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=
  
  isomorphic-fetch@^2.1.1:
    version "2.2.1"
    resolved "https://registry.yarnpkg.com/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
    integrity sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=
    dependencies:
      node-fetch "^1.0.1"
      whatwg-fetch ">=0.10.0"
  
  jest-docblock@^21.0.0:
    version "21.2.0"
    resolved "https://registry.yarnpkg.com/jest-docblock/-/jest-docblock-21.2.0.tgz#51529c3b30d5fd159da60c27ceedc195faf8d414"
    integrity sha512-5IZ7sY9dBAYSV+YjQ0Ovb540Ku7AO9Z5o2Cg789xj167iQuZ2cG+z0f3Uct6WeYLbU6aQiM2pCs7sZ+4dotydw==
  
  jest-get-type@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-get-type/-/jest-get-type-24.9.0.tgz#1684a0c8a50f2e4901b6644ae861f579eed2ef0e"
    integrity sha512-lUseMzAley4LhIcpSP9Jf+fTrQ4a1yHQwLNeeVa2cEmbCGeoZAtYPOIv8JaxLD/sUpKxetKGP+gsHl8f8TSj8Q==
  
  jest-haste-map@^24.7.1:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-haste-map/-/jest-haste-map-24.9.0.tgz#b38a5d64274934e21fa417ae9a9fbeb77ceaac7d"
    integrity sha512-kfVFmsuWui2Sj1Rp1AJ4D9HqJwE4uwTlS/vO+eRUaMmd54BFpli2XhMQnPC2k4cHFVbB2Q2C+jtI1AGLgEnCjQ==
    dependencies:
      "@jest/types" "^24.9.0"
      anymatch "^2.0.0"
      fb-watchman "^2.0.0"
      graceful-fs "^4.1.15"
      invariant "^2.2.4"
      jest-serializer "^24.9.0"
      jest-util "^24.9.0"
      jest-worker "^24.9.0"
      micromatch "^3.1.10"
      sane "^4.0.3"
      walker "^1.0.7"
    optionalDependencies:
      fsevents "^1.2.7"
  
  jest-message-util@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-message-util/-/jest-message-util-24.9.0.tgz#527f54a1e380f5e202a8d1149b0ec872f43119e3"
    integrity sha512-oCj8FiZ3U0hTP4aSui87P4L4jC37BtQwUMqk+zk/b11FR19BJDeZsZAvIHutWnmtw7r85UmR3CEWZ0HWU2mAlw==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      "@types/stack-utils" "^1.0.1"
      chalk "^2.0.1"
      micromatch "^3.1.10"
      slash "^2.0.0"
      stack-utils "^1.0.1"
  
  jest-mock@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-mock/-/jest-mock-24.9.0.tgz#c22835541ee379b908673ad51087a2185c13f1c6"
    integrity sha512-3BEYN5WbSq9wd+SyLDES7AHnjH9A/ROBwmz7l2y+ol+NtSFO8DYiEBzoO1CeFc9a8DYy10EO4dDFVv/wN3zl1w==
    dependencies:
      "@jest/types" "^24.9.0"
  
  jest-serializer@^24.4.0, jest-serializer@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-serializer/-/jest-serializer-24.9.0.tgz#e6d7d7ef96d31e8b9079a714754c5d5c58288e73"
    integrity sha512-DxYipDr8OvfrKH3Kel6NdED3OXxjvxXZ1uIY2I9OFbGg+vUkkg7AGvi65qbhbWNPvDckXmzMPbK3u3HaDO49bQ==
  
  jest-util@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-util/-/jest-util-24.9.0.tgz#7396814e48536d2e85a37de3e4c431d7cb140162"
    integrity sha512-x+cZU8VRmOJxbA1K5oDBdxQmdq0OIdADarLxk0Mq+3XS4jgvhG/oKGWcIDCtPG0HgjxOYvF+ilPJQsAyXfbNOg==
    dependencies:
      "@jest/console" "^24.9.0"
      "@jest/fake-timers" "^24.9.0"
      "@jest/source-map" "^24.9.0"
      "@jest/test-result" "^24.9.0"
      "@jest/types" "^24.9.0"
      callsites "^3.0.0"
      chalk "^2.0.1"
      graceful-fs "^4.1.15"
      is-ci "^2.0.0"
      mkdirp "^0.5.1"
      slash "^2.0.0"
      source-map "^0.6.0"
  
  jest-validate@^24.7.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-validate/-/jest-validate-24.9.0.tgz#0775c55360d173cd854e40180756d4ff52def8ab"
    integrity sha512-HPIt6C5ACwiqSiwi+OfSSHbK8sG7akG8eATl+IPKaeIjtPOeBUd/g3J7DghugzxrGjI93qS/+RPKe1H6PqvhRQ==
    dependencies:
      "@jest/types" "^24.9.0"
      camelcase "^5.3.1"
      chalk "^2.0.1"
      jest-get-type "^24.9.0"
      leven "^3.1.0"
      pretty-format "^24.9.0"
  
  jest-worker@^24.6.0, jest-worker@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/jest-worker/-/jest-worker-24.9.0.tgz#5dbfdb5b2d322e98567898238a9697bcce67b3e5"
    integrity sha512-51PE4haMSXcHohnSMdM42anbvZANYTqMrr52tVKPqqsPJMzoP6FYYDVqahX/HrAoKEKz3uUPzSvKs9A3qR4iVw==
    dependencies:
      merge-stream "^2.0.0"
      supports-color "^6.1.0"
  
  jetifier@^1.6.2:
    version "1.6.5"
    resolved "https://registry.yarnpkg.com/jetifier/-/jetifier-1.6.5.tgz#ea87324a4230bef20a9651178ecab978ee54a8cb"
    integrity sha512-T7yzBSu9PR+DqjYt+I0KVO1XTb1QhAfHnXV5Nd3xpbXM6Xg4e3vP60Q4qkNU8Fh6PHC2PivPUNN3rY7G2MxcDQ==
  
  js-levenshtein@^1.1.3:
    version "1.1.6"
    resolved "https://registry.yarnpkg.com/js-levenshtein/-/js-levenshtein-1.1.6.tgz#c6cee58eb3550372df8deb85fad5ce66ce01d59d"
    integrity sha512-X2BB11YZtrRqY4EnQcLX5Rh373zbK4alC1FW7D7MBhL2gtcC17cTnr6DmfHZeS0s2rTHjUTMMHfG7gO8SSdw+g==
  
  "js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-yaml@^3.13.1:
    version "3.13.1"
    resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
    integrity sha512-YfbcO7jXDdyj0DGxYVSlSeQNHbD7XPWvrVWeVUujrQEoZzWJIRrCPoyk6kL6IAjAG2IolMK4T0hNUe0HOUs5Jw==
    dependencies:
      argparse "^1.0.7"
      esprima "^4.0.0"
  
  jsc-android@^245459.0.0:
    version "245459.0.0"
    resolved "https://registry.yarnpkg.com/jsc-android/-/jsc-android-245459.0.0.tgz#e584258dd0b04c9159a27fb104cd5d491fd202c9"
    integrity sha512-wkjURqwaB1daNkDi2OYYbsLnIdC/lUM2nPXQKRs5pqEU9chDg435bjvo+LSaHotDENygHQDHe+ntUkkw2gwMtg==
  
  jsesc@^2.5.1:
    version "2.5.2"
    resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
    integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==
  
  jsesc@~0.5.0:
    version "0.5.0"
    resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
    integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=
  
  json-parse-better-errors@^1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
    integrity sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
    integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=
  
  json-stable-stringify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
    integrity sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=
    dependencies:
      jsonify "~0.0.0"
  
  json5@^0.5.1:
    version "0.5.1"
    resolved "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
    integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=
  
  json5@^2.1.0:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/json5/-/json5-2.1.1.tgz#81b6cb04e9ba496f1c7005d07b4368a2638f90b6"
    integrity sha512-l+3HXD0GEI3huGq1njuqtzYK8OYJyXMkOLtQ53pjWh89tvWS2h6l+1zMkYWqlb57+SiQodKZyvMEFb2X+KrFhQ==
    dependencies:
      minimist "^1.2.0"
  
  json5@^2.1.2:
    version "2.1.3"
    resolved "https://registry.yarnpkg.com/json5/-/json5-2.1.3.tgz#c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43"
    integrity sha512-KXPvOm8K9IJKFM0bmdn8QXh7udDh1g/giieX0NLCaMnb4hEiVFqnop2ImTXCc5e0/oHz3LTqmHGtExn5hfMkOA==
    dependencies:
      minimist "^1.2.5"
  
  jsonfile@^2.1.0:
    version "2.4.0"
    resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
    integrity sha1-NzaitCi4e72gzIO1P6PWM6NcKug=
    optionalDependencies:
      graceful-fs "^4.1.6"
  
  jsonfile@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
    integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
    optionalDependencies:
      graceful-fs "^4.1.6"
  
  jsonify@~0.0.0:
    version "0.0.0"
    resolved "https://registry.yarnpkg.com/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"
    integrity sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=
  
  jsx-ast-utils@^2.2.1, jsx-ast-utils@^2.2.3:
    version "2.2.3"
    resolved "https://registry.yarnpkg.com/jsx-ast-utils/-/jsx-ast-utils-2.2.3.tgz#8a9364e402448a3ce7f14d357738310d9248054f"
    integrity sha512-EdIHFMm+1BPynpKOpdPqiOsvnIrInRGJD7bzPZdPkjitQEqpdpUuFpq4T0npZFKTiB3RhWFdGN+oqOJIdhDhQA==
    dependencies:
      array-includes "^3.0.3"
      object.assign "^4.1.0"
  
  kind-of@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-1.1.0.tgz#140a3d2d41a36d2efcfa9377b62c24f8495a5c44"
    integrity sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ=
  
  kind-of@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-2.0.1.tgz#018ec7a4ce7e3a86cb9141be519d24c8faa981b5"
    integrity sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU=
    dependencies:
      is-buffer "^1.0.2"
  
  kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
    version "3.2.2"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
    integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
    dependencies:
      is-buffer "^1.1.5"
  
  kind-of@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
    integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
    dependencies:
      is-buffer "^1.1.5"
  
  kind-of@^5.0.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
    integrity sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==
  
  kind-of@^6.0.0, kind-of@^6.0.2:
    version "6.0.2"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-6.0.2.tgz#01146b36a6218e64e58f3a8d66de5d7fc6f6d051"
    integrity sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA==
  
  klaw@^1.0.0:
    version "1.3.1"
    resolved "https://registry.yarnpkg.com/klaw/-/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
    integrity sha1-QIhDO0azsbolnXh4XY6W9zugJDk=
    optionalDependencies:
      graceful-fs "^4.1.9"
  
  lazy-cache@^0.2.3:
    version "0.2.7"
    resolved "https://registry.yarnpkg.com/lazy-cache/-/lazy-cache-0.2.7.tgz#7feddf2dcb6edb77d11ef1d117ab5ffdf0ab1b65"
    integrity sha1-f+3fLctu23fRHvHRF6tf/fCrG2U=
  
  lazy-cache@^1.0.3:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
    integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=
  
  lcid@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
    integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
    dependencies:
      invert-kv "^1.0.0"
  
  lcid@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/lcid/-/lcid-2.0.0.tgz#6ef5d2df60e52f82eb228a4c373e8d1f397253cf"
    integrity sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA==
    dependencies:
      invert-kv "^2.0.0"
  
  leven@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/leven/-/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
    integrity sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==
  
  levn@^0.3.0, levn@~0.3.0:
    version "0.3.0"
    resolved "https://registry.yarnpkg.com/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
    integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
    dependencies:
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
  
  lines-and-columns@^1.1.6:
    version "1.1.6"
    resolved "https://registry.yarnpkg.com/lines-and-columns/-/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
    integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=
  
  load-json-file@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/load-json-file/-/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
    integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
    dependencies:
      graceful-fs "^4.1.2"
      parse-json "^2.2.0"
      pify "^2.0.0"
      strip-bom "^3.0.0"
  
  locate-path@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
    integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
    dependencies:
      p-locate "^2.0.0"
      path-exists "^3.0.0"
  
  locate-path@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
    integrity sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==
    dependencies:
      p-locate "^3.0.0"
      path-exists "^3.0.0"
  
  locate-path@^5.0.0:
    version "5.0.0"
    resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
    integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
    dependencies:
      p-locate "^4.1.0"
  
  lodash-es@^4.2.1:
    version "4.17.15"
    resolved "https://registry.yarnpkg.com/lodash-es/-/lodash-es-4.17.15.tgz#21bd96839354412f23d7a10340e5eac6ee455d78"
    integrity sha512-rlrc3yU3+JNOpZ9zj5pQtxnx2THmvRykwL4Xlxoa8I9lHBlVbbyPhgyPMioxVZ4NqyxaVVtaJnzsyOidQIhyyQ==
  
  lodash.throttle@^4.1.1:
    version "4.1.1"
    resolved "https://registry.yarnpkg.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
    integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=
  
  lodash.toarray@^4.4.0:
    version "4.4.0"
    resolved "https://registry.yarnpkg.com/lodash.toarray/-/lodash.toarray-4.4.0.tgz#24c4bfcd6b2fba38bfd0594db1179d8e9b656561"
    integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=
  
  lodash.unescape@4.0.1:
    version "4.0.1"
    resolved "https://registry.yarnpkg.com/lodash.unescape/-/lodash.unescape-4.0.1.tgz#bf2249886ce514cda112fae9218cdc065211fc9c"
    integrity sha1-vyJJiGzlFM2hEvrpIYzcBlIR/Jw=
  
  lodash@^4, lodash@^4.0.0, lodash@^4.17.10, lodash@^4.17.12, lodash@^4.17.13, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.4, lodash@^4.17.5, lodash@^4.2.1, lodash@^4.3.0, lodash@^4.5.0, lodash@^4.6.0:
    version "4.17.15"
    resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
    integrity sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A==
  
  log-symbols@^2.2.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/log-symbols/-/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
    integrity sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==
    dependencies:
      chalk "^2.0.1"
  
  logkitty@^0.6.0:
    version "0.6.1"
    resolved "https://registry.yarnpkg.com/logkitty/-/logkitty-0.6.1.tgz#fe29209669d261539cbd6bb998a136fc92a1a05c"
    integrity sha512-cHuXN8qUZuzX/7kB6VyS7kB4xyD24e8gyHXIFNhIv+fjW3P+jEXNUhj0o/7qWJtv7UZpbnPgUqzu/AZQ8RAqxQ==
    dependencies:
      ansi-fragments "^0.2.1"
      dayjs "^1.8.15"
      yargs "^12.0.5"
  
  loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
    integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
    dependencies:
      js-tokens "^3.0.0 || ^4.0.0"
  
  lru-cache@^4.0.1:
    version "4.1.5"
    resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
    integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
    dependencies:
      pseudomap "^1.0.2"
      yallist "^2.1.2"
  
  make-dir@^2.0.0, make-dir@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/make-dir/-/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
    integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
    dependencies:
      pify "^4.0.1"
      semver "^5.6.0"
  
  makeerror@1.0.x:
    version "1.0.11"
    resolved "https://registry.yarnpkg.com/makeerror/-/makeerror-1.0.11.tgz#e01a5c9109f2af79660e4e8b9587790184f5a96c"
    integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
    dependencies:
      tmpl "1.0.x"
  
  map-age-cleaner@^0.1.1:
    version "0.1.3"
    resolved "https://registry.yarnpkg.com/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz#7d583a7306434c055fe474b0f45078e6e1b4b92a"
    integrity sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==
    dependencies:
      p-defer "^1.0.0"
  
  map-cache@^0.2.2:
    version "0.2.2"
    resolved "https://registry.yarnpkg.com/map-cache/-/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
    integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=
  
  map-visit@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/map-visit/-/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
    integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
    dependencies:
      object-visit "^1.0.0"
  
  md5-file@^3.2.3:
    version "3.2.3"
    resolved "https://registry.yarnpkg.com/md5-file/-/md5-file-3.2.3.tgz#f9bceb941eca2214a4c0727f5e700314e770f06f"
    integrity sha512-3Tkp1piAHaworfcCgH0jKbTvj1jWWFgbvh2cXaNCgHwyTCBxxvD1Y04rmfpvdPm1P4oXMOpm6+2H7sr7v9v8Fw==
    dependencies:
      buffer-alloc "^1.1.0"
  
  mdn-data@2.0.4:
    version "2.0.4"
    resolved "https://registry.yarnpkg.com/mdn-data/-/mdn-data-2.0.4.tgz#699b3c38ac6f1d728091a64650b65d388502fd5b"
    integrity sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA==
  
  mdn-data@2.0.6:
    version "2.0.6"
    resolved "https://registry.yarnpkg.com/mdn-data/-/mdn-data-2.0.6.tgz#852dc60fcaa5daa2e8cf6c9189c440ed3e042978"
    integrity sha512-rQvjv71olwNHgiTbfPZFkJtjNMciWgswYeciZhtvWLO8bmX3TnhyA62I6sTWOyZssWHJJjY6/KiWwqQsWWsqOA==
  
  mem@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/mem/-/mem-1.1.0.tgz#5edd52b485ca1d900fe64895505399a0dfa45f76"
    integrity sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y=
    dependencies:
      mimic-fn "^1.0.0"
  
  mem@^4.0.0:
    version "4.3.0"
    resolved "https://registry.yarnpkg.com/mem/-/mem-4.3.0.tgz#461af497bc4ae09608cdb2e60eefb69bff744178"
    integrity sha512-qX2bG48pTqYRVmDB37rn/6PT7LcR8T7oAX3bf99u1Tt1nzxYfxkgqDwUwolPlXweM0XzBOBFzSx4kfp7KP1s/w==
    dependencies:
      map-age-cleaner "^0.1.1"
      mimic-fn "^2.0.0"
      p-is-promise "^2.0.0"
  
  merge-deep@^3.0.2:
    version "3.0.2"
    resolved "https://registry.yarnpkg.com/merge-deep/-/merge-deep-3.0.2.tgz#f39fa100a4f1bd34ff29f7d2bf4508fbb8d83ad2"
    integrity sha512-T7qC8kg4Zoti1cFd8Cr0M+qaZfOwjlPDEdZIIPPB2JZctjaPM4fX+i7HOId69tAti2fvO6X5ldfYUONDODsrkA==
    dependencies:
      arr-union "^3.1.0"
      clone-deep "^0.2.4"
      kind-of "^3.0.2"
  
  merge-stream@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/merge-stream/-/merge-stream-1.0.1.tgz#4041202d508a342ba00174008df0c251b8c135e1"
    integrity sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=
    dependencies:
      readable-stream "^2.0.1"
  
  merge-stream@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
    integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==
  
  metro-babel-register@0.56.3, metro-babel-register@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-babel-register/-/metro-babel-register-0.56.3.tgz#d0cfb38adf45cb35965649ede794f2308562e20f"
    integrity sha512-ILCRtNFdW6vzqmLAG2MYWdTSE1vCAZqDKNggiNhlfViuoxmWAIL0vOqixl1CHZF5z4t55+fk46A0jSN7UgPyVw==
    dependencies:
      "@babel/core" "^7.0.0"
      "@babel/plugin-proposal-class-properties" "^7.0.0"
      "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
      "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
      "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
      "@babel/plugin-proposal-optional-chaining" "^7.0.0"
      "@babel/plugin-transform-async-to-generator" "^7.0.0"
      "@babel/plugin-transform-flow-strip-types" "^7.0.0"
      "@babel/plugin-transform-modules-commonjs" "^7.0.0"
      "@babel/register" "^7.0.0"
      core-js "^2.2.2"
      escape-string-regexp "^1.0.5"
  
  metro-babel-transformer@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-babel-transformer/-/metro-babel-transformer-0.56.3.tgz#6559c3a8565238a704a181353cef59fdb974e6db"
    integrity sha512-N5/ftb3rBkt6uKlgYAv+lwtzYc4dK0tBpfZ8pjec3kcypGuGTuf4LTHEh65EuzySreLngYI0bQzoFSn3G3DYsw==
    dependencies:
      "@babel/core" "^7.0.0"
      metro-source-map "0.56.3"
  
  metro-cache@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-cache/-/metro-cache-0.56.3.tgz#1b0759bc45291cc3ffc77736c09dcfbd322edb8b"
    integrity sha512-SsryVe/TVkt2IkEGnYhB3gQlg9iMlu8WJikQHcCEjMfPEnSIzmeymrX73fwQNPnTnN7F3E0HVjH6Wvq6fh0mcA==
    dependencies:
      jest-serializer "^24.4.0"
      metro-core "0.56.3"
      mkdirp "^0.5.1"
      rimraf "^2.5.4"
  
  metro-config@0.56.3, metro-config@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-config/-/metro-config-0.56.3.tgz#b16e600817c58c768946f24b039d2a1ba6a67651"
    integrity sha512-C3ZLA5y5gW5auDSQN5dsCTduJg7LXEiX/tLAADOkgXWVImr5P74x9Wt8y1MMWrKx6p+4p5RMDyEwWDMXJt/DwA==
    dependencies:
      cosmiconfig "^5.0.5"
      jest-validate "^24.7.0"
      metro "0.56.3"
      metro-cache "0.56.3"
      metro-core "0.56.3"
      pretty-format "^24.7.0"
  
  metro-core@0.56.3, metro-core@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-core/-/metro-core-0.56.3.tgz#34bb3a92621fd9b1ed3e6a01c6a4324fbb1201d9"
    integrity sha512-OAaHP3mBdlACMZRwDJzZzYC0o2S3qfb4BBK75L8H4Ds+y3QUSrjsDEpHACcpaMTOds8rBvjzn+jjB5tqNoHfBA==
    dependencies:
      jest-haste-map "^24.7.1"
      lodash.throttle "^4.1.1"
      metro-resolver "0.56.3"
      wordwrap "^1.0.0"
  
  metro-inspector-proxy@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-inspector-proxy/-/metro-inspector-proxy-0.56.3.tgz#48046f9e3f7153be2409e0bee9252dede932ac39"
    integrity sha512-7WtHinw+VJcunQ3q8El1MqqzYSRvXEjW5QE13VYwcLtnay3pvcqACeiQmGbWI0IqxB1+QH8tf3nkA7z7pQ7Vpw==
    dependencies:
      connect "^3.6.5"
      debug "^2.2.0"
      rxjs "^5.4.3"
      ws "^1.1.5"
      yargs "^9.0.0"
  
  metro-minify-uglify@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-minify-uglify/-/metro-minify-uglify-0.56.3.tgz#763b26895f79d0589d3391dc94083d348cf9c2be"
    integrity sha512-b9ljyeUpkJWVlFy8M/i4aNbvEBI0zN9vJh1jfU7yx+k9dX7FulLnpGmAQxxQdEszcM//sJrsKNS1oLYBxr0NMQ==
    dependencies:
      uglify-es "^3.1.9"
  
  metro-react-native-babel-preset@0.56.3, metro-react-native-babel-preset@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.56.3.tgz#5a1097c2f94e8ee0797a8ba2ab8f86d096f4c093"
    integrity sha512-tGPzX2ZwI8vQ8SiNVBPUIgKqmaRNVB6rtJtHCBQZAYRiMbxh0NHCUoFfKBej6U5qVgxiYYHyN8oB23evG4/Oow==
    dependencies:
      "@babel/plugin-proposal-class-properties" "^7.0.0"
      "@babel/plugin-proposal-export-default-from" "^7.0.0"
      "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
      "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
      "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
      "@babel/plugin-proposal-optional-chaining" "^7.0.0"
      "@babel/plugin-syntax-dynamic-import" "^7.0.0"
      "@babel/plugin-syntax-export-default-from" "^7.0.0"
      "@babel/plugin-syntax-flow" "^7.2.0"
      "@babel/plugin-transform-arrow-functions" "^7.0.0"
      "@babel/plugin-transform-block-scoping" "^7.0.0"
      "@babel/plugin-transform-classes" "^7.0.0"
      "@babel/plugin-transform-computed-properties" "^7.0.0"
      "@babel/plugin-transform-destructuring" "^7.0.0"
      "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
      "@babel/plugin-transform-flow-strip-types" "^7.0.0"
      "@babel/plugin-transform-for-of" "^7.0.0"
      "@babel/plugin-transform-function-name" "^7.0.0"
      "@babel/plugin-transform-literals" "^7.0.0"
      "@babel/plugin-transform-modules-commonjs" "^7.0.0"
      "@babel/plugin-transform-object-assign" "^7.0.0"
      "@babel/plugin-transform-parameters" "^7.0.0"
      "@babel/plugin-transform-react-display-name" "^7.0.0"
      "@babel/plugin-transform-react-jsx" "^7.0.0"
      "@babel/plugin-transform-react-jsx-source" "^7.0.0"
      "@babel/plugin-transform-regenerator" "^7.0.0"
      "@babel/plugin-transform-runtime" "^7.0.0"
      "@babel/plugin-transform-shorthand-properties" "^7.0.0"
      "@babel/plugin-transform-spread" "^7.0.0"
      "@babel/plugin-transform-sticky-regex" "^7.0.0"
      "@babel/plugin-transform-template-literals" "^7.0.0"
      "@babel/plugin-transform-typescript" "^7.0.0"
      "@babel/plugin-transform-unicode-regex" "^7.0.0"
      "@babel/template" "^7.0.0"
      react-refresh "^0.4.0"
  
  metro-react-native-babel-transformer@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-react-native-babel-transformer/-/metro-react-native-babel-transformer-0.56.3.tgz#e68205230be65c07290b932f7684226013dae310"
    integrity sha512-T87m4jDu0gIvJo8kWEvkodWFgQ8XBzJUESs1hUUTBSMIqTa31MdWfA1gs+MipadG7OsEJpcb9m83mGr8K70MWw==
    dependencies:
      "@babel/core" "^7.0.0"
      babel-preset-fbjs "^3.1.2"
      metro-babel-transformer "0.56.3"
      metro-react-native-babel-preset "0.56.3"
      metro-source-map "0.56.3"
  
  metro-resolver@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-resolver/-/metro-resolver-0.56.3.tgz#f18978b919a5ecc67028732609a564880715ef75"
    integrity sha512-VvMl4xUp0fy76WiP3YDtzMmrn6tN/jwxOBqlTy9MjN6R9sUXrGyO5thwn/uKQqp5vwBTuJev7nZL7OKzwludKA==
    dependencies:
      absolute-path "^0.0.0"
  
  metro-source-map@0.56.3, metro-source-map@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-source-map/-/metro-source-map-0.56.3.tgz#0cadc9f9eca9ece224a6fd28b9e4fa3a9834e24c"
    integrity sha512-CheqWbJZSM0zjcNBqELUiocwH3XArrOk6alhVuzJ2gV/WTMBQFwP0TtQssSMwjnouMHNEzY8RxErXKXBk/zJmQ==
    dependencies:
      "@babel/traverse" "^7.0.0"
      "@babel/types" "^7.0.0"
      invariant "^2.2.4"
      metro-symbolicate "0.56.3"
      ob1 "0.56.3"
      source-map "^0.5.6"
      vlq "^1.0.0"
  
  metro-symbolicate@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro-symbolicate/-/metro-symbolicate-0.56.3.tgz#20f9dc52fab3209903715716402692b3ac16831c"
    integrity sha512-fSQtjjy4eiJDThSl9eloxMElhrs+5PQB+DKKzmTFXT8e2GDga+pa1xTBFRUACMO8BXGuWmxR7SnGDw0wo5Ngrw==
    dependencies:
      invariant "^2.2.4"
      metro-source-map "0.56.3"
      source-map "^0.5.6"
      through2 "^2.0.1"
      vlq "^1.0.0"
  
  metro@0.56.3, metro@^0.56.0:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/metro/-/metro-0.56.3.tgz#3a38706bf6b1200421e871a4c53ddc2f359f65a9"
    integrity sha512-mxHpvBGWanZ46wAEZVLinNO5IYMcFbTdMZIRhC7r+rvoSK6r9iPj95AujBfzLXMAl36RI2O3D7yp5hOYif/gEQ==
    dependencies:
      "@babel/core" "^7.0.0"
      "@babel/generator" "^7.0.0"
      "@babel/parser" "^7.0.0"
      "@babel/plugin-external-helpers" "^7.0.0"
      "@babel/template" "^7.0.0"
      "@babel/traverse" "^7.0.0"
      "@babel/types" "^7.0.0"
      absolute-path "^0.0.0"
      async "^2.4.0"
      babel-preset-fbjs "^3.1.2"
      buffer-crc32 "^0.2.13"
      chalk "^2.4.1"
      concat-stream "^1.6.0"
      connect "^3.6.5"
      debug "^2.2.0"
      denodeify "^1.2.1"
      eventemitter3 "^3.0.0"
      fbjs "^1.0.0"
      fs-extra "^1.0.0"
      graceful-fs "^4.1.3"
      image-size "^0.6.0"
      invariant "^2.2.4"
      jest-haste-map "^24.7.1"
      jest-worker "^24.6.0"
      json-stable-stringify "^1.0.1"
      lodash.throttle "^4.1.1"
      merge-stream "^1.0.1"
      metro-babel-register "0.56.3"
      metro-babel-transformer "0.56.3"
      metro-cache "0.56.3"
      metro-config "0.56.3"
      metro-core "0.56.3"
      metro-inspector-proxy "0.56.3"
      metro-minify-uglify "0.56.3"
      metro-react-native-babel-preset "0.56.3"
      metro-resolver "0.56.3"
      metro-source-map "0.56.3"
      metro-symbolicate "0.56.3"
      mime-types "2.1.11"
      mkdirp "^0.5.1"
      node-fetch "^2.2.0"
      nullthrows "^1.1.0"
      resolve "^1.5.0"
      rimraf "^2.5.4"
      serialize-error "^2.1.0"
      source-map "^0.5.6"
      temp "0.8.3"
      throat "^4.1.0"
      wordwrap "^1.0.0"
      write-file-atomic "^1.2.0"
      ws "^1.1.5"
      xpipe "^1.0.5"
      yargs "^9.0.0"
  
  micromatch@^3.1.10, micromatch@^3.1.4:
    version "3.1.10"
    resolved "https://registry.yarnpkg.com/micromatch/-/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
    integrity sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==
    dependencies:
      arr-diff "^4.0.0"
      array-unique "^0.3.2"
      braces "^2.3.1"
      define-property "^2.0.2"
      extend-shallow "^3.0.2"
      extglob "^2.0.4"
      fragment-cache "^0.2.1"
      kind-of "^6.0.2"
      nanomatch "^1.2.9"
      object.pick "^1.3.0"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.2"
  
  mime-db@1.43.0, "mime-db@>= 1.43.0 < 2":
    version "1.43.0"
    resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.43.0.tgz#0a12e0502650e473d735535050e7c8f4eb4fae58"
    integrity sha512-+5dsGEEovYbT8UY9yD7eE4XTc4UwJ1jBYlgaQQF38ENsKR3wj/8q8RFZrF9WIZpB2V1ArTVFUva8sAul1NzRzQ==
  
  mime-db@~1.23.0:
    version "1.23.0"
    resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.23.0.tgz#a31b4070adaea27d732ea333740a64d0ec9a6659"
    integrity sha1-oxtAcK2uon1zLqMzdApk0OyaZlk=
  
  mime-types@2.1.11:
    version "2.1.11"
    resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.11.tgz#c259c471bda808a85d6cd193b430a5fae4473b3c"
    integrity sha1-wlnEcb2oCKhdbNGTtDCl+uRHOzw=
    dependencies:
      mime-db "~1.23.0"
  
  mime-types@~2.1.24:
    version "2.1.26"
    resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.26.tgz#9c921fc09b7e149a65dfdc0da4d20997200b0a06"
    integrity sha512-01paPWYgLrkqAyrlDorC1uDwl2p3qZT7yl806vW7DvDoxwXi46jsjFbg+WdwotBIk6/MbEhO/dh5aZ5sNj/dWQ==
    dependencies:
      mime-db "1.43.0"
  
  mime@1.6.0:
    version "1.6.0"
    resolved "https://registry.yarnpkg.com/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
    integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==
  
  mime@^2.4.1:
    version "2.4.4"
    resolved "https://registry.yarnpkg.com/mime/-/mime-2.4.4.tgz#bd7b91135fc6b01cde3e9bae33d659b63d8857e5"
    integrity sha512-LRxmNwziLPT828z+4YkNzloCFC2YM4wrB99k+AV5ZbEyfGNWfG8SO1FUXLmLDBSo89NrJZ4DIWeLjy1CHGhMGA==
  
  mimic-fn@^1.0.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
    integrity sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==
  
  mimic-fn@^2.0.0, mimic-fn@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
    integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==
  
  minimatch@^3.0.4:
    version "3.0.4"
    resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
    integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist@0.0.8:
    version "0.0.8"
    resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
    integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=
  
  minimist@^1.1.1, minimist@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"
    integrity sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=
  
  minimist@^1.2.5:
    version "1.2.5"
    resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
    integrity sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==
  
  mixin-deep@^1.2.0:
    version "1.3.2"
    resolved "https://registry.yarnpkg.com/mixin-deep/-/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
    integrity sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==
    dependencies:
      for-in "^1.0.2"
      is-extendable "^1.0.1"
  
  mixin-object@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/mixin-object/-/mixin-object-2.0.1.tgz#4fb949441dab182540f1fe035ba60e1947a5e57e"
    integrity sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4=
    dependencies:
      for-in "^0.1.3"
      is-extendable "^0.1.1"
  
  mkdirp@^0.5.1:
    version "0.5.1"
    resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
    integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
    dependencies:
      minimist "0.0.8"
  
  mkdirp@^0.5.4, mkdirp@~0.5.1:
    version "0.5.5"
    resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
    integrity sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==
    dependencies:
      minimist "^1.2.5"
  
  mkdirp@^1.0.3:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
    integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==
  
  moment@^2.24.0:
    version "2.24.0"
    resolved "https://registry.yarnpkg.com/moment/-/moment-2.24.0.tgz#0d055d53f5052aa653c9f6eb68bb5d12bf5c2b5b"
    integrity sha512-bV7f+6l2QigeBBZSM/6yTNq4P2fNpSWj/0e7jQcy87A8e7o2nAfP/34/2ky5Vw4B9S446EtIhodAzkFCcR4dQg==
  
  morgan@^1.9.0:
    version "1.9.1"
    resolved "https://registry.yarnpkg.com/morgan/-/morgan-1.9.1.tgz#0a8d16734a1d9afbc824b99df87e738e58e2da59"
    integrity sha512-HQStPIV4y3afTiCYVxirakhlCfGkI161c76kKFca7Fk1JusM//Qeo1ej2XaMniiNeaZklMVrh3vTtIzpzwbpmA==
    dependencies:
      basic-auth "~2.0.0"
      debug "2.6.9"
      depd "~1.1.2"
      on-finished "~2.3.0"
      on-headers "~1.0.1"
  
  mri@^1.1.4:
    version "1.1.4"
    resolved "https://registry.yarnpkg.com/mri/-/mri-1.1.4.tgz#7cb1dd1b9b40905f1fac053abe25b6720f44744a"
    integrity sha512-6y7IjGPm8AzlvoUrwAaw1tLnUBudaS3752vcd8JtrpGGQn+rXIe63LFVHm/YMwtqAuh+LJPCFdlLYPWM1nYn6w==
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
    integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=
  
  ms@2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
    integrity sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==
  
  ms@^2.1.1:
    version "2.1.2"
    resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
    integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==
  
  multimatch@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/multimatch/-/multimatch-4.0.0.tgz#8c3c0f6e3e8449ada0af3dd29efb491a375191b3"
    integrity sha512-lDmx79y1z6i7RNx0ZGCPq1bzJ6ZoDDKbvh7jxr9SJcWLkShMzXrHbYVpTdnhNM5MXpDUxCQ4DgqVttVXlBgiBQ==
    dependencies:
      "@types/minimatch" "^3.0.3"
      array-differ "^3.0.0"
      array-union "^2.1.0"
      arrify "^2.0.1"
      minimatch "^3.0.4"
  
  mute-stream@0.0.7:
    version "0.0.7"
    resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
    integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=
  
  mute-stream@0.0.8:
    version "0.0.8"
    resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
    integrity sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==
  
  nan@^2.12.1:
    version "2.14.0"
    resolved "https://registry.yarnpkg.com/nan/-/nan-2.14.0.tgz#7818f722027b2459a86f0295d434d1fc2336c52c"
    integrity sha512-INOFj37C7k3AfaNTtX8RhsTw7qRy7eLET14cROi9+5HAVbbHuIWUHEauBv5qT4Av2tWasiTY1Jw6puUNqRJXQg==
  
  nanomatch@^1.2.9:
    version "1.2.13"
    resolved "https://registry.yarnpkg.com/nanomatch/-/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
    integrity sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==
    dependencies:
      arr-diff "^4.0.0"
      array-unique "^0.3.2"
      define-property "^2.0.2"
      extend-shallow "^3.0.2"
      fragment-cache "^0.2.1"
      is-windows "^1.0.2"
      kind-of "^6.0.2"
      object.pick "^1.3.0"
      regex-not "^1.0.0"
      snapdragon "^0.8.1"
      to-regex "^3.0.1"
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
    integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=
  
  negotiator@0.6.2:
    version "0.6.2"
    resolved "https://registry.yarnpkg.com/negotiator/-/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
    integrity sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==
  
  nice-try@^1.0.4:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/nice-try/-/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
    integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==
  
  node-emoji@^1.10.0:
    version "1.10.0"
    resolved "https://registry.yarnpkg.com/node-emoji/-/node-emoji-1.10.0.tgz#8886abd25d9c7bb61802a658523d1f8d2a89b2da"
    integrity sha512-Yt3384If5H6BYGVHiHwTL+99OzJKHhgp82S8/dktEK73T26BazdgZ4JZh92xSVtGNJvz9UbXdNAc5hcrXV42vw==
    dependencies:
      lodash.toarray "^4.4.0"
  
  node-fetch@^1.0.1:
    version "1.7.3"
    resolved "https://registry.yarnpkg.com/node-fetch/-/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
    integrity sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==
    dependencies:
      encoding "^0.1.11"
      is-stream "^1.0.1"
  
  node-fetch@^2.0.0-alpha.8, node-fetch@^2.1.2, node-fetch@^2.2.0, node-fetch@^2.5.0:
    version "2.6.0"
    resolved "https://registry.yarnpkg.com/node-fetch/-/node-fetch-2.6.0.tgz#e633456386d4aa55863f676a7ab0daa8fdecb0fd"
    integrity sha512-8dG4H5ujfvFiqDmVu9fQ5bOHUC15JMjMY/Zumv26oOvvVJjM67KF8koCWIabKQ1GJIa9r2mMZscBq/TbdOcmNA==
  
  node-int64@^0.4.0:
    version "0.4.0"
    resolved "https://registry.yarnpkg.com/node-int64/-/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
    integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=
  
  node-modules-regexp@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/node-modules-regexp/-/node-modules-regexp-1.0.0.tgz#8d9dbe28964a4ac5712e9131642107c71e90ec40"
    integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=
  
  node-notifier@^5.2.1:
    version "5.4.3"
    resolved "https://registry.yarnpkg.com/node-notifier/-/node-notifier-5.4.3.tgz#cb72daf94c93904098e28b9c590fd866e464bd50"
    integrity sha512-M4UBGcs4jeOK9CjTsYwkvH6/MzuUmGCyTW+kCY7uO+1ZVr0+FHGdPdIf5CCLqAaxnRrWidyoQlNkMIIVwbKB8Q==
    dependencies:
      growly "^1.3.0"
      is-wsl "^1.1.0"
      semver "^5.5.0"
      shellwords "^0.1.1"
      which "^1.3.0"
  
  node-releases@^1.1.44:
    version "1.1.44"
    resolved "https://registry.yarnpkg.com/node-releases/-/node-releases-1.1.44.tgz#cd66438a6eb875e3eb012b6a12e48d9f4326ffd7"
    integrity sha512-NwbdvJyR7nrcGrXvKAvzc5raj/NkoJudkarh2yIpJ4t0NH4aqjUDz/486P+ynIW5eokKOfzGNRdYoLfBlomruw==
    dependencies:
      semver "^6.3.0"
  
  noop-fn@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/noop-fn/-/noop-fn-1.0.0.tgz#5f33d47f13d2150df93e0cb036699e982f78ffbf"
    integrity sha1-XzPUfxPSFQ35PgywNmmemC94/78=
  
  normalize-css-color@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/normalize-css-color/-/normalize-css-color-1.0.2.tgz#02991e97cccec6623fe573afbbf0de6a1f3e9f8d"
    integrity sha1-Apkel8zOxmI/5XOvu/Deah8+n40=
  
  normalize-package-data@^2.3.2, normalize-package-data@^2.5.0:
    version "2.5.0"
    resolved "https://registry.yarnpkg.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
    integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
    dependencies:
      hosted-git-info "^2.1.4"
      resolve "^1.10.0"
      semver "2 || 3 || 4 || 5"
      validate-npm-package-license "^3.0.1"
  
  normalize-path@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
    integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
    dependencies:
      remove-trailing-separator "^1.0.1"
  
  normalize-svg-path@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/normalize-svg-path/-/normalize-svg-path-1.0.1.tgz#6f729ad6b70bb4ca4eff2fe4b107489efe1d56fe"
    integrity sha1-b3Ka1rcLtMpO/y/ksQdInv4dVv4=
    dependencies:
      svg-arc-to-cubic-bezier "^3.0.0"
  
  normalizr@^3.6.0:
    version "3.6.0"
    resolved "https://registry.yarnpkg.com/normalizr/-/normalizr-3.6.0.tgz#b8bbc4546ffe43c1c2200503041642915fcd3e1c"
    integrity sha512-25cd8DiDu+pL46KIaxtVVvvEPjGacJgv0yUg950evr62dQ/ks2JO1kf7+Vi5/rMFjaSTSTls7aCnmRlUSljtiA==
  
  npm-run-path@^2.0.0:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
    integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
    dependencies:
      path-key "^2.0.0"
  
  npm-run-path@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/npm-run-path/-/npm-run-path-3.1.0.tgz#7f91be317f6a466efed3c9f2980ad8a4ee8b0fa5"
    integrity sha512-Dbl4A/VfiVGLgQv29URL9xshU8XDY1GeLy+fsaZ1AA8JDSfjvr5P5+pzRbWqRSBxk6/DW7MIh8lTM/PaGnP2kg==
    dependencies:
      path-key "^3.0.0"
  
  nth-check@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/nth-check/-/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
    integrity sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==
    dependencies:
      boolbase "~1.0.0"
  
  nullthrows@^1.1.0:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/nullthrows/-/nullthrows-1.1.1.tgz#7818258843856ae971eae4208ad7d7eb19a431b1"
    integrity sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==
  
  number-is-nan@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
    integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=
  
  ob1@0.56.3:
    version "0.56.3"
    resolved "https://registry.yarnpkg.com/ob1/-/ob1-0.56.3.tgz#5829e446587c9bf89c22ece4f3757b29f2ccfd18"
    integrity sha512-3JL2ZyWOHDGTEAe4kcG+TxhGPKCCikgyoUIjE82JnXnmpR1LXItM9K3WhGsi4+O7oYngMW6FjpHHoc5xJTMkTQ==
  
  object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
    version "4.1.1"
    resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
    integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=
  
  object-copy@^0.1.0:
    version "0.1.0"
    resolved "https://registry.yarnpkg.com/object-copy/-/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
    integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
    dependencies:
      copy-descriptor "^0.1.0"
      define-property "^0.2.5"
      kind-of "^3.0.3"
  
  object-inspect@^1.7.0:
    version "1.7.0"
    resolved "https://registry.yarnpkg.com/object-inspect/-/object-inspect-1.7.0.tgz#f4f6bd181ad77f006b5ece60bd0b6f398ff74a67"
    integrity sha512-a7pEHdh1xKIAgTySUGgLMx/xwDZskN1Ud6egYYN3EdRW4ZMPNEDUTF+hwy2LUC+Bl+SyLXANnwz/jyh/qutKUw==
  
  object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
    integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
  
  object-visit@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/object-visit/-/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
    integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
    dependencies:
      isobject "^3.0.0"
  
  object.assign@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/object.assign/-/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
    integrity sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==
    dependencies:
      define-properties "^1.1.2"
      function-bind "^1.1.1"
      has-symbols "^1.0.0"
      object-keys "^1.0.11"
  
  object.entries@^1.1.0:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/object.entries/-/object.entries-1.1.1.tgz#ee1cf04153de02bb093fec33683900f57ce5399b"
    integrity sha512-ilqR7BgdyZetJutmDPfXCDffGa0/Yzl2ivVNpbx/g4UeWrCdRnFDUBrKJGLhGieRHDATnyZXWBeCb29k9CJysQ==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.0-next.1"
      function-bind "^1.1.1"
      has "^1.0.3"
  
  object.fromentries@^2.0.0, object.fromentries@^2.0.1:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/object.fromentries/-/object.fromentries-2.0.2.tgz#4a09c9b9bb3843dd0f89acdb517a794d4f355ac9"
    integrity sha512-r3ZiBH7MQppDJVLx6fhD618GKNG40CZYH9wgwdhKxBDDbQgjeWGGd4AtkZad84d291YxvWe7bJGuE65Anh0dxQ==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.0-next.1"
      function-bind "^1.1.1"
      has "^1.0.3"
  
  object.getownpropertydescriptors@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.0.tgz#369bf1f9592d8ab89d712dced5cb81c7c5352649"
    integrity sha512-Z53Oah9A3TdLoblT7VKJaTDdXdT+lQO+cNpKVnya5JDe9uLvzu1YyY1yFDFrcxrlRgWrEFH0jJtD/IbuwjcEVg==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.0-next.1"
  
  object.pick@^1.3.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/object.pick/-/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
    integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
    dependencies:
      isobject "^3.0.1"
  
  object.values@^1.1.0:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/object.values/-/object.values-1.1.1.tgz#68a99ecde356b7e9295a3c5e0ce31dc8c953de5e"
    integrity sha512-WTa54g2K8iu0kmS/us18jEmdv1a4Wi//BZ/DTVYEcH0XhLM5NYdpDHja3gt57VrZLcNAO2WGA+KpWsDBaHt6eA==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.0-next.1"
      function-bind "^1.1.1"
      has "^1.0.3"
  
  on-finished@~2.3.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
    integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
    dependencies:
      ee-first "1.1.1"
  
  on-headers@~1.0.1, on-headers@~1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
    integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==
  
  once@^1.3.0, once@^1.3.1, once@^1.4.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
    integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
    dependencies:
      wrappy "1"
  
  onetime@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
    integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
    dependencies:
      mimic-fn "^1.0.0"
  
  onetime@^5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/onetime/-/onetime-5.1.0.tgz#fff0f3c91617fe62bb50189636e99ac8a6df7be5"
    integrity sha512-5NcSkPHhwTVFIQN+TUqXoS5+dlElHXdpAWu9I0HP20YOtIi+aZ0Ct82jdlILDxjLEAWwvm+qj1m6aEtsDVmm6Q==
    dependencies:
      mimic-fn "^2.1.0"
  
  open@^6.2.0:
    version "6.4.0"
    resolved "https://registry.yarnpkg.com/open/-/open-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
    integrity sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg==
    dependencies:
      is-wsl "^1.1.0"
  
  opencollective-postinstall@^2.0.0, opencollective-postinstall@^2.0.2:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/opencollective-postinstall/-/opencollective-postinstall-2.0.2.tgz#5657f1bede69b6e33a45939b061eb53d3c6c3a89"
    integrity sha512-pVOEP16TrAO2/fjej1IdOyupJY8KDUM1CvsaScRbw6oddvpQoOfGk4ywha0HKKVAD6RkW4x6Q+tNBwhf3Bgpuw==
  
  opn@^5.4.0:
    version "5.5.0"
    resolved "https://registry.yarnpkg.com/opn/-/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
    integrity sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA==
    dependencies:
      is-wsl "^1.1.0"
  
  optionator@^0.8.3:
    version "0.8.3"
    resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
    integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
    dependencies:
      deep-is "~0.1.3"
      fast-levenshtein "~2.0.6"
      levn "~0.3.0"
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
      word-wrap "~1.2.3"
  
  options@>=0.0.5:
    version "0.0.6"
    resolved "https://registry.yarnpkg.com/options/-/options-0.0.6.tgz#ec22d312806bb53e731773e7cdaefcf1c643128f"
    integrity sha1-7CLTEoBrtT5zF3Pnza788cZDEo8=
  
  ora@^3.4.0:
    version "3.4.0"
    resolved "https://registry.yarnpkg.com/ora/-/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
    integrity sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==
    dependencies:
      chalk "^2.4.2"
      cli-cursor "^2.1.0"
      cli-spinners "^2.0.0"
      log-symbols "^2.2.0"
      strip-ansi "^5.2.0"
      wcwidth "^1.0.1"
  
  os-locale@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/os-locale/-/os-locale-2.1.0.tgz#42bc2900a6b5b8bd17376c8e882b65afccf24bf2"
    integrity sha512-3sslG3zJbEYcaC4YVAvDorjGxc7tv6KVATnLPZONiljsUncvihe9BQoVCEs0RZ1kmf4Hk9OBqlZfJZWI4GanKA==
    dependencies:
      execa "^0.7.0"
      lcid "^1.0.0"
      mem "^1.1.0"
  
  os-locale@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/os-locale/-/os-locale-3.1.0.tgz#a802a6ee17f24c10483ab9935719cef4ed16bf1a"
    integrity sha512-Z8l3R4wYWM40/52Z+S265okfFj8Kt2cC2MKY+xNi3kFs+XGI7WXu/I309QQQYbRW4ijiZ+yxs9pqEhJh0DqW3Q==
    dependencies:
      execa "^1.0.0"
      lcid "^2.0.0"
      mem "^4.0.0"
  
  os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
    integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=
  
  p-defer@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/p-defer/-/p-defer-1.0.0.tgz#9f6eb182f6c9aa8cd743004a7d4f96b196b0fb0c"
    integrity sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=
  
  p-finally@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/p-finally/-/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
    integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=
  
  p-finally@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/p-finally/-/p-finally-2.0.1.tgz#bd6fcaa9c559a096b680806f4d657b3f0f240561"
    integrity sha512-vpm09aKwq6H9phqRQzecoDpD8TmVyGw70qmWlyq5onxY7tqyTTFVvxMykxQSQKILBSFlbXpypIw2T1Ml7+DDtw==
  
  p-is-promise@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/p-is-promise/-/p-is-promise-2.1.0.tgz#918cebaea248a62cf7ffab8e3bca8c5f882fc42e"
    integrity sha512-Y3W0wlRPK8ZMRbNq97l4M5otioeA5lm1z7bkNkxCka8HSPjR0xRWmpCmc9utiaLP9Jb1eD8BgeIxTW4AIF45Pg==
  
  p-limit@^1.1.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
    integrity sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==
    dependencies:
      p-try "^1.0.0"
  
  p-limit@^2.0.0, p-limit@^2.2.0:
    version "2.2.2"
    resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-2.2.2.tgz#61279b67721f5287aa1c13a9a7fbbc48c9291b1e"
    integrity sha512-WGR+xHecKTr7EbUEhyLSh5Dube9JtdiG78ufaeLxTgpudf/20KqyMioIUZJAezlTIi6evxuoUs9YXc11cU+yzQ==
    dependencies:
      p-try "^2.0.0"
  
  p-locate@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
    integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
    dependencies:
      p-limit "^1.1.0"
  
  p-locate@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
    integrity sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==
    dependencies:
      p-limit "^2.0.0"
  
  p-locate@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
    integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
    dependencies:
      p-limit "^2.2.0"
  
  p-try@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/p-try/-/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
    integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=
  
  p-try@^2.0.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
    integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
    integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
    dependencies:
      callsites "^3.0.0"
  
  parse-json@^2.2.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
    integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
    dependencies:
      error-ex "^1.2.0"
  
  parse-json@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
    integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
    dependencies:
      error-ex "^1.3.1"
      json-parse-better-errors "^1.0.1"
  
  parse-json@^5.0.0:
    version "5.0.0"
    resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-5.0.0.tgz#73e5114c986d143efa3712d4ea24db9a4266f60f"
    integrity sha512-OOY5b7PAEFV0E2Fir1KOkxchnZNCdowAJgQ5NuxjpBKTRP3pQhwkrkxqQjeoKJ+fO7bCpmIZaogI4eZGDMEGOw==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      error-ex "^1.3.1"
      json-parse-better-errors "^1.0.1"
      lines-and-columns "^1.1.6"
  
  parse-node-version@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/parse-node-version/-/parse-node-version-1.0.1.tgz#e2b5dbede00e7fa9bc363607f53327e8b073189b"
    integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==
  
  parse-svg-path@^0.1.2:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/parse-svg-path/-/parse-svg-path-0.1.2.tgz#7a7ec0d1eb06fa5325c7d3e009b859a09b5d49eb"
    integrity sha1-en7A0esG+lMlx9PgCbhZoJtdSes=
  
  parseurl@~1.3.3:
    version "1.3.3"
    resolved "https://registry.yarnpkg.com/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
    integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==
  
  pascalcase@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/pascalcase/-/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
    integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=
  
  path-browserify@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/path-browserify/-/path-browserify-1.0.0.tgz#40702a97af46ae00b0ea6fa8998c0b03c0af160d"
    integrity sha512-Hkavx/nY4/plImrZPHRk2CL9vpOymZLgEbMNX1U0bjcBL7QN9wODxyx0yaMZURSQaUtSEvDrfAvxa9oPb0at9g==
  
  path-dirname@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/path-dirname/-/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
    integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=
  
  path-exists@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
    integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
    integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
    integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=
  
  path-key@^2.0.0, path-key@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
    integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=
  
  path-key@^3.0.0, path-key@^3.1.0:
    version "3.1.1"
    resolved "https://registry.yarnpkg.com/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
    integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
  
  path-parse@^1.0.6:
    version "1.0.6"
    resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
    integrity sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==
  
  path-to-regexp@^1.7.0:
    version "1.8.0"
    resolved "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-1.8.0.tgz#887b3ba9d84393e87a0a0b9f4cb756198b53548a"
    integrity sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==
    dependencies:
      isarray "0.0.1"
  
  path-type@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/path-type/-/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
    integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
    dependencies:
      pify "^2.0.0"
  
  paths-js@^0.4.10:
    version "0.4.10"
    resolved "https://registry.yarnpkg.com/paths-js/-/paths-js-0.4.10.tgz#a3575f409b4a36f8aa795ba4d051989021be58c7"
    integrity sha512-JZoqlRSHtx+bc+xKI9o4bropEbqZBF4ZfYImiB1T9RYpHB73h5I8XZ7FfSBbHbBMtdD1c04ujjAPH8wUuu4+Gw==
  
  pify@^2.0.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
    integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=
  
  pify@^4.0.1:
    version "4.0.1"
    resolved "https://registry.yarnpkg.com/pify/-/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
    integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==
  
  pirates@^4.0.0:
    version "4.0.1"
    resolved "https://registry.yarnpkg.com/pirates/-/pirates-4.0.1.tgz#643a92caf894566f91b2b986d2c66950a8e2fb87"
    integrity sha512-WuNqLTbMI3tmfef2TKxlQmAiLHKtFhlsCZnPIpuv2Ow0RDVO8lfy1Opf4NUzlMXLjPl+Men7AuVdX6TA+s+uGA==
    dependencies:
      node-modules-regexp "^1.0.0"
  
  pkg-dir@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-2.0.0.tgz#f6d5d1109e19d63edf428e0bd57e12777615334b"
    integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
    dependencies:
      find-up "^2.1.0"
  
  pkg-dir@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
    integrity sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==
    dependencies:
      find-up "^3.0.0"
  
  pkg-dir@^4.2.0:
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
    integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
    dependencies:
      find-up "^4.0.0"
  
  pkg-up@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/pkg-up/-/pkg-up-2.0.0.tgz#c819ac728059a461cab1c3889a2be3c49a004d7f"
    integrity sha1-yBmscoBZpGHKscOImivjxJoATX8=
    dependencies:
      find-up "^2.1.0"
  
  pkg-up@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/pkg-up/-/pkg-up-3.1.0.tgz#100ec235cc150e4fd42519412596a28512a0def5"
    integrity sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==
    dependencies:
      find-up "^3.0.0"
  
  please-upgrade-node@^3.2.0:
    version "3.2.0"
    resolved "https://registry.yarnpkg.com/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
    integrity sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==
    dependencies:
      semver-compare "^1.0.0"
  
  plist@^3.0.0, plist@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/plist/-/plist-3.0.1.tgz#a9b931d17c304e8912ef0ba3bdd6182baf2e1f8c"
    integrity sha512-GpgvHHocGRyQm74b6FWEZZVRroHKE1I0/BTjAmySaohK+cUn+hZpbqXkc3KWgW3gQYkqcQej35FohcT0FRlkRQ==
    dependencies:
      base64-js "^1.2.3"
      xmlbuilder "^9.0.7"
      xmldom "0.1.x"
  
  plugin-error@^0.1.2:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/plugin-error/-/plugin-error-0.1.2.tgz#3b9bb3335ccf00f425e07437e19276967da47ace"
    integrity sha1-O5uzM1zPAPQl4HQ34ZJ2ln2kes4=
    dependencies:
      ansi-cyan "^0.1.1"
      ansi-red "^0.1.1"
      arr-diff "^1.0.1"
      arr-union "^2.0.1"
      extend-shallow "^1.1.2"
  
  point-in-polygon@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/point-in-polygon/-/point-in-polygon-1.0.1.tgz#d59b64e8fee41c49458aac82b56718c5957b2af7"
    integrity sha1-1Ztk6P7kHElFiqyCtWcYxZV7Kvc=
  
  posix-character-classes@^0.1.0:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/posix-character-classes/-/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
    integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=
  
  pouchdb-collections@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/pouchdb-collections/-/pouchdb-collections-1.0.1.tgz#fe63a17da977611abef7cb8026cb1a9553fd8359"
    integrity sha1-/mOhfal3YRq+98uAJssalVP9g1k=
  
  prelude-ls@~1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
    integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=
  
  prettier-linter-helpers@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
    integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
    dependencies:
      fast-diff "^1.1.2"
  
  prettier@1.17.0:
    version "1.17.0"
    resolved "https://registry.yarnpkg.com/prettier/-/prettier-1.17.0.tgz#53b303676eed22cc14a9f0cec09b477b3026c008"
    integrity sha512-sXe5lSt2WQlCbydGETgfm1YBShgOX4HxQkFPvbxkcwgDvGDeqVau8h+12+lmSVlP3rHPz0oavfddSZg/q+Szjw==
  
  prettier@^1.19.1:
    version "1.19.1"
    resolved "https://registry.yarnpkg.com/prettier/-/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"
    integrity sha512-s7PoyDv/II1ObgQunCbB9PdLmUcBZcnWOcxDh7O0N/UwDEsHyqkW+Qh28jW+mVuCdx7gLB0BotYI1Y6uI9iyew==
  
  pretty-format@^23.6.0:
    version "23.6.0"
    resolved "https://registry.yarnpkg.com/pretty-format/-/pretty-format-23.6.0.tgz#5eaac8eeb6b33b987b7fe6097ea6a8a146ab5760"
    integrity sha512-zf9NV1NSlDLDjycnwm6hpFATCGl/K1lt0R/GdkAK2O5LN/rwJoB+Mh93gGJjut4YbmecbfgLWVGSTCr0Ewvvbw==
    dependencies:
      ansi-regex "^3.0.0"
      ansi-styles "^3.2.0"
  
  pretty-format@^24.7.0, pretty-format@^24.9.0:
    version "24.9.0"
    resolved "https://registry.yarnpkg.com/pretty-format/-/pretty-format-24.9.0.tgz#12fac31b37019a4eea3c11aa9a959eb7628aa7c9"
    integrity sha512-00ZMZUiHaJrNfk33guavqgvfJS30sLYf0f8+Srklv0AMPodGGHcoHgksZ3OThYnIvOd+8yMCn0YiEOogjlgsnA==
    dependencies:
      "@jest/types" "^24.9.0"
      ansi-regex "^4.0.0"
      ansi-styles "^3.2.0"
      react-is "^16.8.4"
  
  pretty-quick@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/pretty-quick/-/pretty-quick-2.0.1.tgz#417ee605ade98ecc686e72f63b5d28a2c35b43e9"
    integrity sha512-y7bJt77XadjUr+P1uKqZxFWLddvj3SKY6EU4BuQtMxmmEFSMpbN132pUWdSG1g1mtUfO0noBvn7wBf0BVeomHg==
    dependencies:
      chalk "^2.4.2"
      execa "^2.1.0"
      find-up "^4.1.0"
      ignore "^5.1.4"
      mri "^1.1.4"
      multimatch "^4.0.0"
  
  private@^0.1.6:
    version "0.1.8"
    resolved "https://registry.yarnpkg.com/private/-/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"
    integrity sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==
  
  process-nextick-args@~2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
    integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==
  
  progress@2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/progress/-/progress-2.0.0.tgz#8a1be366bf8fc23db2bd23f10c6fe920b4389d1f"
    integrity sha1-ihvjZr+Pwj2yvSPxDG/pILQ4nR8=
  
  progress@^2.0.0:
    version "2.0.3"
    resolved "https://registry.yarnpkg.com/progress/-/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
    integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==
  
  promise@^7.1.1:
    version "7.3.1"
    resolved "https://registry.yarnpkg.com/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
    integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
    dependencies:
      asap "~2.0.3"
  
  prop-types@^15.5.10, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2:
    version "15.7.2"
    resolved "https://registry.yarnpkg.com/prop-types/-/prop-types-15.7.2.tgz#52c41e75b8c87e72b9d9360e0206b99dcbffa6c5"
    integrity sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ==
    dependencies:
      loose-envify "^1.4.0"
      object-assign "^4.1.1"
      react-is "^16.8.1"
  
  proxy-from-env@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
    integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==
  
  pseudomap@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
    integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=
  
  pump@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/pump/-/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
    integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
    dependencies:
      end-of-stream "^1.1.0"
      once "^1.3.1"
  
  punycode@^2.1.0:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
    integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==
  
  q@^1.1.2:
    version "1.5.1"
    resolved "https://registry.yarnpkg.com/q/-/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
    integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=
  
  qs@^6.5.0:
    version "6.9.1"
    resolved "https://registry.yarnpkg.com/qs/-/qs-6.9.1.tgz#20082c65cb78223635ab1a9eaca8875a29bf8ec9"
    integrity sha512-Cxm7/SS/y/Z3MHWSxXb8lIFqgqBowP5JMlTUFyJN88y0SGQhVmZnqFK/PeuMX9LzUyWsqqhNxIyg0jlzq946yA==
  
  query-string@^6.12.1:
    version "6.12.1"
    resolved "https://registry.yarnpkg.com/query-string/-/query-string-6.12.1.tgz#2ae4d272db4fba267141665374e49a1de09e8a7c"
    integrity sha512-OHj+zzfRMyj3rmo/6G8a5Ifvw3AleL/EbcHMD27YA31Q+cO5lfmQxECkImuNVjcskLcvBRVHNAB3w6udMs1eAA==
    dependencies:
      decode-uri-component "^0.2.0"
      split-on-first "^1.0.0"
      strict-uri-encode "^2.0.0"
  
  query-string@^6.4.2:
    version "6.9.0"
    resolved "https://registry.yarnpkg.com/query-string/-/query-string-6.9.0.tgz#1c3b727c370cf00f177c99f328fda2108f8fa3dd"
    integrity sha512-KG4bhCFYapExLsUHrFt+kQVEegF2agm4cpF/VNc6pZVthIfCc/GK8t8VyNIE3nyXG9DK3Tf2EGkxjR6/uRdYsA==
    dependencies:
      decode-uri-component "^0.2.0"
      split-on-first "^1.0.0"
      strict-uri-encode "^2.0.0"
  
  querystringify@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/querystringify/-/querystringify-2.1.1.tgz#60e5a5fd64a7f8bfa4d2ab2ed6fdf4c85bad154e"
    integrity sha512-w7fLxIRCRT7U8Qu53jQnJyPkYZIaR4n5151KMfcJlO/A9397Wxb1amJvROTK6TOnp7PfoAmg/qXiNHI+08jRfA==
  
  r2@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/r2/-/r2-2.0.1.tgz#94cd802ecfce9a622549c8182032d8e4a2b2e612"
    integrity sha512-EEmxoxYCe3LHzAUhRIRxdCKERpeRNmlLj6KLUSORqnK6dWl/K5ShmDGZqM2lRZQeqJgF+wyqk0s1M7SWUveNOQ==
    dependencies:
      caseless "^0.12.0"
      node-fetch "^2.0.0-alpha.8"
      typedarray-to-buffer "^3.1.2"
  
  range-parser@~1.2.1:
    version "1.2.1"
    resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
    integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==
  
  react-addons-shallow-compare@15.6.2:
    version "15.6.2"
    resolved "https://registry.yarnpkg.com/react-addons-shallow-compare/-/react-addons-shallow-compare-15.6.2.tgz#198a00b91fc37623db64a28fd17b596ba362702f"
    integrity sha1-GYoAuR/DdiPbZKKP0XtZa6NicC8=
    dependencies:
      fbjs "^0.8.4"
      object-assign "^4.1.0"
  
  react-devtools-core@^3.6.3:
    version "3.6.3"
    resolved "https://registry.yarnpkg.com/react-devtools-core/-/react-devtools-core-3.6.3.tgz#977d95b684c6ad28205f0c62e1e12c5f16675814"
    integrity sha512-+P+eFy/yo8Z/UH9J0DqHZuUM5+RI2wl249TNvMx3J2jpUomLQa4Zxl56GEotGfw3PIP1eI+hVf1s53FlUONStQ==
    dependencies:
      shell-quote "^1.6.1"
      ws "^3.3.1"
  
  react-dom@16.9.0:
    version "16.9.0"
    resolved "https://registry.yarnpkg.com/react-dom/-/react-dom-16.9.0.tgz#5e65527a5e26f22ae3701131bcccaee9fb0d3962"
    integrity sha512-YFT2rxO9hM70ewk9jq0y6sQk8cL02xm4+IzYBz75CQGlClQQ1Bxq0nhHF6OtSbit+AIahujJgb/CPRibFkMNJQ==
    dependencies:
      loose-envify "^1.1.0"
      object-assign "^4.1.1"
      prop-types "^15.6.2"
      scheduler "^0.15.0"
  
  react-is@^16.7.0, react-is@^16.8.1, react-is@^16.8.4, react-is@^16.8.6, react-is@^16.9.0:
    version "16.12.0"
    resolved "https://registry.yarnpkg.com/react-is/-/react-is-16.12.0.tgz#2cc0fe0fba742d97fd527c42a13bec4eeb06241c"
    integrity sha512-rPCkf/mWBtKc97aLL9/txD8DZdemK0vkA3JMLShjlJB3Pj3s+lpf1KaBzMfQrAmhMQB0n1cU/SUGgKKBCe837Q==
  
  react-lifecycles-compat@^3.0.4:
    version "3.0.4"
    resolved "https://registry.yarnpkg.com/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
    integrity sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==
  
  react-native-animatable@^1.2.4:
    version "1.3.3"
    resolved "https://registry.yarnpkg.com/react-native-animatable/-/react-native-animatable-1.3.3.tgz#a13a4af8258e3bb14d0a9d839917e9bb9274ec8a"
    integrity sha512-2ckIxZQAsvWn25Ho+DK3d1mXIgj7tITkrS4pYDvx96WyOttSvzzFeQnM2od0+FUMzILbdHDsDEqZvnz1DYNQ1w==
    dependencies:
      prop-types "^15.7.2"
  
  react-native-appearance@~0.3.3:
    version "0.3.3"
    resolved "https://registry.yarnpkg.com/react-native-appearance/-/react-native-appearance-0.3.3.tgz#7267eccbc7a0bc7ea743eecd85c68aabc37f81d4"
    integrity sha512-XETmdFGMH1j07mIrri74dG+qUvW/vme7SIZWbFup9Ra5p/eiEkl6kWbQljap4+G+PUIsPjU5OdbUaNz8Zs9DAg==
    dependencies:
      fbemitter "^2.1.1"
      invariant "^2.2.4"
      use-subscription "^1.0.0"
  
  react-native-button@^2.3.0:
    version "2.4.0"
    resolved "https://registry.yarnpkg.com/react-native-button/-/react-native-button-2.4.0.tgz#42b487073a7ce7c54ab503f77c6ced1ab95742bf"
    integrity sha512-4siaJlpOLeL9fAhX8VU3cnUfcGLe3E2zABDWSKxkF+NiYOd+AnKeYY29WXlV8hXhCFo+Ry7E+alrJ6zjZLTSfg==
    dependencies:
      prop-types "^15.5.10"
  
  react-native-chart-kit@^5.6.0:
    version "5.6.0"
    resolved "https://registry.yarnpkg.com/react-native-chart-kit/-/react-native-chart-kit-5.6.0.tgz#288b55eedb83c457999c818178fbf84dac22611d"
    integrity sha512-KYcbU5X5w2afFFbgfYL7W6yZudjBegswwkn6heg2JDwpX5fll7nRYenMnfskzqbZ0yTg7gpWgrR66byxLcxddQ==
    dependencies:
      lodash "^4.17.13"
      paths-js "^0.4.10"
      point-in-polygon "^1.0.1"
  
  react-native-elements@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/react-native-elements/-/react-native-elements-2.0.0.tgz#eebe36c3250808ebd4a7e9dcf97a666fb7a20930"
    integrity sha512-xViTU/JlabYX94fDL2iu17gvMtgEOq2lFAToYlU3RBkwb/J13cdwSr8Ti9z6v6Iui4f8S3FjkpRJnFaOsZrK7w==
    dependencies:
      "@types/react-native-vector-icons" "^6.4.4"
      color "^3.1.0"
      deepmerge "^3.1.0"
      hoist-non-react-statics "^3.1.0"
      opencollective-postinstall "^2.0.0"
      prop-types "^15.7.2"
      react-native-ratings "^6.5.0"
      react-native-status-bar-height "^2.2.0"
  
  react-native-expo-image-cache@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/react-native-expo-image-cache/-/react-native-expo-image-cache-4.1.0.tgz#649cbe9786249134d3eafed5baba50bbfa80c029"
    integrity sha512-U6xHtuyalNZThhM11lu4+mRNSpJFkdh4dSLbWkKAj5QfY63cKlTnDVtv8c88njn71GHL4exEzf8hNKBMWhH37Q==
    dependencies:
      crypto-js "^3.1.9-1"
      lodash "^4.17.4"
  
  react-native-gesture-handler@~1.6.0:
    version "1.6.1"
    resolved "https://registry.yarnpkg.com/react-native-gesture-handler/-/react-native-gesture-handler-1.6.1.tgz#678e2dce250ed66e93af409759be22cd6375dd17"
    integrity sha512-gQgIKhDiYf754yzhhliagLuLupvGb6ZyBdzYzr7aus3Fyi87TLOw63ers+r4kGw0h26oAWTAdHd34JnF4NeL6Q==
    dependencies:
      "@egjs/hammerjs" "^2.0.17"
      hoist-non-react-statics "^2.3.1"
      invariant "^2.2.4"
      prop-types "^15.7.2"
  
  react-native-ratings@^6.5.0:
    version "6.5.0"
    resolved "https://registry.yarnpkg.com/react-native-ratings/-/react-native-ratings-6.5.0.tgz#a1606ccba3c5b54eec8e6cfa4765a45cf0e4ab8d"
    integrity sha512-YMcfQ7UQCmXGEc/WPlukHSHs5yvckTwjq5fTRk1FG8gaO7fZCNygEUGPuw4Dbvvp3IlsCUn0bOQd63RYsb7NDQ==
    dependencies:
      lodash "^4.17.4"
      prop-types "^15.5.10"
  
  react-native-reanimated@~1.7.0:
    version "1.7.1"
    resolved "https://registry.yarnpkg.com/react-native-reanimated/-/react-native-reanimated-1.7.1.tgz#6363c7f3ebbabc56a05d5dccaf09d95f3b6a2d69"
    integrity sha512-aBwhoQdH4shkeTPbi7vKcAwYOzBp/6zElEKuIOgby11TceoM7y5SgNImC3HbDWWld3QV2PA2AgQGwAy51WgF3Q==
    dependencies:
      fbjs "^1.0.0"
  
  react-native-redash@12.1.0:
    version "12.1.0"
    resolved "https://registry.yarnpkg.com/react-native-redash/-/react-native-redash-12.1.0.tgz#e68e0decd7df8c118446b60a847edeef2a691895"
    integrity sha512-B3B5Jtn/7NQDPBBZ+DMznSl0l0TTvaA28tOJ9COccv1D8Z8r4s07/wz/9bDA5WjNDBLzI2/FOJhZsHVUmguYEw==
    dependencies:
      abs-svg-path "^0.1.1"
      normalize-svg-path "^1.0.1"
      parse-svg-path "^0.1.2"
      use-memo-one "^1.1.1"
  
  react-native-safe-area-context@0.7.3:
    version "0.7.3"
    resolved "https://registry.yarnpkg.com/react-native-safe-area-context/-/react-native-safe-area-context-0.7.3.tgz#ad6bd4abbabe195332c53810e4ce5851eb21aa2a"
    integrity sha512-9Uqu1vlXPi+2cKW/CW6OnHxA76mWC4kF3wvlqzq4DY8hn37AeiXtLFs2WkxH4yXQRrnJdP6ivc65Lz+MqwRZAA==
  
  react-native-safe-area-view@^0.14.1, react-native-safe-area-view@^0.14.6:
    version "0.14.8"
    resolved "https://registry.yarnpkg.com/react-native-safe-area-view/-/react-native-safe-area-view-0.14.8.tgz#ef33c46ff8164ae77acad48c3039ec9c34873e5b"
    integrity sha512-MtRSIcZNstxv87Jet+UsPhEd1tpGe8cVskDXlP657x6rHpSrbrc+y13ZNXrwAgGNNhqQNX7UJT68ZIq//ZRmvw==
    dependencies:
      hoist-non-react-statics "^2.3.1"
  
  "react-native-screens@^1.0.0 || ^1.0.0-alpha":
    version "1.0.0-alpha.23"
    resolved "https://registry.yarnpkg.com/react-native-screens/-/react-native-screens-1.0.0-alpha.23.tgz#25d7ea4d11bda4fcde2d1da7ae50271c6aa636e0"
    integrity sha512-tOxHGQUN83MTmQB4ghoQkibqOdGiX4JQEmeyEv96MKWO/x8T2PJv84ECUos9hD3blPRQwVwSpAid1PPPhrVEaw==
    dependencies:
      debounce "^1.2.0"
  
  react-native-screens@~2.2.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/react-native-screens/-/react-native-screens-2.2.0.tgz#cc4cdf17426fdda97ad93a5e812a1899390f1978"
    integrity sha512-a0VzxOWot7F9B/GQyDSssBRd3jUJazFnTQS61IiyReWB6aHlFhf3Xz10jBRoURXy1EMCDCHgenmTVTkKHpKyqQ==
    dependencies:
      debounce "^1.2.0"
  
  react-native-snap-carousel@^3.9.0:
    version "3.9.0"
    resolved "https://registry.yarnpkg.com/react-native-snap-carousel/-/react-native-snap-carousel-3.9.0.tgz#017793ca3f5e901ccd5a3117d79bb18ec08928a3"
    integrity sha512-XDCUNo0SXfHOCc/v1s96hzrf/N4lDBjXLZMNLLUeSkLsHlH2QryFyQKO48AI08fJmM5JBqGVwcJiF47aMWOmYQ==
    dependencies:
      prop-types "^15.6.1"
      react-addons-shallow-compare "15.6.2"
  
  react-native-star-rating@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/react-native-star-rating/-/react-native-star-rating-1.1.0.tgz#57fcd023c63e947427c4dbbd1c8f4cc49e5c48ce"
    integrity sha512-ocOYx+BKUvfruvXm45MBbQZtpkVO3PQieBDepB0FaLuxE3vUtDTPzHqXuBes3iCM5oRi5umrnmMUMsM0mEq5ZA==
    dependencies:
      prop-types "^15.5.10"
      react-native-animatable "^1.2.4"
      react-native-button "^2.3.0"
      react-native-vector-icons "^4.5.0"
  
  react-native-status-bar-height@^2.2.0:
    version "2.4.0"
    resolved "https://registry.yarnpkg.com/react-native-status-bar-height/-/react-native-status-bar-height-2.4.0.tgz#de8cee4bb733a196167210d2d0bc1fa10acba3e3"
    integrity sha512-pWvZFlyIHiuxLugLioq97vXiaGSovFXEyxt76wQtbq0gxv4dGXMPqYow46UmpwOgeJpBhqL1E0EKxnfJRrFz5w==
  
  react-native-status-bar-height@^2.5.0:
    version "2.5.0"
    resolved "https://registry.yarnpkg.com/react-native-status-bar-height/-/react-native-status-bar-height-2.5.0.tgz#bc0fb85230603850aab9667ee8111a62954de90c"
    integrity sha512-sYBCPYA/NapBSHkdm/IVL4ID3LLlIuLqINi2FBDyMkc2BU9pfSGOtkz9yfxoK39mYJuTrlTOQ7mManARUsYDSA==
  
  react-native-svg-transformer@^0.14.3:
    version "0.14.3"
    resolved "https://registry.yarnpkg.com/react-native-svg-transformer/-/react-native-svg-transformer-0.14.3.tgz#43c8e176f5a11f16f39b87a64018e0ac090ffbdb"
    integrity sha512-agDGdMeeBAsWEgg/u7mjtR2Z3c8smGCLep/n3svwifut9dpswZCP+bSIrU8ekg6RNtxAJL+eGJbWjJ38vWxw6g==
    dependencies:
      "@svgr/core" "^4.3.3"
      "@svgr/plugin-svgo" "^4.3.1"
      path-dirname "^1.0.2"
      semver "^5.6.0"
  
  react-native-svg@11.0.1:
    version "11.0.1"
    resolved "https://registry.yarnpkg.com/react-native-svg/-/react-native-svg-11.0.1.tgz#e92e7e9f9cb2604333fd5014fd4edcf62fea6496"
    integrity sha512-XriIwSoe9eTtKyqxpNC6POSOqmXAB9mZQOm5tMoaimEqQOMfzgYrBoF9nY6VPGmaH5dRlWBqnnBf389APiZFcQ==
    dependencies:
      css-select "^2.1.0"
      css-tree "^1.0.0-alpha.39"
  
  react-native-tab-view@^2.11.0:
    version "2.11.0"
    resolved "https://registry.yarnpkg.com/react-native-tab-view/-/react-native-tab-view-2.11.0.tgz#2e57d1f617ccc88c7f452708804f3409f880b700"
    integrity sha512-vqetlxGO7A8bnqvXcB50MWpRZAImXFrDGz1WCQKdCqe03Ey3ZzENe7yLuWrtBJYlepGfOLAsmCXv+wW82Yfm1w==
  
  react-native-tab-view@^2.14.0:
    version "2.14.0"
    resolved "https://registry.yarnpkg.com/react-native-tab-view/-/react-native-tab-view-2.14.0.tgz#dd801f82fdb8e92fce9688b78d13923f3a5a2b3f"
    integrity sha512-Z7XAM3NsMB96c17e9EAveunk7SUgilXWwVUTmWI1u/+p2SeOMUOB87z+//UXrcVlD1e9BPweR73q7dq7krTzTg==
  
  react-native-toast-message@^1.3.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/react-native-toast-message/-/react-native-toast-message-1.3.0.tgz#6e689add9511d8822e86487d1e0f691d3443fae4"
    integrity sha512-27m9h1ELAOnOZgjqESY/85QiK8CPWsuNEyoYyzikVHOi5ZTWu/LyMbIzS5Km6OIrq+vVr3cc7HY88670Xtb1xA==
  
  react-native-vector-icons@^4.5.0:
    version "4.6.0"
    resolved "https://registry.yarnpkg.com/react-native-vector-icons/-/react-native-vector-icons-4.6.0.tgz#e4014311ffa6de397d914ffc31b7097a874cc8d5"
    integrity sha512-rpfhfPiXCK2PX1nrNhdxSMrEGB/Gw/SvKoPM0G2wAkSoqynnes19K0VYI+Up7DqR1rFIpE4hP2erpT1tNx2tfg==
    dependencies:
      lodash "^4.0.0"
      prop-types "^15.5.10"
      yargs "^8.0.2"
  
  react-native-view-shot@3.1.2:
    version "3.1.2"
    resolved "https://registry.yarnpkg.com/react-native-view-shot/-/react-native-view-shot-3.1.2.tgz#8c8e84c67a4bc8b603e697dbbd59dbc9b4f84825"
    integrity sha512-9u9fPtp6a52UMoZ/UCPrCjKZk8tnkI9To0Eh6yYnLKFEGkRZ7Chm6DqwDJbYJHeZrheCCopaD5oEOnRqhF4L2Q==
  
  react-native-web@^0.11.7:
    version "0.11.7"
    resolved "https://registry.yarnpkg.com/react-native-web/-/react-native-web-0.11.7.tgz#d173d5a9b58db23b6d442c4bc4c81e9939adac23"
    integrity sha512-w1KAxX2FYLS2GAi3w3BnEZg/IUu7FdgHnLmFKHplRnHMV3u1OPB2EVA7ndNdfu7ds4Rn2OZjSXoNh6F61g3gkA==
    dependencies:
      array-find-index "^1.0.2"
      create-react-class "^15.6.2"
      debounce "^1.2.0"
      deep-assign "^3.0.0"
      fbjs "^1.0.0"
      hyphenate-style-name "^1.0.2"
      inline-style-prefixer "^5.0.3"
      normalize-css-color "^1.0.2"
      prop-types "^15.6.0"
      react-timer-mixin "^0.13.4"
  
  "react-native@https://github.com/expo/react-native/archive/sdk-37.0.1.tar.gz":
    version "0.61.4"
    resolved "https://github.com/expo/react-native/archive/sdk-37.0.1.tar.gz#69f3f63c36c9df52611847a67c9d94596c1754cc"
    dependencies:
      "@babel/runtime" "^7.0.0"
      "@react-native-community/cli" "^3.0.0-alpha.1"
      "@react-native-community/cli-platform-android" "^3.0.0-alpha.1"
      "@react-native-community/cli-platform-ios" "^3.0.0-alpha.1"
      abort-controller "^3.0.0"
      art "^0.10.0"
      base64-js "^1.1.2"
      connect "^3.6.5"
      create-react-class "^15.6.3"
      escape-string-regexp "^1.0.5"
      event-target-shim "^5.0.1"
      fbjs "^1.0.0"
      fbjs-scripts "^1.1.0"
      hermes-engine "^0.2.1"
      invariant "^2.2.4"
      jsc-android "^245459.0.0"
      metro-babel-register "^0.56.0"
      metro-react-native-babel-transformer "^0.56.0"
      metro-source-map "^0.56.0"
      nullthrows "^1.1.0"
      pretty-format "^24.7.0"
      promise "^7.1.1"
      prop-types "^15.7.2"
      react-devtools-core "^3.6.3"
      react-refresh "^0.4.0"
      regenerator-runtime "^0.13.2"
      scheduler "0.15.0"
      stacktrace-parser "^0.1.3"
      whatwg-fetch "^3.0.0"
  
  react-navigation-hooks@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/react-navigation-hooks/-/react-navigation-hooks-1.1.0.tgz#337c41a50ebc7b9030bb9d9333cd4fd6e1f86b68"
    integrity sha512-ZY/aiYJ88KXaOo8iOa4171O/0x6ztGhUPd2OYzdaJhLT/tP64zi5HB/RZFImuKhaBTODXjoSpFaOTA5xpePG4g==
  
  react-navigation-stack@1.9.3:
    version "1.9.3"
    resolved "https://registry.yarnpkg.com/react-navigation-stack/-/react-navigation-stack-1.9.3.tgz#956a41796878d82c4db506075afea5e3cef64120"
    integrity sha512-MwQ55tfTJ5LhSdCHc8EMAH3bVinJ1hTLYhyxx7KgnReUl4BZ9s/Qn0fKC95BgSefAeunEphQPLHNNBygDN6uKw==
    dependencies:
      prop-types "^15.7.2"
  
  react-navigation-tabs@^2.7.0:
    version "2.7.0"
    resolved "https://registry.yarnpkg.com/react-navigation-tabs/-/react-navigation-tabs-2.7.0.tgz#68505910164a51c44710f835def8fe29360f346a"
    integrity sha512-fQEHjFrIBi+89loLL521HjGS0Z9tl4Y89SSamRkDl8Of6bPS1Ep++an3ctl2j7c3veqBZBrj3H1qa7qjd3apBQ==
    dependencies:
      hoist-non-react-statics "^3.3.0"
      react-lifecycles-compat "^3.0.4"
      react-native-safe-area-view "^0.14.6"
      react-native-tab-view "^2.11.0"
  
  react-navigation@^4.0.10:
    version "4.0.10"
    resolved "https://registry.yarnpkg.com/react-navigation/-/react-navigation-4.0.10.tgz#ddf41134600689d6ba99e35dd22ba1f664f91e5c"
    integrity sha512-7PqvmsdQ7HIyxPUMYbd9Uq//VoMdniEOLAOSvIhb/ExtbAt/1INSjUF+RiMWOMCWLTCNvNPRvTz7xy7qwWureg==
    dependencies:
      "@react-navigation/core" "^3.5.1"
      "@react-navigation/native" "^3.6.2"
  
  react-redux@^7.1.3:
    version "7.1.3"
    resolved "https://registry.yarnpkg.com/react-redux/-/react-redux-7.1.3.tgz#717a3d7bbe3a1b2d535c94885ce04cdc5a33fc79"
    integrity sha512-uI1wca+ECG9RoVkWQFF4jDMqmaw0/qnvaSvOoL/GA4dNxf6LoV8sUAcNDvE5NWKs4hFpn0t6wswNQnY3f7HT3w==
    dependencies:
      "@babel/runtime" "^7.5.5"
      hoist-non-react-statics "^3.3.0"
      invariant "^2.2.4"
      loose-envify "^1.4.0"
      prop-types "^15.7.2"
      react-is "^16.9.0"
  
  react-refresh@^0.4.0:
    version "0.4.2"
    resolved "https://registry.yarnpkg.com/react-refresh/-/react-refresh-0.4.2.tgz#54a277a6caaac2803d88f1d6f13c1dcfbd81e334"
    integrity sha512-kv5QlFFSZWo7OlJFNYbxRtY66JImuP2LcrFgyJfQaf85gSP+byzG21UbDQEYjU7f//ny8rwiEkO6py2Y+fEgAQ==
  
  react-timer-mixin@^0.13.4:
    version "0.13.4"
    resolved "https://registry.yarnpkg.com/react-timer-mixin/-/react-timer-mixin-0.13.4.tgz#75a00c3c94c13abe29b43d63b4c65a88fc8264d3"
    integrity sha512-4+ow23tp/Tv7hBM5Az5/Be/eKKF7DIvJ09voz5LyHGQaqqz9WV8YMs31eFvcYQs7d451LSg7kDJV70XYN/Ug/Q==
  
  react@16.9.0:
    version "16.9.0"
    resolved "https://registry.yarnpkg.com/react/-/react-16.9.0.tgz#40ba2f9af13bc1a38d75dbf2f4359a5185c4f7aa"
    integrity sha512-+7LQnFBwkiw+BobzOF6N//BdoNw0ouwmSJTEm9cglOOmsg/TMiFHZLe2sEoN5M7LgJTj9oHH0gxklfnQe66S1w==
    dependencies:
      loose-envify "^1.1.0"
      object-assign "^4.1.1"
      prop-types "^15.6.2"
  
  read-env@^1.3.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/read-env/-/read-env-1.3.0.tgz#e26e1e446992b3216e9a3c6f6ac51064fe91fdff"
    integrity sha512-DbCgZ8oHwZreK/E2E27RGk3EUPapMhYGSGIt02k9sX6R3tCFc4u4tkltKvkCvzEQ3SOLUaiYHAnGb+TdsnPp0A==
    dependencies:
      camelcase "5.0.0"
  
  read-pkg-up@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/read-pkg-up/-/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
    integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
    dependencies:
      find-up "^2.0.0"
      read-pkg "^2.0.0"
  
  read-pkg@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
    integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
    dependencies:
      load-json-file "^2.0.0"
      normalize-package-data "^2.3.2"
      path-type "^2.0.0"
  
  read-pkg@^5.2.0:
    version "5.2.0"
    resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
    integrity sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
    dependencies:
      "@types/normalize-package-data" "^2.4.0"
      normalize-package-data "^2.5.0"
      parse-json "^5.0.0"
      type-fest "^0.6.0"
  
  readable-stream@^2.0.1, readable-stream@^2.2.2, readable-stream@~2.3.6:
    version "2.3.7"
    resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
    integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.3"
      isarray "~1.0.0"
      process-nextick-args "~2.0.0"
      safe-buffer "~5.1.1"
      string_decoder "~1.1.1"
      util-deprecate "~1.0.1"
  
  redux-api-middleware@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/redux-api-middleware/-/redux-api-middleware-3.1.0.tgz#7cd304beffe1cb46f42147cc884544c3e15c33f1"
    integrity sha512-MaIUAsGAu07jnxkcFuUcZXWGC93cPTgAx9Jy/7gNU9b4O0MfWz+/3ryZQDOAALcSsBogspL9ZW3muK5m0KKNBw==
  
  redux-logger@^3.0.6:
    version "3.0.6"
    resolved "https://registry.yarnpkg.com/redux-logger/-/redux-logger-3.0.6.tgz#f7555966f3098f3c88604c449cf0baf5778274bf"
    integrity sha1-91VZZvMJjzyIYExEnPC69XeCdL8=
    dependencies:
      deep-diff "^0.3.5"
  
  redux-persist@^6.0.0:
    version "6.0.0"
    resolved "https://registry.yarnpkg.com/redux-persist/-/redux-persist-6.0.0.tgz#b4d2972f9859597c130d40d4b146fecdab51b3a8"
    integrity sha512-71LLMbUq2r02ng2We9S215LtPu3fY0KgaGE0k8WRgl6RkqxtGfl7HUozz1Dftwsb0D/5mZ8dwAaPbtnzfvbEwQ==
  
  redux-thunk@^2.3.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/redux-thunk/-/redux-thunk-2.3.0.tgz#51c2c19a185ed5187aaa9a2d08b666d0d6467622"
    integrity sha512-km6dclyFnmcvxhAcrQV2AkZmPQjzPDjgVlQtR0EQjxZPyJ0BnMf3in1ryuR8A2qU0HldVRfxYXbFSKlI3N7Slw==
  
  redux@^3.6.0:
    version "3.7.2"
    resolved "https://registry.yarnpkg.com/redux/-/redux-3.7.2.tgz#06b73123215901d25d065be342eb026bc1c8537b"
    integrity sha512-pNqnf9q1hI5HHZRBkj3bAngGZW/JMCmexDlOxw4XagXY2o1327nHH54LoTjiPJ0gizoqPDRqWyX/00g0hD6w+A==
    dependencies:
      lodash "^4.2.1"
      lodash-es "^4.2.1"
      loose-envify "^1.1.0"
      symbol-observable "^1.0.3"
  
  redux@^4.0.0, redux@^4.0.1, redux@^4.0.5:
    version "4.0.5"
    resolved "https://registry.yarnpkg.com/redux/-/redux-4.0.5.tgz#4db5de5816e17891de8a80c424232d06f051d93f"
    integrity sha512-VSz1uMAH24DM6MF72vcojpYPtrTUu3ByVWfPL1nPfVRb5mZVTve5GnNCUV53QM/BZ66xfWrm0CTWoM+Xlz8V1w==
    dependencies:
      loose-envify "^1.4.0"
      symbol-observable "^1.2.0"
  
  regenerate-unicode-properties@^8.1.0:
    version "8.1.0"
    resolved "https://registry.yarnpkg.com/regenerate-unicode-properties/-/regenerate-unicode-properties-8.1.0.tgz#ef51e0f0ea4ad424b77bf7cb41f3e015c70a3f0e"
    integrity sha512-LGZzkgtLY79GeXLm8Dp0BVLdQlWICzBnJz/ipWUgo59qBaZ+BHtq51P2q1uVZlppMuUAT37SDk39qUbjTWB7bA==
    dependencies:
      regenerate "^1.4.0"
  
  regenerate@^1.4.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/regenerate/-/regenerate-1.4.0.tgz#4a856ec4b56e4077c557589cae85e7a4c8869a11"
    integrity sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg==
  
  regenerator-runtime@^0.13.2:
    version "0.13.3"
    resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.13.3.tgz#7cf6a77d8f5c6f60eb73c5fc1955b2ceb01e6bf5"
    integrity sha512-naKIZz2GQ8JWh///G7L3X6LaQUAMp2lvb1rvwwsURe/VXwD6VMfr+/1NuNw3ag8v2kY1aQ/go5SNn79O9JU7yw==
  
  regenerator-transform@^0.14.0:
    version "0.14.1"
    resolved "https://registry.yarnpkg.com/regenerator-transform/-/regenerator-transform-0.14.1.tgz#3b2fce4e1ab7732c08f665dfdb314749c7ddd2fb"
    integrity sha512-flVuee02C3FKRISbxhXl9mGzdbWUVHubl1SMaknjxkFB1/iqpJhArQUvRxOOPEc/9tAiX0BaQ28FJH10E4isSQ==
    dependencies:
      private "^0.1.6"
  
  regex-not@^1.0.0, regex-not@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/regex-not/-/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
    integrity sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==
    dependencies:
      extend-shallow "^3.0.2"
      safe-regex "^1.1.0"
  
  regexpp@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/regexpp/-/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
    integrity sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==
  
  regexpp@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/regexpp/-/regexpp-3.0.0.tgz#dd63982ee3300e67b41c1956f850aa680d9d330e"
    integrity sha512-Z+hNr7RAVWxznLPuA7DIh8UNX1j9CDrUQxskw9IrBE1Dxue2lyXT+shqEIeLUjrokxIP8CMy1WkjgG3rTsd5/g==
  
  regexpu-core@^4.6.0:
    version "4.6.0"
    resolved "https://registry.yarnpkg.com/regexpu-core/-/regexpu-core-4.6.0.tgz#2037c18b327cfce8a6fea2a4ec441f2432afb8b6"
    integrity sha512-YlVaefl8P5BnFYOITTNzDvan1ulLOiXJzCNZxduTIosN17b87h3bvG9yHMoHaRuo88H4mQ06Aodj5VtYGGGiTg==
    dependencies:
      regenerate "^1.4.0"
      regenerate-unicode-properties "^8.1.0"
      regjsgen "^0.5.0"
      regjsparser "^0.6.0"
      unicode-match-property-ecmascript "^1.0.4"
      unicode-match-property-value-ecmascript "^1.1.0"
  
  regjsgen@^0.5.0:
    version "0.5.1"
    resolved "https://registry.yarnpkg.com/regjsgen/-/regjsgen-0.5.1.tgz#48f0bf1a5ea205196929c0d9798b42d1ed98443c"
    integrity sha512-5qxzGZjDs9w4tzT3TPhCJqWdCc3RLYwy9J2NB0nm5Lz+S273lvWcpjaTGHsT1dc6Hhfq41uSEOw8wBmxrKOuyg==
  
  regjsparser@^0.6.0:
    version "0.6.2"
    resolved "https://registry.yarnpkg.com/regjsparser/-/regjsparser-0.6.2.tgz#fd62c753991467d9d1ffe0a9f67f27a529024b96"
    integrity sha512-E9ghzUtoLwDekPT0DYCp+c4h+bvuUpe6rRHCTYn6eGoqj1LgKXxT6I0Il4WbjhQkOghzi/V+y03bPKvbllL93Q==
    dependencies:
      jsesc "~0.5.0"
  
  remove-trailing-separator@^1.0.1:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
    integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=
  
  repeat-element@^1.1.2:
    version "1.1.3"
    resolved "https://registry.yarnpkg.com/repeat-element/-/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
    integrity sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g==
  
  repeat-string@^1.6.1:
    version "1.6.1"
    resolved "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
    integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=
  
  require-directory@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
    integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=
  
  require-main-filename@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"
    integrity sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=
  
  requires-port@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
    integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=
  
  reselect@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/reselect/-/reselect-3.0.1.tgz#efdaa98ea7451324d092b2b2163a6a1d7a9a2147"
    integrity sha1-79qpjqdFEyTQkrKyFjpqHXqaIUc=
  
  reselect@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/reselect/-/reselect-4.0.0.tgz#f2529830e5d3d0e021408b246a206ef4ea4437f7"
    integrity sha512-qUgANli03jjAyGlnbYVAV5vvnOmJnODyABz51RdBN7M4WaVu8mecZWgyQNkG8Yqe3KRGRt0l4K4B3XVEULC4CA==
  
  resolve-from@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
    integrity sha1-six699nWiBvItuZTM17rywoYh0g=
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
    integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
  
  resolve-url@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/resolve-url/-/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
    integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=
  
  resolve@^1.10.0, resolve@^1.12.0, resolve@^1.13.1, resolve@^1.3.2, resolve@^1.4.0, resolve@^1.5.0, resolve@^1.8.1:
    version "1.14.1"
    resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.14.1.tgz#9e018c540fcf0c427d678b9931cbf45e984bcaff"
    integrity sha512-fn5Wobh4cxbLzuHaE+nphztHy43/b++4M6SsGFC2gB8uYwf0C8LcarfCz1un7UTW8OFQg9iNjZ4xpcFVGebDPg==
    dependencies:
      path-parse "^1.0.6"
  
  restore-cursor@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
    integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
    dependencies:
      onetime "^2.0.0"
      signal-exit "^3.0.2"
  
  restore-cursor@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
    integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
    dependencies:
      onetime "^5.1.0"
      signal-exit "^3.0.2"
  
  ret@~0.1.10:
    version "0.1.15"
    resolved "https://registry.yarnpkg.com/ret/-/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
    integrity sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==
  
  rimraf@2.6.3:
    version "2.6.3"
    resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
    integrity sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==
    dependencies:
      glob "^7.1.3"
  
  rimraf@^2.5.4, rimraf@^2.6.1:
    version "2.7.1"
    resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
    integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
    dependencies:
      glob "^7.1.3"
  
  rimraf@~2.2.6:
    version "2.2.8"
    resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.2.8.tgz#e439be2aaee327321952730f99a8929e4fc50582"
    integrity sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI=
  
  rsvp@^4.8.4:
    version "4.8.5"
    resolved "https://registry.yarnpkg.com/rsvp/-/rsvp-4.8.5.tgz#c8f155311d167f68f21e168df71ec5b083113734"
    integrity sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA==
  
  rtl-detect@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/rtl-detect/-/rtl-detect-1.0.2.tgz#8eca316f5c6563d54df4e406171dd7819adda67f"
    integrity sha512-5X1422hvphzg2a/bo4tIDbjFjbJUOaPZwqE6dnyyxqwFqfR+tBcvfqapJr0o0VygATVCGKiODEewhZtKF+90AA==
  
  run-async@^2.2.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/run-async/-/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
    integrity sha1-A3GrSuC91yDUFm19/aZP96RFpsA=
    dependencies:
      is-promise "^2.1.0"
  
  run-node@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/run-node/-/run-node-1.0.0.tgz#46b50b946a2aa2d4947ae1d886e9856fd9cabe5e"
    integrity sha512-kc120TBlQ3mih1LSzdAJXo4xn/GWS2ec0l3S+syHDXP9uRr0JAT8Qd3mdMuyjqCzeZktgP3try92cEgf9Nks8A==
  
  rx-lite-aggregates@^4.0.8:
    version "4.0.8"
    resolved "https://registry.yarnpkg.com/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz#753b87a89a11c95467c4ac1626c4efc4e05c67be"
    integrity sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=
    dependencies:
      rx-lite "*"
  
  rx-lite@*, rx-lite@^4.0.8:
    version "4.0.8"
    resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-4.0.8.tgz#0b1e11af8bc44836f04a6407e92da42467b79444"
    integrity sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=
  
  rxjs@^5.4.3:
    version "5.5.12"
    resolved "https://registry.yarnpkg.com/rxjs/-/rxjs-5.5.12.tgz#6fa61b8a77c3d793dbaf270bee2f43f652d741cc"
    integrity sha512-xx2itnL5sBbqeeiVgNPVuQQ1nC8Jp2WfNJhXWHmElW9YmrpS9UVnNzhP3EH3HFqexO5Tlp8GhYY+WEcqcVMvGw==
    dependencies:
      symbol-observable "1.0.1"
  
  rxjs@^6.4.0:
    version "6.5.5"
    resolved "https://registry.yarnpkg.com/rxjs/-/rxjs-6.5.5.tgz#c5c884e3094c8cfee31bf27eb87e54ccfc87f9ec"
    integrity sha512-WfQI+1gohdf0Dai/Bbmk5L5ItH5tYqm3ki2c5GdWhKjalzjg93N3avFjVStyZZz+A2Em+ZxKH5bNghw9UeylGQ==
    dependencies:
      tslib "^1.9.0"
  
  rxjs@^6.5.3:
    version "6.5.4"
    resolved "https://registry.yarnpkg.com/rxjs/-/rxjs-6.5.4.tgz#e0777fe0d184cec7872df147f303572d414e211c"
    integrity sha512-naMQXcgEo3csAEGvw/NydRA0fuS2nDZJiw1YUWFKU7aPPAPGZEsD4Iimit96qwCieH6y614MCLYwdkrWx7z/7Q==
    dependencies:
      tslib "^1.9.0"
  
  safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
    version "5.1.2"
    resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
    integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==
  
  safe-regex@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/safe-regex/-/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
    integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
    dependencies:
      ret "~0.1.10"
  
  "safer-buffer@>= 2.1.2 < 3":
    version "2.1.2"
    resolved "https://registry.yarnpkg.com/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
    integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==
  
  sane@^4.0.3:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/sane/-/sane-4.1.0.tgz#ed881fd922733a6c461bc189dc2b6c006f3ffded"
    integrity sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA==
    dependencies:
      "@cnakazawa/watch" "^1.0.3"
      anymatch "^2.0.0"
      capture-exit "^2.0.0"
      exec-sh "^0.3.2"
      execa "^1.0.0"
      fb-watchman "^2.0.0"
      micromatch "^3.1.4"
      minimist "^1.1.1"
      walker "~1.0.5"
  
  sax@^1.2.1, sax@~1.2.4:
    version "1.2.4"
    resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
    integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==
  
  scheduler@0.15.0, scheduler@^0.15.0:
    version "0.15.0"
    resolved "https://registry.yarnpkg.com/scheduler/-/scheduler-0.15.0.tgz#6bfcf80ff850b280fed4aeecc6513bc0b4f17f8e"
    integrity sha512-xAefmSfN6jqAa7Kuq7LIJY0bwAPG3xlCj0HMEBQk1lxYiDKZscY2xJ5U/61ZTrYbmNQbXa+gc7czPkVo11tnCg==
    dependencies:
      loose-envify "^1.1.0"
      object-assign "^4.1.1"
  
  semver-compare@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/semver-compare/-/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
    integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=
  
  "semver@2 || 3 || 4 || 5", semver@^5.1.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
    version "5.7.1"
    resolved "https://registry.yarnpkg.com/semver/-/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
    integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==
  
  semver@5.5.0:
    version "5.5.0"
    resolved "https://registry.yarnpkg.com/semver/-/semver-5.5.0.tgz#dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab"
    integrity sha512-4SJ3dm0WAwWy/NVeioZh5AntkdJoWKxHxcmyP622fOkgHa4z3R0TdBJICINyaSDE6uNwVc8gZr+ZinwZAH4xIA==
  
  semver@7.0.0:
    version "7.0.0"
    resolved "https://registry.yarnpkg.com/semver/-/semver-7.0.0.tgz#5f3ca35761e47e05b206c6daff2cf814f0316b8e"
    integrity sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==
  
  semver@^6.1.2, semver@^6.3.0:
    version "6.3.0"
    resolved "https://registry.yarnpkg.com/semver/-/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
    integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==
  
  send@0.17.1:
    version "0.17.1"
    resolved "https://registry.yarnpkg.com/send/-/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
    integrity sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg==
    dependencies:
      debug "2.6.9"
      depd "~1.1.2"
      destroy "~1.0.4"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      fresh "0.5.2"
      http-errors "~1.7.2"
      mime "1.6.0"
      ms "2.1.1"
      on-finished "~2.3.0"
      range-parser "~1.2.1"
      statuses "~1.5.0"
  
  sentry-expo@^2.0.3:
    version "2.0.3"
    resolved "https://registry.yarnpkg.com/sentry-expo/-/sentry-expo-2.0.3.tgz#cea1794bdbb873bd72a0228e7a7971fd21c03fb3"
    integrity sha512-9yi3W9wPyrpjgAUaFYi86he9iBtimTMSxgXsSDWDbTuMUmh/xRIoOvoUrf3L4Xb4zqbKFC3+mZWxLyfuc+hLpw==
    dependencies:
      "@expo/spawn-async" "^1.2.8"
      "@sentry/integrations" "^5.5.0"
      "@sentry/react-native" "^1.0.0"
      expo-constants "*"
      mkdirp "^1.0.3"
      rimraf "^2.6.1"
  
  serialize-error@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/serialize-error/-/serialize-error-2.1.0.tgz#50b679d5635cdf84667bdc8e59af4e5b81d5f60a"
    integrity sha1-ULZ51WNc34Rme9yOWa9OW4HV9go=
  
  serve-static@^1.13.1:
    version "1.14.1"
    resolved "https://registry.yarnpkg.com/serve-static/-/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
    integrity sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==
    dependencies:
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      parseurl "~1.3.3"
      send "0.17.1"
  
  set-blocking@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
    integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=
  
  set-value@^2.0.0, set-value@^2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/set-value/-/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
    integrity sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==
    dependencies:
      extend-shallow "^2.0.1"
      is-extendable "^0.1.1"
      is-plain-object "^2.0.3"
      split-string "^3.0.1"
  
  setimmediate@^1.0.5:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
    integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=
  
  setprototypeof@1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
    integrity sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw==
  
  shallow-clone@^0.1.2:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/shallow-clone/-/shallow-clone-0.1.2.tgz#5909e874ba77106d73ac414cfec1ffca87d97060"
    integrity sha1-WQnodLp3EG1zrEFM/sH/yofZcGA=
    dependencies:
      is-extendable "^0.1.1"
      kind-of "^2.0.1"
      lazy-cache "^0.2.3"
      mixin-object "^2.0.1"
  
  shebang-command@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
    integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
    dependencies:
      shebang-regex "^1.0.0"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
    integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
    integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
    integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
  
  shell-quote@1.6.1:
    version "1.6.1"
    resolved "https://registry.yarnpkg.com/shell-quote/-/shell-quote-1.6.1.tgz#f4781949cce402697127430ea3b3c5476f481767"
    integrity sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c=
    dependencies:
      array-filter "~0.0.0"
      array-map "~0.0.0"
      array-reduce "~0.0.0"
      jsonify "~0.0.0"
  
  shell-quote@^1.6.1:
    version "1.7.2"
    resolved "https://registry.yarnpkg.com/shell-quote/-/shell-quote-1.7.2.tgz#67a7d02c76c9da24f99d20808fcaded0e0e04be2"
    integrity sha512-mRz/m/JVscCrkMyPqHc/bczi3OQHkLTqXHEFu0zDhK/qfv3UcOA4SVmRCLmos4bhjr9ekVQubj/R7waKapmiQg==
  
  shellwords@^0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/shellwords/-/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"
    integrity sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==
  
  signal-exit@^3.0.0, signal-exit@^3.0.2:
    version "3.0.2"
    resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"
    integrity sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=
  
  simple-plist@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/simple-plist/-/simple-plist-1.1.0.tgz#8354ab63eb3922a054c78ce96c209c532e907a23"
    integrity sha512-2i5Tc0BYAqppM7jVzmNrI+aEUntPolIq4fDgji6WuNNn1D/qYdn2KwoLhZdzQkE04lu9L5tUoeJsjuJAvd+lFg==
    dependencies:
      bplist-creator "0.0.8"
      bplist-parser "0.2.0"
      plist "^3.0.1"
  
  simple-swizzle@^0.2.2:
    version "0.2.2"
    resolved "https://registry.yarnpkg.com/simple-swizzle/-/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
    integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
    dependencies:
      is-arrayish "^0.3.1"
  
  slash@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/slash/-/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
    integrity sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==
  
  slash@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
    integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==
  
  slice-ansi@^2.0.0, slice-ansi@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/slice-ansi/-/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
    integrity sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==
    dependencies:
      ansi-styles "^3.2.0"
      astral-regex "^1.0.0"
      is-fullwidth-code-point "^2.0.0"
  
  slide@^1.1.5:
    version "1.1.6"
    resolved "https://registry.yarnpkg.com/slide/-/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"
    integrity sha1-VusCfWW00tzmyy4tMsTUr8nh1wc=
  
  snapdragon-node@^2.0.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
    integrity sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==
    dependencies:
      define-property "^1.0.0"
      isobject "^3.0.0"
      snapdragon-util "^3.0.1"
  
  snapdragon-util@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
    integrity sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==
    dependencies:
      kind-of "^3.2.0"
  
  snapdragon@^0.8.1:
    version "0.8.2"
    resolved "https://registry.yarnpkg.com/snapdragon/-/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
    integrity sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==
    dependencies:
      base "^0.11.1"
      debug "^2.2.0"
      define-property "^0.2.5"
      extend-shallow "^2.0.1"
      map-cache "^0.2.2"
      source-map "^0.5.6"
      source-map-resolve "^0.5.0"
      use "^3.1.0"
  
  source-map-resolve@^0.5.0:
    version "0.5.3"
    resolved "https://registry.yarnpkg.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
    integrity sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==
    dependencies:
      atob "^2.1.2"
      decode-uri-component "^0.2.0"
      resolve-url "^0.2.1"
      source-map-url "^0.4.0"
      urix "^0.1.0"
  
  source-map-support@^0.5.16:
    version "0.5.16"
    resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.5.16.tgz#0ae069e7fe3ba7538c64c98515e35339eac5a042"
    integrity sha512-efyLRJDr68D9hBBNIPWFjhpFzURh+KJykQwvMyW5UiZzYwoF6l4YMMDIJJEyFWxWCqfyxLzz6tSfUFR+kXXsVQ==
    dependencies:
      buffer-from "^1.0.0"
      source-map "^0.6.0"
  
  source-map-url@^0.4.0:
    version "0.4.0"
    resolved "https://registry.yarnpkg.com/source-map-url/-/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
    integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=
  
  source-map@^0.5.0, source-map@^0.5.6:
    version "0.5.7"
    resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
    integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=
  
  source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
    version "0.6.1"
    resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
    integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==
  
  spdx-correct@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/spdx-correct/-/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
    integrity sha512-lr2EZCctC2BNR7j7WzJ2FpDznxky1sjfxvvYEyzxNyb6lZXHODmEoJeFu4JupYlkfha1KZpJyoqiJ7pgA1qq8Q==
    dependencies:
      spdx-expression-parse "^3.0.0"
      spdx-license-ids "^3.0.0"
  
  spdx-exceptions@^2.1.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/spdx-exceptions/-/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"
    integrity sha512-2XQACfElKi9SlVb1CYadKDXvoajPgBVPn/gOQLrTvHdElaVhr7ZEbqJaRnJLVNeaI4cMEAgVCeBMKF6MWRDCRA==
  
  spdx-expression-parse@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
    integrity sha512-Yg6D3XpRD4kkOmTpdgbUiEJFKghJH03fiC1OPll5h/0sO6neh2jqRDVHOQ4o/LMea0tgCkbMgea5ip/e+MkWyg==
    dependencies:
      spdx-exceptions "^2.1.0"
      spdx-license-ids "^3.0.0"
  
  spdx-license-ids@^3.0.0:
    version "3.0.5"
    resolved "https://registry.yarnpkg.com/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
    integrity sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q==
  
  split-on-first@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/split-on-first/-/split-on-first-1.1.0.tgz#f610afeee3b12bce1d0c30425e76398b78249a5f"
    integrity sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==
  
  split-string@^3.0.1, split-string@^3.0.2:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/split-string/-/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
    integrity sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==
    dependencies:
      extend-shallow "^3.0.0"
  
  sprintf-js@~1.0.2:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
    integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=
  
  stable@^0.1.8:
    version "0.1.8"
    resolved "https://registry.yarnpkg.com/stable/-/stable-0.1.8.tgz#836eb3c8382fe2936feaf544631017ce7d47a3cf"
    integrity sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==
  
  stack-utils@^1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/stack-utils/-/stack-utils-1.0.2.tgz#33eba3897788558bebfc2db059dc158ec36cebb8"
    integrity sha512-MTX+MeG5U994cazkjd/9KNAapsHnibjMLnfXodlkXw76JEea0UiNzrqidzo1emMwk7w5Qhc9jd4Bn9TBb1MFwA==
  
  stacktrace-parser@^0.1.3:
    version "0.1.8"
    resolved "https://registry.yarnpkg.com/stacktrace-parser/-/stacktrace-parser-0.1.8.tgz#28b0272bd9aeb41636f0c8265c03ba270c865e1b"
    integrity sha512-ig5rHJSdJrAsVqdb3oAI/8C6aQ7dEwJXoy/TIEIOTzdJHssmn12o6RsFoeQSLHoKjq0lX+kqhmnLDpyQTuWiJA==
    dependencies:
      type-fest "^0.7.1"
  
  static-extend@^0.1.1:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/static-extend/-/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
    integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
    dependencies:
      define-property "^0.2.5"
      object-copy "^0.1.0"
  
  "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
    version "1.5.0"
    resolved "https://registry.yarnpkg.com/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
    integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=
  
  stream-buffers@~2.2.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/stream-buffers/-/stream-buffers-2.2.0.tgz#91d5f5130d1cef96dcfa7f726945188741d09ee4"
    integrity sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ=
  
  strict-uri-encode@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz#b9c7330c7042862f6b142dc274bbcc5866ce3546"
    integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=
  
  string-width@^1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
    integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
    dependencies:
      code-point-at "^1.0.0"
      is-fullwidth-code-point "^1.0.0"
      strip-ansi "^3.0.0"
  
  string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
    integrity sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==
    dependencies:
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^4.0.0"
  
  string-width@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/string-width/-/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
    integrity sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==
    dependencies:
      emoji-regex "^7.0.1"
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^5.1.0"
  
  string-width@^4.1.0:
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/string-width/-/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
    integrity sha512-zUz5JD+tgqtuDjMhwIg5uFVV3dtqZ9yQJlZVfq4I01/K5Paj5UHj7VyrQOJvzawSVlKpObApbfD0Ed6yJc+1eg==
    dependencies:
      emoji-regex "^8.0.0"
      is-fullwidth-code-point "^3.0.0"
      strip-ansi "^6.0.0"
  
  string.prototype.trimleft@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/string.prototype.trimleft/-/string.prototype.trimleft-2.1.1.tgz#9bdb8ac6abd6d602b17a4ed321870d2f8dcefc74"
    integrity sha512-iu2AGd3PuP5Rp7x2kEZCrB2Nf41ehzh+goo8TV7z8/XDBbsvc6HQIlUl9RjkZ4oyrW1XM5UwlGl1oVEaDjg6Ag==
    dependencies:
      define-properties "^1.1.3"
      function-bind "^1.1.1"
  
  string.prototype.trimright@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/string.prototype.trimright/-/string.prototype.trimright-2.1.1.tgz#440314b15996c866ce8a0341894d45186200c5d9"
    integrity sha512-qFvWL3/+QIgZXVmJBfpHmxLB7xsUXz6HsUmP8+5dRaC3Q7oKUv9Vo6aMCRZC1smrtyECFsIT30PqBJ1gTjAs+g==
    dependencies:
      define-properties "^1.1.3"
      function-bind "^1.1.1"
  
  string_decoder@~1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
    integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
    dependencies:
      safe-buffer "~5.1.0"
  
  strip-ansi@^3.0.0, strip-ansi@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
    integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
    dependencies:
      ansi-regex "^2.0.0"
  
  strip-ansi@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
    integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
    dependencies:
      ansi-regex "^3.0.0"
  
  strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
    version "5.2.0"
    resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
    integrity sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==
    dependencies:
      ansi-regex "^4.1.0"
  
  strip-ansi@^6.0.0:
    version "6.0.0"
    resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
    integrity sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==
    dependencies:
      ansi-regex "^5.0.0"
  
  strip-bom@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
    integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=
  
  strip-eof@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
    integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=
  
  strip-final-newline@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
    integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==
  
  strip-json-comments@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-3.0.1.tgz#85713975a91fb87bf1b305cca77395e40d2a64a7"
    integrity sha512-VTyMAUfdm047mwKl+u79WIdrZxtFtn+nBxHeb844XBQ9uMNTuTHdx2hc5RiAJYqwTj3wc/xe5HLSdJSkJ+WfZw==
  
  sudo-prompt@^9.0.0:
    version "9.1.1"
    resolved "https://registry.yarnpkg.com/sudo-prompt/-/sudo-prompt-9.1.1.tgz#73853d729770392caec029e2470db9c221754db0"
    integrity sha512-es33J1g2HjMpyAhz8lOR+ICmXXAqTuKbuXuUWLhOLew20oN9oUCgCJx615U/v7aioZg7IX5lIh9x34vwneu4pA==
  
  supports-color@^5.3.0:
    version "5.5.0"
    resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
    integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^6.1.0:
    version "6.1.0"
    resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
    integrity sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==
    dependencies:
      has-flag "^3.0.0"
  
  svg-arc-to-cubic-bezier@^3.0.0:
    version "3.2.0"
    resolved "https://registry.yarnpkg.com/svg-arc-to-cubic-bezier/-/svg-arc-to-cubic-bezier-3.2.0.tgz#390c450035ae1c4a0104d90650304c3bc814abe6"
    integrity sha512-djbJ/vZKZO+gPoSDThGNpKDO+o+bAeA4XQKovvkNCqnIS2t+S4qnLAGQhyyrulhCFRl1WWzAp0wUDV8PpTVU3g==
  
  svg-parser@^2.0.0:
    version "2.0.4"
    resolved "https://registry.yarnpkg.com/svg-parser/-/svg-parser-2.0.4.tgz#fdc2e29e13951736140b76cb122c8ee6630eb6b5"
    integrity sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==
  
  svgo@^1.2.2:
    version "1.3.2"
    resolved "https://registry.yarnpkg.com/svgo/-/svgo-1.3.2.tgz#b6dc511c063346c9e415b81e43401145b96d4167"
    integrity sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==
    dependencies:
      chalk "^2.4.1"
      coa "^2.0.2"
      css-select "^2.0.0"
      css-select-base-adapter "^0.1.1"
      css-tree "1.0.0-alpha.37"
      csso "^4.0.2"
      js-yaml "^3.13.1"
      mkdirp "~0.5.1"
      object.values "^1.1.0"
      sax "~1.2.4"
      stable "^0.1.8"
      unquote "~1.1.1"
      util.promisify "~1.0.0"
  
  symbol-observable@1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/symbol-observable/-/symbol-observable-1.0.1.tgz#8340fc4702c3122df5d22288f88283f513d3fdd4"
    integrity sha1-g0D8RwLDEi310iKI+IKD9RPT/dQ=
  
  symbol-observable@^1.0.3, symbol-observable@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/symbol-observable/-/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
    integrity sha512-e900nM8RRtGhlV36KGEU9k65K3mPb1WV70OdjfxlG2EAuM1noi/E/BaW/uMhL7bPEssK8QV57vN3esixjUvcXQ==
  
  table@^5.2.3:
    version "5.4.6"
    resolved "https://registry.yarnpkg.com/table/-/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
    integrity sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==
    dependencies:
      ajv "^6.10.2"
      lodash "^4.17.14"
      slice-ansi "^2.1.0"
      string-width "^3.0.0"
  
  temp@0.8.3:
    version "0.8.3"
    resolved "https://registry.yarnpkg.com/temp/-/temp-0.8.3.tgz#e0c6bc4d26b903124410e4fed81103014dfc1f59"
    integrity sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k=
    dependencies:
      os-tmpdir "^1.0.0"
      rimraf "~2.2.6"
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "https://registry.yarnpkg.com/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
    integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=
  
  throat@^4.1.0:
    version "4.1.0"
    resolved "https://registry.yarnpkg.com/throat/-/throat-4.1.0.tgz#89037cbc92c56ab18926e6ba4cbb200e15672a6a"
    integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=
  
  through2@^2.0.0, through2@^2.0.1:
    version "2.0.5"
    resolved "https://registry.yarnpkg.com/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
    integrity sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==
    dependencies:
      readable-stream "~2.3.6"
      xtend "~4.0.1"
  
  through@^2.3.6:
    version "2.3.8"
    resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
    integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=
  
  time-stamp@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/time-stamp/-/time-stamp-1.1.0.tgz#764a5a11af50561921b133f3b44e618687e0f5c3"
    integrity sha1-dkpaEa9QVhkhsTPztE5hhofg9cM=
  
  tiny-queue@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/tiny-queue/-/tiny-queue-0.2.1.tgz#25a67f2c6e253b2ca941977b5ef7442ef97a6046"
    integrity sha1-JaZ/LG4lOyypQZd7XvdELvl6YEY=
  
  tinycolor2@^1.4.1:
    version "1.4.1"
    resolved "https://registry.yarnpkg.com/tinycolor2/-/tinycolor2-1.4.1.tgz#f4fad333447bc0b07d4dc8e9209d8f39a8ac77e8"
    integrity sha1-9PrTM0R7wLB9TcjpIJ2POaisd+g=
  
  tmp@^0.0.33:
    version "0.0.33"
    resolved "https://registry.yarnpkg.com/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
    integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
    dependencies:
      os-tmpdir "~1.0.2"
  
  tmpl@1.0.x:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/tmpl/-/tmpl-1.0.4.tgz#23640dd7b42d00433911140820e5cf440e521dd1"
    integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=
  
  to-fast-properties@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
    integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=
  
  to-object-path@^0.3.0:
    version "0.3.0"
    resolved "https://registry.yarnpkg.com/to-object-path/-/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
    integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
    dependencies:
      kind-of "^3.0.2"
  
  to-regex-range@^2.1.0:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
    integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
    dependencies:
      is-number "^3.0.0"
      repeat-string "^1.6.1"
  
  to-regex@^3.0.1, to-regex@^3.0.2:
    version "3.0.2"
    resolved "https://registry.yarnpkg.com/to-regex/-/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
    integrity sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==
    dependencies:
      define-property "^2.0.2"
      extend-shallow "^3.0.2"
      regex-not "^1.0.2"
      safe-regex "^1.1.0"
  
  toidentifier@1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/toidentifier/-/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
    integrity sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==
  
  tslib@^1.8.1, tslib@^1.9.0:
    version "1.10.0"
    resolved "https://registry.yarnpkg.com/tslib/-/tslib-1.10.0.tgz#c3c19f95973fb0a62973fb09d90d961ee43e5c8a"
    integrity sha512-qOebF53frne81cf0S9B41ByenJ3/IuH8yJKngAX35CmiZySA0khhkovshKK+jGCaMnVomla7gVlIcc3EvKPbTQ==
  
  tslib@^1.9.3:
    version "1.11.1"
    resolved "https://registry.yarnpkg.com/tslib/-/tslib-1.11.1.tgz#eb15d128827fbee2841549e171f45ed338ac7e35"
    integrity sha512-aZW88SY8kQbU7gpV19lN24LtXh/yD4ZZg6qieAJDDg+YBsJcSmLGK9QpnUjAKVG/xefmvJGd1WUmfpT/g6AJGA==
  
  tsutils@^3.17.1, tsutils@^3.7.0:
    version "3.17.1"
    resolved "https://registry.yarnpkg.com/tsutils/-/tsutils-3.17.1.tgz#ed719917f11ca0dee586272b2ac49e015a2dd759"
    integrity sha512-kzeQ5B8H3w60nFY2g8cJIuH7JDpsALXySGtwGJ0p2LSjLgay3NdIpqq5SoOBe46bKDW2iq25irHCr8wjomUS2g==
    dependencies:
      tslib "^1.8.1"
  
  type-check@~0.3.2:
    version "0.3.2"
    resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
    integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
    dependencies:
      prelude-ls "~1.1.2"
  
  type-fest@^0.6.0:
    version "0.6.0"
    resolved "https://registry.yarnpkg.com/type-fest/-/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
    integrity sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==
  
  type-fest@^0.7.1:
    version "0.7.1"
    resolved "https://registry.yarnpkg.com/type-fest/-/type-fest-0.7.1.tgz#8dda65feaf03ed78f0a3f9678f1869147f7c5c48"
    integrity sha512-Ne2YiiGN8bmrmJJEuTWTLJR32nh/JdL1+PSicowtNb0WFpn59GK8/lfD61bVtzguz7b3PBt74nxpv/Pw5po5Rg==
  
  type-fest@^0.8.1:
    version "0.8.1"
    resolved "https://registry.yarnpkg.com/type-fest/-/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
    integrity sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==
  
  typedarray-to-buffer@^3.1.2:
    version "3.1.5"
    resolved "https://registry.yarnpkg.com/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
    integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
    dependencies:
      is-typedarray "^1.0.0"
  
  typedarray@^0.0.6:
    version "0.0.6"
    resolved "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
    integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=
  
  typescript@^3.8.3:
    version "3.8.3"
    resolved "https://registry.yarnpkg.com/typescript/-/typescript-3.8.3.tgz#409eb8544ea0335711205869ec458ab109ee1061"
    integrity sha512-MYlEfn5VrLNsgudQTVJeNaQFUAI7DkhnOjdpAp4T+ku1TfQClewlbSuTVHiA+8skNBgaf02TL/kLOvig4y3G8w==
  
  ua-parser-js@^0.7.18:
    version "0.7.21"
    resolved "https://registry.yarnpkg.com/ua-parser-js/-/ua-parser-js-0.7.21.tgz#853cf9ce93f642f67174273cc34565ae6f308777"
    integrity sha512-+O8/qh/Qj8CgC6eYBVBykMrNtp5Gebn4dlGD/kKXVkJNDwyrAwSIqwz8CDf+tsAIWVycKcku6gIXJ0qwx/ZXaQ==
  
  uglify-es@^3.1.9:
    version "3.3.9"
    resolved "https://registry.yarnpkg.com/uglify-es/-/uglify-es-3.3.9.tgz#0c1c4f0700bed8dbc124cdb304d2592ca203e677"
    integrity sha512-r+MU0rfv4L/0eeW3xZrd16t4NZfK8Ld4SWVglYBb7ez5uXFWHuVRs6xCTrf1yirs9a4j4Y27nn7SRfO6v67XsQ==
    dependencies:
      commander "~2.13.0"
      source-map "~0.6.1"
  
  ultron@1.0.x:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/ultron/-/ultron-1.0.2.tgz#ace116ab557cd197386a4e88f4685378c8b2e4fa"
    integrity sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po=
  
  ultron@~1.1.0:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/ultron/-/ultron-1.1.1.tgz#9fe1536a10a664a65266a1e3ccf85fd36302bc9c"
    integrity sha512-UIEXBNeYmKptWH6z8ZnqTeS8fV74zG0/eRU9VGkpzz+LIJNs8W/zM/L+7ctCkRrgbNnnR0xxw4bKOr0cW0N0Og==
  
  unicode-canonical-property-names-ecmascript@^1.0.4:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz#2619800c4c825800efdd8343af7dd9933cbe2818"
    integrity sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ==
  
  unicode-match-property-ecmascript@^1.0.4:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz#8ed2a32569961bce9227d09cd3ffbb8fed5f020c"
    integrity sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg==
    dependencies:
      unicode-canonical-property-names-ecmascript "^1.0.4"
      unicode-property-aliases-ecmascript "^1.0.4"
  
  unicode-match-property-value-ecmascript@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.1.0.tgz#5b4b426e08d13a80365e0d657ac7a6c1ec46a277"
    integrity sha512-hDTHvaBk3RmFzvSl0UVrUmC3PuW9wKVnpoUDYH0JDkSIovzw+J5viQmeYHxVSBptubnr7PbH2e0fnpDRQnQl5g==
  
  unicode-property-aliases-ecmascript@^1.0.4:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.5.tgz#a9cc6cc7ce63a0a3023fc99e341b94431d405a57"
    integrity sha512-L5RAqCfXqAwR3RriF8pM0lU0w4Ryf/GgzONwi6KnL1taJQa7x1TCxdJnILX59WIGOwR57IVxn7Nej0fz1Ny6fw==
  
  unimodules-app-loader@~1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/unimodules-app-loader/-/unimodules-app-loader-1.0.1.tgz#4358195cfa4d6026c71c7365362acf047f252bb0"
    integrity sha512-GhPwdTp9WHRMRnM2ONQsHb0mciKU5tafohEX0+E4F7bSba89r+rJckWQnpDvHE7uyUaRtocLYcbk09vv2eg8ew==
  
  unimodules-barcode-scanner-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-barcode-scanner-interface/-/unimodules-barcode-scanner-interface-5.1.0.tgz#6d24322b6db556b21eca99a130673c7e07d86559"
    integrity sha512-FUau0mm4sBOGmlekltY0iAimJ438w3rtWiv6hcjE77Map527aCH3GyjnZSw78raVxe598EXhWHviuwRxOGINYg==
  
  unimodules-camera-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-camera-interface/-/unimodules-camera-interface-5.1.0.tgz#ea43a8d05b7b1a9053e6b2281b428a1e80853661"
    integrity sha512-uwBmZ3XS6vkdzRAmiDhUE/P7fafN7ufXoRuBDGoX/Q9kIiKg61D8HzTmhLMelvJFW6eCjoBJfh/zRyZ54qcjGg==
  
  unimodules-constants-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-constants-interface/-/unimodules-constants-interface-5.1.0.tgz#916a8203a887b53cdbcd80b63bc6fd56c85ccfd2"
    integrity sha512-TlrqwtKt2G0QH4Fn1ko3tRtLX+eUGSnCBuu1TiAGlsQ5FM/1+AGuJNftHdUwZY1DncIAlw6lhNW+amv0hw5ocg==
  
  unimodules-face-detector-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-face-detector-interface/-/unimodules-face-detector-interface-5.1.0.tgz#56b4e8c238d8b38f7937f2eb87212d5f87c463f9"
    integrity sha512-0qDA6j1WvPM98q32aKvRdFhgSa9Nu8lqNUlrgE740UTYsAmfQl8lM/r2TOuR1k3dVC14q33YvLizSOYM5FLhAw==
  
  unimodules-file-system-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-file-system-interface/-/unimodules-file-system-interface-5.1.0.tgz#adcba6d6dbb58d889175425dedcbb1501f498ab7"
    integrity sha512-G2QXhEXY3uHuDD50MWI7C/nesbVlf2C0QHTs+fAt1VpmWYWfdDaeqgO67f/QRz8FH8xm3ul9XvgP6nA+P0xfIg==
  
  unimodules-font-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-font-interface/-/unimodules-font-interface-5.1.0.tgz#953c1eb6e1f221f0c7d427d7aba78cce599b4b27"
    integrity sha512-ZKycNecNN0xxGIo9Db2n8RYU+ijlc+hzpE5acVSiIlmMjTsiOODRLkF++yKsZxglGXn/blgtBLrcTQr4jJV4MQ==
  
  unimodules-image-loader-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-image-loader-interface/-/unimodules-image-loader-interface-5.1.0.tgz#40eeecb1d9409b51595b559023230ce50485b626"
    integrity sha512-yU1OPWMtZ9QcW5CxLE1DYWrpJGZ1hRGdoFG3vyk4syUS8QsCPR0HXqcI6KlTpI6wcLA0+HtS+1CmgJCMCUDd4w==
  
  unimodules-permissions-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-permissions-interface/-/unimodules-permissions-interface-5.1.0.tgz#146062ee5cde1f00f34ba2692efab5f0c6f55d02"
    integrity sha512-3Mz9A4a+iYF57ZeE99nidRPNM7dX3dzTZRvRQyCP5+CvsEmGNlLTIbTQ7fxKECoe3I6cjw94gNSirxIbwb3lDg==
  
  unimodules-sensors-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-sensors-interface/-/unimodules-sensors-interface-5.1.0.tgz#2d8f5f15a8b00b3f0aab59c3ff474f39735d634f"
    integrity sha512-v8nRFRHtl4jFI1aiAmWurPKDuvboSxj0qoqpy/IB3xkkzBfw4KsZQ1b1yomwNbv9cCqIkFxaNAOzyrvVZrz/dA==
  
  unimodules-task-manager-interface@~5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/unimodules-task-manager-interface/-/unimodules-task-manager-interface-5.1.0.tgz#49fe4431464faa576ba3453a1824030debbf8d35"
    integrity sha512-t7FSWOdw4ev9SlqPzfw9rOKlFyryZbrcmjEr0n6HtPXqZ4NRfPqXtYSjoVWswGb3iGr3GPOIHZ/OQ6Z6StL1NA==
  
  union-value@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/union-value/-/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
    integrity sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==
    dependencies:
      arr-union "^3.1.0"
      get-value "^2.0.6"
      is-extendable "^0.1.1"
      set-value "^2.0.1"
  
  universalify@^0.1.0:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/universalify/-/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
    integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==
  
  unpipe@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
    integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=
  
  unquote@~1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/unquote/-/unquote-1.1.1.tgz#8fded7324ec6e88a0ff8b905e7c098cdc086d544"
    integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=
  
  unset-value@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/unset-value/-/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
    integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
    dependencies:
      has-value "^0.3.1"
      isobject "^3.0.0"
  
  uri-js@^4.2.2:
    version "4.2.2"
    resolved "https://registry.yarnpkg.com/uri-js/-/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
    integrity sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==
    dependencies:
      punycode "^2.1.0"
  
  urix@^0.1.0:
    version "0.1.0"
    resolved "https://registry.yarnpkg.com/urix/-/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
    integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=
  
  url-parse@^1.4.4:
    version "1.4.7"
    resolved "https://registry.yarnpkg.com/url-parse/-/url-parse-1.4.7.tgz#a8a83535e8c00a316e403a5db4ac1b9b853ae278"
    integrity sha512-d3uaVyzDB9tQoSXFvuSUNFibTd9zxd2bkVrDRvF5TmvWWQwqE4lgYJ5m+x1DbecWkw+LK4RNl2CU1hHuOKPVlg==
    dependencies:
      querystringify "^2.1.1"
      requires-port "^1.0.0"
  
  use-memo-one@^1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/use-memo-one/-/use-memo-one-1.1.1.tgz#39e6f08fe27e422a7d7b234b5f9056af313bd22c"
    integrity sha512-oFfsyun+bP7RX8X2AskHNTxu+R3QdE/RC5IefMbqptmACAA/gfol1KDD5KRzPsGMa62sWxGZw+Ui43u6x4ddoQ==
  
  use-subscription@^1.0.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/use-subscription/-/use-subscription-1.3.0.tgz#3df13a798e826c8d462899423293289a3362e4e6"
    integrity sha512-buZV7FUtnbOr+65dN7PHK7chHhQGfk/yjgqfpRLoWuHIAc4klAD/rdot2FsPNtFthN1ZydvA8tR/mWBMQ+/fDQ==
    dependencies:
      object-assign "^4.1.1"
  
  use@^3.1.0:
    version "3.1.1"
    resolved "https://registry.yarnpkg.com/use/-/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
    integrity sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==
  
  util-deprecate@~1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
    integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=
  
  util.promisify@~1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/util.promisify/-/util.promisify-1.0.1.tgz#6baf7774b80eeb0f7520d8b81d07982a59abbaee"
    integrity sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.2"
      has-symbols "^1.0.1"
      object.getownpropertydescriptors "^2.1.0"
  
  utils-merge@1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
    integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=
  
  uuid@^3.3.2:
    version "3.3.3"
    resolved "https://registry.yarnpkg.com/uuid/-/uuid-3.3.3.tgz#4568f0216e78760ee1dbf3a4d2cf53e224112866"
    integrity sha512-pW0No1RGHgzlpHJO1nsVrHKpOEIxkGg1xB+v0ZmdNH5OAeAwzAVrCnI2/6Mtx+Uys6iaylxa+D3g4j63IKKjSQ==
  
  uuid@^3.4.0:
    version "3.4.0"
    resolved "https://registry.yarnpkg.com/uuid/-/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
    integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==
  
  v8-compile-cache@^2.0.3:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/v8-compile-cache/-/v8-compile-cache-2.1.0.tgz#e14de37b31a6d194f5690d67efc4e7f6fc6ab30e"
    integrity sha512-usZBT3PW+LOjM25wbqIlZwPeJV+3OSz3M1k1Ws8snlW39dZyYL9lOGC5FgPVHfk0jKmjiDV8Z0mIbVQPiwFs7g==
  
  validate-npm-package-license@^3.0.1:
    version "3.0.4"
    resolved "https://registry.yarnpkg.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
    integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
    dependencies:
      spdx-correct "^3.0.0"
      spdx-expression-parse "^3.0.0"
  
  vary@~1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
    integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=
  
  vlq@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/vlq/-/vlq-1.0.1.tgz#c003f6e7c0b4c1edd623fd6ee50bbc0d6a1de468"
    integrity sha512-gQpnTgkubC6hQgdIcRdYGDSDc+SaujOdyesZQMv6JlfQee/9Mp0Qhnys6WxDWvQnL5WZdT7o2Ul187aSt0Rq+w==
  
  walker@^1.0.7, walker@~1.0.5:
    version "1.0.7"
    resolved "https://registry.yarnpkg.com/walker/-/walker-1.0.7.tgz#2f7f9b8fd10d677262b18a884e28d19618e028fb"
    integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
    dependencies:
      makeerror "1.0.x"
  
  wcwidth@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/wcwidth/-/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
    integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
    dependencies:
      defaults "^1.0.3"
  
  whatwg-fetch@>=0.10.0, whatwg-fetch@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/whatwg-fetch/-/whatwg-fetch-3.0.0.tgz#fc804e458cc460009b1a2b966bc8817d2578aefb"
    integrity sha512-9GSJUgz1D4MfyKU7KRqwOjXCXTqWdFNvEr7eUBYchQiVc744mqK/MzXPNR2WsPkmkOa4ywfg8C2n8h+13Bey1Q==
  
  which-module@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/which-module/-/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
    integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=
  
  which@^1.2.9, which@^1.3.0:
    version "1.3.1"
    resolved "https://registry.yarnpkg.com/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
    integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
    dependencies:
      isexe "^2.0.0"
  
  which@^2.0.1:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
    integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
    dependencies:
      isexe "^2.0.0"
  
  word-wrap@~1.2.3:
    version "1.2.3"
    resolved "https://registry.yarnpkg.com/word-wrap/-/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
    integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==
  
  wordwrap@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
    integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=
  
  wrap-ansi@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
    integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
    dependencies:
      string-width "^1.0.1"
      strip-ansi "^3.0.1"
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
    integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=
  
  write-file-atomic@^1.2.0:
    version "1.3.4"
    resolved "https://registry.yarnpkg.com/write-file-atomic/-/write-file-atomic-1.3.4.tgz#f807a4f0b1d9e913ae7a48112e6cc3af1991b45f"
    integrity sha1-+Aek8LHZ6ROuekgRLmzDrxmRtF8=
    dependencies:
      graceful-fs "^4.1.11"
      imurmurhash "^0.1.4"
      slide "^1.1.5"
  
  write@1.0.3:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/write/-/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
    integrity sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==
    dependencies:
      mkdirp "^0.5.1"
  
  ws@^1.1.0, ws@^1.1.5:
    version "1.1.5"
    resolved "https://registry.yarnpkg.com/ws/-/ws-1.1.5.tgz#cbd9e6e75e09fc5d2c90015f21f0c40875e0dd51"
    integrity sha512-o3KqipXNUdS7wpQzBHSe180lBGO60SoK0yVo3CYJgb2MkobuWuBX6dhkYP5ORCLd55y+SaflMOV5fqAB53ux4w==
    dependencies:
      options ">=0.0.5"
      ultron "1.0.x"
  
  ws@^3.3.1:
    version "3.3.3"
    resolved "https://registry.yarnpkg.com/ws/-/ws-3.3.3.tgz#f1cf84fe2d5e901ebce94efaece785f187a228f2"
    integrity sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==
    dependencies:
      async-limiter "~1.0.0"
      safe-buffer "~5.1.0"
      ultron "~1.1.0"
  
  xcode@2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/xcode/-/xcode-2.0.0.tgz#134f1f94c26fbfe8a9aaa9724bfb2772419da1a2"
    integrity sha512-5xF6RCjAdDEiEsbbZaS/gBRt3jZ/177otZcpoLCjGN/u1LrfgH7/Sgeeavpr/jELpyDqN2im3AKosl2G2W8hfw==
    dependencies:
      simple-plist "^1.0.0"
      uuid "^3.3.2"
  
  xcode@^2.0.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/xcode/-/xcode-2.1.0.tgz#bab64a7e954bb50ca8d19da7e09531c65a43ecfe"
    integrity sha512-uCrmPITrqTEzhn0TtT57fJaNaw8YJs1aCzs+P/QqxsDbvPZSv7XMPPwXrKvHtD6pLjBM/NaVwraWJm8q83Y4iQ==
    dependencies:
      simple-plist "^1.0.0"
      uuid "^3.3.2"
  
  xmlbuilder@^9.0.7:
    version "9.0.7"
    resolved "https://registry.yarnpkg.com/xmlbuilder/-/xmlbuilder-9.0.7.tgz#132ee63d2ec5565c557e20f4c22df9aca686b10d"
    integrity sha1-Ey7mPS7FVlxVfiD0wi35rKaGsQ0=
  
  xmldoc@^1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/xmldoc/-/xmldoc-1.1.2.tgz#6666e029fe25470d599cd30e23ff0d1ed50466d7"
    integrity sha512-ruPC/fyPNck2BD1dpz0AZZyrEwMOrWTO5lDdIXS91rs3wtm4j+T8Rp2o+zoOYkkAxJTZRPOSnOGei1egoRmKMQ==
    dependencies:
      sax "^1.2.1"
  
  xmldom@0.1.x:
    version "0.1.31"
    resolved "https://registry.yarnpkg.com/xmldom/-/xmldom-0.1.31.tgz#b76c9a1bd9f0a9737e5a72dc37231cf38375e2ff"
    integrity sha512-yS2uJflVQs6n+CyjHoaBmVSqIDevTAWrzMmjG1Gc7h1qQ7uVozNhEPJAwZXWyGQ/Gafo3fCwrcaokezLPupVyQ==
  
  xpipe@^1.0.5:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/xpipe/-/xpipe-1.0.5.tgz#8dd8bf45fc3f7f55f0e054b878f43a62614dafdf"
    integrity sha1-jdi/Rfw/f1Xw4FS4ePQ6YmFNr98=
  
  xtend@~4.0.1:
    version "4.0.2"
    resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
    integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==
  
  y18n@^3.2.1:
    version "3.2.1"
    resolved "https://registry.yarnpkg.com/y18n/-/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"
    integrity sha1-bRX7qITAhnnA136I53WegR4H+kE=
  
  "y18n@^3.2.1 || ^4.0.0":
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/y18n/-/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
    integrity sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w==
  
  yallist@^2.1.2:
    version "2.1.2"
    resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
    integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=
  
  yargs-parser@^11.1.1:
    version "11.1.1"
    resolved "https://registry.yarnpkg.com/yargs-parser/-/yargs-parser-11.1.1.tgz#879a0865973bca9f6bab5cbdf3b1c67ec7d3bcf4"
    integrity sha512-C6kB/WJDiaxONLJQnF8ccx9SEeoTTLek8RVbaOIsrAUS8VrBEXfmeSnCZxygc+XC2sNMBIwOOnfcxiynjHsVSQ==
    dependencies:
      camelcase "^5.0.0"
      decamelize "^1.2.0"
  
  yargs-parser@^7.0.0:
    version "7.0.0"
    resolved "https://registry.yarnpkg.com/yargs-parser/-/yargs-parser-7.0.0.tgz#8d0ac42f16ea55debd332caf4c4038b3e3f5dfd9"
    integrity sha1-jQrELxbqVd69MyyvTEA4s+P139k=
    dependencies:
      camelcase "^4.1.0"
  
  yargs@^12.0.2, yargs@^12.0.5:
    version "12.0.5"
    resolved "https://registry.yarnpkg.com/yargs/-/yargs-12.0.5.tgz#05f5997b609647b64f66b81e3b4b10a368e7ad13"
    integrity sha512-Lhz8TLaYnxq/2ObqHDql8dX8CJi97oHxrjUcYtzKbbykPtVW9WB+poxI+NM2UIzsMgNCZTIf0AQwsjK5yMAqZw==
    dependencies:
      cliui "^4.0.0"
      decamelize "^1.2.0"
      find-up "^3.0.0"
      get-caller-file "^1.0.1"
      os-locale "^3.0.0"
      require-directory "^2.1.1"
      require-main-filename "^1.0.1"
      set-blocking "^2.0.0"
      string-width "^2.0.0"
      which-module "^2.0.0"
      y18n "^3.2.1 || ^4.0.0"
      yargs-parser "^11.1.1"
  
  yargs@^8.0.2:
    version "8.0.2"
    resolved "https://registry.yarnpkg.com/yargs/-/yargs-8.0.2.tgz#6299a9055b1cefc969ff7e79c1d918dceb22c360"
    integrity sha1-YpmpBVsc78lp/355wdkY3Osiw2A=
    dependencies:
      camelcase "^4.1.0"
      cliui "^3.2.0"
      decamelize "^1.1.1"
      get-caller-file "^1.0.1"
      os-locale "^2.0.0"
      read-pkg-up "^2.0.0"
      require-directory "^2.1.1"
      require-main-filename "^1.0.1"
      set-blocking "^2.0.0"
      string-width "^2.0.0"
      which-module "^2.0.0"
      y18n "^3.2.1"
      yargs-parser "^7.0.0"
  
  yargs@^9.0.0:
    version "9.0.1"
    resolved "https://registry.yarnpkg.com/yargs/-/yargs-9.0.1.tgz#52acc23feecac34042078ee78c0c007f5085db4c"
    integrity sha1-UqzCP+7Kw0BCB47njAwAf1CF20w=
    dependencies:
      camelcase "^4.1.0"
      cliui "^3.2.0"
      decamelize "^1.1.1"
      get-caller-file "^1.0.1"
      os-locale "^2.0.0"
      read-pkg-up "^2.0.0"
      require-directory "^2.1.1"
      require-main-filename "^1.0.1"
      set-blocking "^2.0.0"
      string-width "^2.0.0"
      which-module "^2.0.0"
      y18n "^3.2.1"
      yargs-parser "^7.0.0"
