import { useState, useEffect } from 'react';
import Database from 'app/services/storage/DB';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { MSState } from '../redux/redux';

const useWatched = (show: number, season: number = -1, episode: number = -1) => {
    const [watched, setWatched] = useState(false);
    const [percent, setPercent] = useState(0);
    const progressFetched = useSelector((state: MSState) => state.sync.showProgress[show]?.fetched);
    const calculate = () => {
        const query = 'SELECT * FROM watched WHERE show=? AND season = ? AND episode=?';
        const values = [show, season, episode];

        Database.query(query, values).then(r => {
            if (_.isArray(r) && r.length > 0) {
                const { percent } = r[r.length - 1];
                setPercent(percent);
                setWatched(percent === 100);
            }
        });
    };
    useEffect(() => {
        calculate();
    }, []);
    useEffect(() => {
        calculate();
    }, [progressFetched]);

    return { watched, percent };
};
export default useWatched;
