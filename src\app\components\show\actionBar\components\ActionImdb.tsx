import React, { useState, useEffect } from 'react';
import IMDB from 'app/assets/images/companies/imdb.svg';
import { TouchableOpacity } from 'react-native';
import { TouchableOpacity as Button } from 'react-native-gesture-handler';
import styles from '../styles';

export interface ActionImdbProps {
    onImdbPress: () => void;
    isImdbAvail: boolean;
}

const ActionImdb = ({ onImdbPress, isImdbAvail }: ActionImdbProps) => {
    const [avail, setAvail] = useState(isImdbAvail);
    const imdbOpacity = avail ? 1 : 0.3;

    useEffect(() => {
        setAvail(isImdbAvail);
    }, [isImdbAvail]);
    return (
        // Crazy issue with TouchableOpacity from gesture handler not updating disabled prop and TouchableOpacity from react native not fire onPress
        <Button disabled={!avail} onPress={avail ? onImdbPress : null}>
            <TouchableOpacity disabled={!avail} style={{ opacity: imdbOpacity }}>
                <IMDB width={40} height={20} style={{ ...styles.icon }} />
            </TouchableOpacity>
        </Button>
    );
};

export default React.memo(ActionImdb);
