import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import _ from 'lodash';
import { SPACING } from 'app/app/styles/sizes';
import { useNavigation } from 'react-navigation-hooks';
import Constants from 'app/app/Constants';
import { PeopleNavParams } from 'app/views/people/view';
import i18n from 'i18n-js';
import PersonCardItem from '../listItems/person/PersonCardItem';
import PersonListItem from '../listItems/person/PersonListItem';
import ShowMore from '../elements/ShowMore';
import Divider from '../containers/Divider';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    list: {
        paddingHorizontal: SPACING.medium
    }
});
export interface PeopleProps {
    people: TMDB.Cast[] | TMDB.Crew[];
    type: TMDB.PeopleType;
    display: 'list' | 'card';
    limit: number;
}

const People = ({ people, type, display, limit }: PeopleProps) => {
    const { navigate } = useNavigation();
    const Element = display === 'card' ? PersonCardItem : PersonListItem;
    const Container = display === 'card' ? ScrollView : View;
    if (!people) {
        return (
            <Container horizontal contentContainerStyle={styles.list}>
                <Text>{i18n.t('NOT_AVAILABLE')}</Text>
            </Container>
        );
    }
    const items = limit > 0 ? limit : people.length;

    const params: PeopleNavParams = { people, type };
    const onPress = () => navigate(Constants.Navigation.People.VIEW, params);
    const showAll = items < people.length ? <ShowMore {...{ onPress }} /> : null;
    switch (type) {
        case 'cast':
        default:
            return (
                <>
                    <Container horizontal contentContainerStyle={styles.list}>
                        {(people.slice(0, items) as TMDB.Cast[]).map(actor => {
                            const { character, credit_id, name, profile_path, gender, id } = actor;
                            return (
                                <Element
                                    {...{
                                        id,
                                        key: credit_id,
                                        name,
                                        character,
                                        profile_path,
                                        gender
                                    }}
                                />
                            );
                        })}
                    </Container>
                    <Divider size={SPACING.normal} />
                    {showAll}
                </>
            );
        case 'crew':
            return (
                <>
                    <Container horizontal contentContainerStyle={styles.list}>
                        {(people.slice(0, items) as TMDB.Crew[]).map(crew => {
                            const { department, job, name, profile_path, gender, credit_id, id } = crew;
                            return (
                                <Element
                                    {...{
                                        id,
                                        key: credit_id,
                                        name,
                                        department,
                                        job,
                                        gender,
                                        profile_path
                                    }}
                                />
                            );
                        })}
                    </Container>
                    <Divider size={SPACING.normal} />
                    {showAll}
                </>
            );
    }
};
People.defaultProps = {
    type: 'cast',
    display: 'card',
    limit: 0
};
export default React.memo(People, _.isEqual);
