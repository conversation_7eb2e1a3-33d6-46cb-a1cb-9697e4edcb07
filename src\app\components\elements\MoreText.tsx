import React, { useState, useEffect, PropsWithChildren } from 'react';
import { StyleSheet, ViewProps, View, Dimensions } from 'react-native';
import Animated, { Easing } from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeConsumer } from 'react-native-elements';
import { Theme } from 'styles/themes';
import { useMemoOne } from 'use-memo-one';
import { TouchableOpacity } from 'react-native-gesture-handler';

const styles = StyleSheet.create({
    container: {
        borderWidth: 1,
        borderColor: 'transparent',
        overflow: 'hidden'
    },
    gradient: {
        ...StyleSheet.absoluteFillObject,
        padding: 20
    },
    dummy: {
        position: 'absolute',
        top: 0
    }
});

const { width } = Dimensions.get('window');
export interface MoreTextProps {
    viewProps?: ViewProps;
    expanded?: boolean;
    initialHeight?: number;
    onPress?: () => void;
    onExpand?: () => void;
    onCollapse?: () => void;
    expandable?: boolean;
}

interface State {
    isExpanded: boolean;
}

type Props = PropsWithChildren<MoreTextProps>;

const MoreText = ({
    expanded,
    children,
    viewProps,
    initialHeight,
    onPress,
    expandable,
    onCollapse,
    onExpand
}: Props) => {
    const [isExpanded, setIsExpanded] = useState(expanded);
    const { animation } = useMemoOne(() => ({ animation: new Animated.Value(expanded ? 1 : 0) }), []);
    const [contentHeight, setContentHeight] = useState(0);
    const toggle = () => {
        if (isExpanded) {
            onCollapse();
        } else {
            onExpand();
        }
        Animated.timing(animation, {
            toValue: isExpanded ? 0 : 1,
            duration: 450,
            easing: Easing.poly(4)
        }).start(() => {
            setIsExpanded(!isExpanded);
        });
    };

    useEffect(() => {
        if (isExpanded !== expanded) {
            toggle();
        }
    }, [expanded]);

    const height = animation.interpolate({
        inputRange: [0, 1],
        outputRange: [initialHeight, contentHeight],
        extrapolate: Animated.Extrapolate.CLAMP
    });
    const translateY = animation.interpolate({
        inputRange: [0, 1],
        outputRange: [0, contentHeight],
        extrapolate: Animated.Extrapolate.CLAMP
    });

    const AnimatedGradient = Animated.createAnimatedComponent(LinearGradient);
    const moreTextPress = expandable ? onPress || toggle : null;
    return (
        <TouchableOpacity activeOpacity={1} onPress={moreTextPress}>
            <>
                <Animated.View
                    {...viewProps}
                    style={[
                        viewProps.style,
                        styles.container,
                        {
                            height
                        }
                    ]}
                >
                    {children}
                    <ThemeConsumer<Theme>>
                        {({ theme: { scheme } }) => {
                            const gradient = scheme === 'dark' ? ['#00000000', '#000000'] : ['#FFFFFF00', '#FFFFFF'];
                            return (
                                <AnimatedGradient
                                    colors={gradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 0, y: 1 }}
                                    style={[
                                        styles.gradient,
                                        {
                                            transform: [{ translateY }]
                                        }
                                    ]}
                                />
                            );
                        }}
                    </ThemeConsumer>
                </Animated.View>
                {contentHeight === 0 && (
                    <View
                        style={{ ...styles.dummy, transform: [{ translateX: -width }] }}
                        onLayout={e => {
                            setContentHeight(e.nativeEvent.layout.height + 40);
                        }}
                    >
                        {children}
                    </View>
                )}
            </>
        </TouchableOpacity>
    );
};
// @ts-ignore
MoreText.defaultProps = {
    expanded: false,
    initialHeight: 100,
    viewProps: {},
    onPress: null,
    onExpand: () => {},
    onCollapse: () => {},
    expandable: true
};
export default MoreText;
