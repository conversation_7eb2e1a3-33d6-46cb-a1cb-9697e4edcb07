namespace Api {
    type Hosts = 'trakt' | 'tmdb' | 'omdb';

    interface Entity<P, M = {}> {
        id: number | string;
        type: string;
        attributes: P;
        meta: M;
    }

    interface NormalizedResponse<P, T = number> {
        entities: {
            [key: string]: { [key: T]: Api.Entity<P> };
        };
        result: T[];
    }

    interface NormalizedSingleResponse<P> {
        entities: {
            [key: string]: { [key: number]: Api.Entity<P> };
        };
        result: number;
    }

    interface TraktHeaders {
        map: {
            'cf-cache-status': string;
            'x-runtime': string;
            expires: string;
            'cache-control': string;
            'x-pagination-limit': string;
            'x-pagination-page': string;
            'x-pagination-page-count': string;
            'content-type': string;
            date: string;
            vary: string;
            'content-encoding': string;
            'x-xss-protection': string;
            'x-request-id': string;
            'cf-request-id': string;
            'expect-ct': string;
            'x-frame-options': string;
            server: string;
            etag: string;
            'x-pagination-item-count': string;
            'cf-ray': string;
            'x-content-type-options': string;
            'x-applied-sort-by': 'rank';
            'x-applied-sort-how': 'asc';
            'x-sort-by': Trakt.ListSorting;
            'x-sort-how': Trakt.SortDirection;
        };
    }

    interface TraktPagination {
        currentPage: number;
        totalPages: number;
        limit: number;
        totalItems: number;
    }
}
