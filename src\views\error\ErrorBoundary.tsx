import React from 'react';
import { NavigationScreenProp } from 'react-navigation';
import { Dimensions, StyleSheet, View, Image } from 'react-native';
import { SPACING, FONT_SIZE, LINE_HEIGHT } from 'app/app/styles/sizes';
import Text from 'app/app/components/elements/Text';
import { Button, ThemeConsumer } from 'react-native-elements';
import i18n from 'i18n-js';
import { resetStore, AppAction } from 'app/app/redux/app/actions';
import { connect } from 'react-redux';
import { Theme } from 'app/app/styles/themes';
import errorImage from 'app/assets/images/error/error.jpg';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
    container: {
        paddingTop: SPACING.large * 3,
        alignItems: 'center',
        flex: 1
    },
    image: {
        width: width - 100,
        height: width - 100,
        marginBottom: SPACING.large
    },
    header: {
        fontSize: FONT_SIZE.larger,
        lineHeight: LINE_HEIGHT.larger,
        marginBottom: SPACING.large
    },
    buttonContainer: {
        width: width - 100
    },
    button: {
        marginVertical: SPACING.small,
        padding: SPACING.small
    },
    action: {
        fontSize: FONT_SIZE.large,
        lineHeight: LINE_HEIGHT.large
    }
});

interface ErrorBoundaryProps {
    navigation?: NavigationScreenProp<{}>;
}

interface DispatchProps {
    resetStore: () => Promise<AppAction>;
}
interface State {
    hasError: boolean;
}
type Props = ErrorBoundaryProps & DispatchProps;
class ErrorBoundary extends React.Component<Props, State> {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError() {
        return { hasError: true };
    }

    componentDidCatch() {
        this.setState({ hasError: true });
        // Sentry.captureException(error, { extra: info });
    }

    render() {
        const { navigation, children, resetStore } = this.props;
        const { hasError } = this.state;
        if (hasError) {
            return (
                <ThemeConsumer<Theme>>
                    {({ theme: { colors } }) => {
                        return (
                            <>
                                <View style={{ backgroundColor: colors.background, ...styles.container }}>
                                    <Image style={{ ...styles.image }} source={errorImage} />
                                    <Text centered style={styles.header}>
                                        {i18n.t('SBH')}
                                    </Text>
                                    <Button
                                        buttonStyle={styles.buttonContainer}
                                        containerStyle={styles.button}
                                        title={i18n.t('GO_BACK')}
                                        onPress={() => {
                                            this.setState({ hasError: false }, () => {
                                                navigation.goBack(null);
                                            });
                                        }}
                                    />
                                    <Text centered>{i18n.t('OR')}</Text>
                                    <Button
                                        containerStyle={styles.button}
                                        buttonStyle={styles.buttonContainer}
                                        title={i18n.t('RESET_CACHE')}
                                        onPress={() => {
                                            this.setState({ hasError: false }, () => {
                                                // CodePush.restartApp();
                                                resetStore();
                                                navigation.navigate('authLoad');
                                            });
                                        }}
                                    />
                                </View>
                            </>
                        );
                    }}
                </ThemeConsumer>
            );
        }
        return children;
    }
}

const mapDispatchToProps = dispatch => {
    return {
        resetStore: () => dispatch(resetStore())
    };
};

export default connect(null, mapDispatchToProps)(ErrorBoundary);
