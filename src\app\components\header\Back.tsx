import React from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import HeaderIcon from './HeaderIcon';

export interface BackProps {
    onPress?: () => void;
}

type Props = BackProps & NavigationInjectedProps;
const Back = ({ navigation, onPress }: Props) => {
    return (
        <HeaderIcon
            name="arrow-left"
            onPress={() => {
                if (onPress) {
                    onPress();
                } else {
                    navigation.goBack(null);
                }
            }}
        />
    );
};

export default withNavigation(Back);
