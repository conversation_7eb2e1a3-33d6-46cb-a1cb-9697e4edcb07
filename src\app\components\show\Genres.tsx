import React, { useContext } from 'react';
import { StyleSheet, View, FlatList } from 'react-native';
import _ from 'lodash';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import { Theme } from 'app/app/styles/themes';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import Tag from '../containers/Tag';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: SPACING.medium
    },
    tag: {
        borderWidth: 1,
        borderRadius: 10,
        paddingHorizontal: SPACING.normal
    }
});

export interface GenresProps {
    genres: TMDB.Genre[];
    traktGenres: string[];
}

const Genres = ({ genres, traktGenres }: GenresProps) => {
    const genresToShow =
        genres ||
        traktGenres.map(g => {
            return { name: g };
        });
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const renderItem = ({ item }: { item: { name: string } }) => {
        const { name } = item;

        return <Tag {...{ label: name, textStyle: 'small', backgroundColor: colors.primary }} />;
    };
    return (
        <View style={{ ...styles.container }}>
            <FlatList<{ name: string }>
                keyExtractor={item => item.name}
                horizontal
                data={genresToShow}
                renderItem={renderItem}
                ListEmptyComponent={<Text> {i18n.t('NO_GENRES')}</Text>}
            />
        </View>
    );
};

export default React.memo(Genres, _.isEqual);
