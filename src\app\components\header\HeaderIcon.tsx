import React, { useContext } from 'react';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { ICON_SIZE, SPACING } from 'app/app/styles/sizes';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Icon from '../elements/Icon';

export interface HeaderIconProps {
    name: string;
    type?: string;
    onPress?: () => void;
    size?: number;
    color?: string;
    disabled?: boolean;
}

const HeaderIcon = ({ name, type, color, onPress, size, disabled }: HeaderIconProps) => {
    const { theme } = useContext(ThemeContext) as ThemeProps<Theme>;
    const iconColor = color || theme.colors.onBackground;
    const iconSize = size || ICON_SIZE.medium;
    return (
        <TouchableOpacity {...{ onPress, disabled }}>
            <Icon
                {...{
                    name,
                    type,
                    color: iconColor,
                    size: iconSize,
                    containerStyle: {
                        paddingRight: SPACING.normal,
                        opacity: disabled ? 0.5 : 1
                    }
                }}
            />
        </TouchableOpacity>
    );
};

HeaderIcon.defaultProps = {
    disabled: false,
    type: 'feather',
    onPress: () => {}
};
export default HeaderIcon;
