import React, { useState, useEffect } from 'react';
import { NavParams } from 'app/app/navigation/Navigator';
import { Header, ListItem } from 'react-native-elements';
import Back from 'app/app/components/header/Back';
import { useNavigation } from 'react-navigation-hooks';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import _ from 'lodash';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ListItemsState } from 'app/app/redux/list/reducers';
import getEntities from 'app/app/redux/entities/selectors';
import Container from 'app/app/components/containers/Container';
import { addToList, removeFromList } from 'app/app/redux/list/actions';

export interface ListedNavParams extends NavParams {}

export interface ListedProps {}

const Listed = () => {
    const dispatch = useDispatch();
    const navigation = useNavigation();
    const id = navigation.getParam('id');
    const [inLists, setInLists] = useState([]);

    const {
        lists,
        hasLists,
        listItems
    }: { hasLists: boolean; lists: Api.Entity<Trakt.CustomList>[]; listItems: ListItemsState } = useSelector(
        (state: MSState) => {
            const listResult = state.lists.lists.result;
            const lists = getEntities(state, listResult, 'list');
            const hasLists = lists.length > 0;

            return {
                lists,
                hasLists,
                listItems: state.lists.listItems
            };
        },
        _.isEqual
    );
    useEffect(() => {
        if (hasLists) {
            const listed = ShowHelper.getListed(listItems, id);
            setInLists(listed);
        }
    }, [hasLists]);

    const onPress = (listId: number) => {
        const listed = [...inLists];
        if (listed.includes(listId)) {
            setInLists(listed.filter(l => l !== listId));
            dispatch(removeFromList(listId, id));
        } else {
            listed.push(listId);
            setInLists(listed);
            dispatch(addToList(listId, id));
        }
    };

    return (
        <>
            <Header leftComponent={<Back />} centerComponent={{ text: 'NAME' }} />
            <Container>
                {lists.map(list => {
                    const {
                        ids: { trakt },
                        description,
                        name
                    } = list.attributes;
                    return (
                        <ListItem
                            key={trakt}
                            title={name}
                            subtitle={description}
                            checkBox={{ onPress: () => onPress(trakt), checked: inLists.includes(trakt) }}
                        />
                    );
                })}
            </Container>
        </>
    );
};

export default React.memo(Listed);
