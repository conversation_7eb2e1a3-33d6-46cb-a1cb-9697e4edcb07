/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from 'react';
import i18n from 'i18n-js';
import { Header } from 'react-native-elements';
import Back from 'app/app/components/header/Back';

import ClearButton from 'app/app/components/elements/ClearButton';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import Container from 'app/app/components/containers/Container';
import { postComment } from 'app/app/redux/comment/actions';
import { useDispatch } from 'react-redux';
import { SeasonAction, getTraktSeasons, GET_TRAKT_SEASONS_SUCCESS } from 'app/app/redux/season/actions';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { getTraktEpisodes, GET_TRAKT_EPISODES_SUCCESS } from 'app/app/redux/episode/actions';
import Form from '../components/Form';

export interface PostEpisodeCommentProps {
    id: number;
    error: string;
    seasonNumber: number;
    episodeNumber: number;
    loading: boolean;
}

const PostEpisodeComment = ({ id, loading, error, seasonNumber, episodeNumber }: PostEpisodeCommentProps) => {
    const dispatch: MS.Dispatch<SeasonAction> = useDispatch();
    const placeholder = i18n.t('COMMENT');
    const [episodeId, setEpisodeId] = useState(null);
    const [data, setData] = useState<Trakt.CommentForm>({ comment: '', spoiler: false });
    const onChange = (data: Trakt.CommentForm) => {
        const postData = { ...data };
        postData.episode = { ids: { trakt: episodeId } };
        setData(postData);
    };

    useEffect(() => {
        dispatch(getTraktEpisodes(id, seasonNumber)).then(action => {
            if (action.type === GET_TRAKT_EPISODES_SUCCESS) {
                const { episode: episodes } = action.payload.normalized.entities;
                const id = Object.keys(episodes)
                    .map(key => {
                        const episode: Api.Entity<Trakt.Episode> = episodes[key];
                        if (episode.attributes.number === episodeNumber) {
                            return episode.id;
                        }
                        return null;
                    })
                    .filter(v => !!v)?.[0];
                setEpisodeId(id || null);
            }
        });
    }, []);

    const onSubmit = () => {
        dispatch(postComment(data));
    };
    const rightComponent = <ClearButton small title={i18n.t('POST')} onPress={onSubmit} />;

    return (
        <>
            <Header
                {...{
                    leftComponent: <Back />,
                    rightComponent,
                    centerComponent: { text: i18n.t('POST_COMMENT') }
                }}
            />
            <Container keyboardShouldPersistTaps="handled">
                {episodeId && <Form {...{ onChange, placeholder, error }} />}
                {(loading || !episodeId) && <ScreenLoader />}
            </Container>
        </>
    );
};

export default React.memo(PostEpisodeComment);
