import { StyleSheet } from 'react-native';
import { SPACING } from 'app/app/styles/sizes';

export default StyleSheet.create({
    inputField: { padding: 0, paddingLeft: 0, paddingBottom: 0, marginBottom: 0 },
    fieldContainer: {
        padding: 0,
        borderBottomWidth: 0,
        paddingRight: SPACING.medium
    },
    checkBox: { paddingVertical: SPACING.normal, paddingRight: 0 },
    chevron: { paddingVertical: SPACING.normal, paddingRight: SPACING.normal * 2.5 },
    buttons: {
        paddingBottom: SPACING.normal,
        paddingLeft: 0,
        paddingRight: SPACING.normal,
        paddingTop: SPACING.medium
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
    },
    multiselectOk: {
        // position: 'absolute',
        // right: SPACING.medium,
        // top: SPACING.normal
    },
    selectLabel: { textAlign: 'right' },
    select: {
        marginVertical: SPACING.normal
    },
    selectTitleContainer: {
        padding: SPACING.normal,
        borderTopWidth: 1,
        justifyContent: 'space-between'
    },
    selectTitle: { flex: 1 }
});
