import { Action } from 'redux';
import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { GET_RECOMMENDED_SHOWS_ERROR } from '../show/actions';
import { TmdbEpisodeDetailsSchema, TraktEpisodeSchema } from '../schemas';

export const GET_EPISODE_DETAILS_REQUEST = 'GET_EPISODE_DETAILS_REQUEST';
export const GET_EPISODE_DETAILS_SUCCESS = 'GET_EPISODE_DETAILS_SUCCESS';
export const GET_EPISODE_DETAILS_ERROR = 'GET_EPISODE_DETAILS_ERROR';

export const GET_TRAKT_EPISODES_REQUEST = 'GET_TRAKT_EPISODES_REQUEST';
export const GET_TRAKT_EPISODES_SUCCESS = 'GET_TRAKT_EPISODES_SUCCESS';
export const GET_TRAKT_EPISODES_ERROR = 'GET_TRAKT_EPISODES_ERROR';

export interface EpisodeAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<TMDB.Episode>;
        headers: Api.TraktHeaders;
        id: number;
        season: number;
        episode: number;
    };
}
export const getEpisodeDetails = (id: number, season: number, episode: number) => {
    const params = { append_to_response: 'credits,external_ids' };
    return apiClient(
        [
            GET_EPISODE_DETAILS_REQUEST,
            {
                type: GET_EPISODE_DETAILS_SUCCESS,
                payload: (action, state, res) => {
                    const { headers }: { headers: Headers } = res;
                    return res.json().then(json => {
                        const normalized = normalize(json, TmdbEpisodeDetailsSchema);
                        return { normalized, headers, id, season, episode };
                    });
                }
            },
            GET_RECOMMENDED_SHOWS_ERROR
        ],
        `/tv/${id}/season/${season}/episode/${episode}`,
        'GET',
        'tmdb',
        params
    );
};

export const getTraktEpisodes = (id: number, season: number) => {
    return apiClient(
        [
            GET_TRAKT_EPISODES_REQUEST,
            {
                type: GET_TRAKT_EPISODES_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        const normalized = normalize(json, [TraktEpisodeSchema]);
                        return { normalized, id, season };
                    });
                }
            },
            GET_TRAKT_EPISODES_ERROR
        ],
        `/shows/${id}/seasons/${season}`,
        'GET',
        'trakt'
    );
};
