import apiClient from 'app/services/api/client';
import Constants from 'app/app/Constants';
import { Action } from 'redux';
import { MSState } from '../redux';
import { getUserSettings } from '../user/actions';
import { clearFavorites } from '../favorite/actions';
import { clearRatings } from '../sync/rate';
import { clearWatched } from '../sync/watch';
import { clearCollection } from '../sync/collection';

export const GET_TOKEN_REQUEST = 'GET_TOKEN_REQUEST';
export const GET_TOKEN_SUCCESS = 'GET_TOKEN_SUCCESS';
export const GET_TOKEN_ERROR = 'GET_TOKEN_ERROR';

export const REFRESH_TOKEN_REQUEST = 'REFRESH_TOKEN_REQUEST';
export const REFRESH_TOKEN_SUCCESS = 'REFRESH_TOKEN_SUCCESS';
export const REFRESH_TOKEN_ERROR = 'REFRESH_TOKEN_ERROR';

export const SIGN_OUT_REQUEST = 'SIGN_OUT_REQUEST';
export const SIGN_OUT_SUCCESS = 'SIGN_OUT_SUCCESS';
export const SIGN_OUT_ERROR = 'SIGN_OUT_ERROR';

export const GUEST_SIGN_IN_SUCCESS = 'GUEST_SIGN_IN_SUCCESS';

export interface AuthAction extends Action {
    payload: {
        token: Trakt.Token;
    };
}

const clearApp = dispatch => {
    dispatch(clearFavorites());
    dispatch(clearRatings());
    dispatch(clearWatched());
    dispatch(clearCollection());
};

export const getToken = (code: string) => dispatch => {
    return dispatch(
        apiClient(
            [
                GET_TOKEN_REQUEST,
                {
                    type: GET_TOKEN_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const token: Trakt.Token = json;
                            dispatch(getUserSettings(token.access_token));
                            return { token };
                        });
                    }
                },
                GET_TOKEN_ERROR
            ],
            `/oauth/token/`,
            'POST',
            'trakt',
            {},
            {
                body: {
                    code,
                    client_id: Constants.Api.Trakt.CLIENT_ID,
                    client_secret: Constants.Api.Trakt.CLIENT_SECRET,
                    redirect_uri: Constants.Api.Trakt.REDIRECT_URL,
                    grant_type: Constants.Api.Trakt.GRANT_TYPE_AUTH
                }
            }
        )
    );
};

export const refreshToken = (token: string) => dispatch => {
    console.log('refresh token', token);
    return dispatch(
        apiClient(
            [
                REFRESH_TOKEN_REQUEST,
                {
                    type: REFRESH_TOKEN_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const token: Trakt.Token = json;
                            console.log('refreshed token', token);
                            return { token };
                        });
                    }
                },
                {
                    type: REFRESH_TOKEN_ERROR,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            console.log('refresh token error', json);
                            return { error: json };
                        });
                    }
                }
            ],
            `/oauth/token/`,
            'POST',
            'trakt',
            {},
            {
                body: {
                    refresh_token: token,
                    client_id: Constants.Api.Trakt.CLIENT_ID,
                    client_secret: Constants.Api.Trakt.CLIENT_SECRET,
                    redirect_uri: Constants.Api.Trakt.REDIRECT_URL,
                    grant_type: Constants.Api.Trakt.GRANT_TYPE_REFRESH
                }
            }
        )
    );
};

export const signOut = () => (dispatch, getState) => {
    const state: MSState = getState();
    const token = state.auth.trakt.token.access_token;
    return dispatch(
        apiClient(
            [
                SIGN_OUT_REQUEST,
                {
                    type: SIGN_OUT_SUCCESS,
                    payload: (action, state, res) => {
                        clearApp(dispatch);
                        return res.json().then(() => {
                            return { token: null };
                        });
                    }
                },
                SIGN_OUT_ERROR
            ],
            `/oauth/revoke/`,
            'POST',
            'trakt',
            {},
            {
                body: {
                    refresh_token: token,
                    client_id: Constants.Api.Trakt.CLIENT_ID,
                    client_secret: Constants.Api.Trakt.CLIENT_SECRET
                }
            }
        )
    );
};

export const guestSignIn = () => dispatch => {
    return new Promise(resolve => {
        resolve(
            dispatch({
                type: GUEST_SIGN_IN_SUCCESS,
                payload: {}
            })
        );
    });
};

export const guestSignOut = () => dispatch => {
    return new Promise(resolve => {
        clearApp(dispatch);
        resolve(
            dispatch({
                type: SIGN_OUT_SUCCESS,
                payload: {}
            })
        );
    });
};
