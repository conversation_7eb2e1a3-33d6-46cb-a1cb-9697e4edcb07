import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Icon } from 'react-native-elements';
import { ICON_SIZE } from 'app/app/styles/sizes';
import { common } from 'app/app/styles/themes';
import MaskedView from '@react-native-masked-view/masked-view';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center'
    },
    mask: {
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export interface SmallRatingProps {
    rating: number;
    max?: number;
    size?: number;
}

const SmallRating = ({ rating, max, size }: SmallRatingProps) => {
    const width = (size * rating) / max;
    const color = common.red;
    return (
        <View style={styles.container}>
            <MaskedView
                style={{
                    width: size
                }}
                maskElement={
                    <View style={styles.mask}>
                        <Icon type="font-awesome" name="heart" size={size} />
                    </View>
                }
            >
                <View
                    style={{
                        backgroundColor: color,
                        width,
                        height: size
                    }}
                />
            </MaskedView>
            <Text textStyle="normal" color={color}>
                {rating}
            </Text>
        </View>
    );
};
SmallRating.defaultProps = {
    rating: 10,
    max: 10,
    size: ICON_SIZE.small
};
export default React.memo(SmallRating);
