import Database from 'app/services/storage/DB';
import apiClient from 'app/services/api/client';
import _ from 'lodash';
import { MSState } from '../redux';
import { UNATHORIZED } from '../app/actions';
import { UPDATE_COLLECTION } from '../meta/actions';
import { updateLastActivity } from './actions';

export const GET_COLLECTION_REQUEST = 'GET_COLLECTION_REQUEST';
export const GET_COLLECTION_SUCCESS = 'GET_COLLECTION_SUCCESS';
export const GET_COLLECTION_ERROR = 'GET_COLLECTION_ERROR';

export const CLEAR_COLLECTION_REQUEST = 'C<PERSON><PERSON>_COLLECTION_REQUEST';
export const CLEAR_COLLECTION_SUCCESS = 'CLEAR_COLLECTION_SUCCESS';
export const CLEAR_COLLECTION_ERROR = 'CLEAR_COLLECTION_ERROR';

export const COLLECT_SHOW_REQUEST = 'COLLECT_SHOW_REQUEST';
export const COLLECT_SHOW_SUCCESS = 'COLLECT_SHOW_SUCCESS';
export const COLLECT_SHOW_ERROR = 'COLLECT_SHOW_ERROR';

export const UNCOLLECT_SHOW_REQUEST = 'UNCOLLECT_SHOW_REQUEST';
export const UNCOLLECT_SHOW_SUCCESS = 'UNCOLLECT_SHOW_SUCCESS';
export const UNCOLLECT_SHOW_ERROR = 'UNCOLLECT_SHOW_ERROR';

export const COLLECT_SEASON_REQUEST = 'COLLECT_SEASON_REQUEST';
export const COLLECT_SEASON_SUCCESS = 'COLLECT_SEASON_SUCCESS';
export const COLLECT_SEASON_ERROR = 'COLLECT_SEASON_ERROR';

export const UNCOLLECT_SEASON_REQUEST = 'UNCOLLECT_SEASON_REQUEST';
export const UNCOLLECT_SEASON_SUCCESS = 'UNCOLLECT_SEASON_SUCCESS';
export const UNCOLLECT_SEASON_ERROR = 'UNCOLLECT_SEASON_ERROR';

export const COLLECT_EPISODE_REQUEST = 'COLLECT_EPISODE_REQUEST';
export const COLLECT_EPISODE_SUCCESS = 'COLLECT_EPISODE_SUCCESS';
export const COLLECT_EPISODE_ERROR = 'COLLECT_EPISODE_ERROR';

export const UNCOLLECT_EPISODE_REQUEST = 'UNCOLLECT_EPISODE_REQUEST';
export const UNCOLLECT_EPISODE_SUCCESS = 'UNCOLLECT_EPISODE_SUCCESS';
export const UNCOLLECT_EPISODE_ERROR = 'UNCOLLECT_EPISODE_ERROR';

const extractCollectionData = (data: any[]) => {
    const rows = [];
    data.forEach(value => {
        const { show, seasons } = value;
        const showId = show.ids.trakt;
        rows.push({ show: showId, season: -1, episode: -1 });
        seasons.forEach(season => {
            const seasonNumber = season.number;
            rows.push({ show: showId, season: seasonNumber, episode: -1 });
            season.episodes.forEach(episode => {
                const episodeNumber = episode.number;
                rows.push({ show: showId, season: seasonNumber, episode: episodeNumber });
            });
        });
    });
    return rows;
};

export const clearCollection = () => dispatch => {
    dispatch({ type: CLEAR_COLLECTION_REQUEST });
    return Database.query('DROP TABLE collection')
        .then(() => {
            dispatch({ type: UPDATE_COLLECTION });
            return dispatch({ type: CLEAR_COLLECTION_SUCCESS });
        })
        .catch(error => {
            dispatch({ type: CLEAR_COLLECTION_ERROR, error });
        });
};

export const getCollection = () => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                GET_COLLECTION_REQUEST,
                {
                    type: GET_COLLECTION_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async json => {
                            const collection = extractCollectionData(json);
                            await Database.query('DELETE FROM collection');
                            const sql = 'INSERT INTO collection (show,season,episode) VALUES ';
                            _.chunk(collection, 50).forEach(watch => {
                                const queries = watch.map(row => {
                                    const { show, season, episode } = row;
                                    return {
                                        q: '(?,?,?)',
                                        p: [show, season, episode]
                                    };
                                });
                                const placeholders = queries.map(q => q.q).join(',');
                                const values = queries.map(q => q.p).flat(1);
                                const query = `${sql}${placeholders}`;
                                Database.query(query, values);
                            });
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                GET_COLLECTION_ERROR
            ],
            `/sync/collection/shows`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const addToCollection = async (show: number, season: number = -1, episode: number = -1) => {
    const sql = 'INSERT INTO collection (show,season,episode) VALUES (?, ?, ?)';
    return Database.query(sql, [show, season, episode]);
};

export const collectShow = (show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                COLLECT_SHOW_REQUEST,
                {
                    type: COLLECT_SHOW_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async () => {
                            dispatch(updateLastActivity());
                            dispatch(getCollection());
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                COLLECT_SHOW_ERROR
            ],
            `/sync/collection`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: { shows: [{ ids: { trakt: show } }] }
            }
        )
    );
};

export const unCollectShow = (show: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UNCOLLECT_SHOW_REQUEST,
                {
                    type: UNCOLLECT_SHOW_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async () => {
                            dispatch(updateLastActivity());
                            dispatch(getCollection());
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                UNCOLLECT_SHOW_ERROR
            ],
            `/sync/collection/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: { shows: [{ ids: { trakt: show } }] }
            }
        )
    );
};

export const collectSeason = (show: number, season: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                COLLECT_SEASON_REQUEST,
                {
                    type: COLLECT_SEASON_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async () => {
                            dispatch(updateLastActivity());
                            dispatch(getCollection());
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                COLLECT_SEASON_ERROR
            ],
            `/sync/collection`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season }] }]
                }
            }
        )
    );
};

export const unCollectSeason = (show: number, season: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UNCOLLECT_SEASON_REQUEST,
                {
                    type: UNCOLLECT_SEASON_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async () => {
                            dispatch(updateLastActivity());
                            dispatch(getCollection());
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                UNCOLLECT_SEASON_ERROR
            ],
            `/sync/collection/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season }] }]
                }
            }
        )
    );
};

export const collectEpisode = (show: number, season: number, episode: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                COLLECT_EPISODE_REQUEST,
                {
                    type: COLLECT_EPISODE_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async () => {
                            dispatch(updateLastActivity());
                            dispatch(getCollection());
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                COLLECT_EPISODE_ERROR
            ],
            `/sync/collection`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season, episodes: [{ number: episode }] }] }]
                }
            }
        )
    );
};

export const unCollectEpisode = (show: number, season: number, episode: number) => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UNCOLLECT_EPISODE_REQUEST,
                {
                    type: UNCOLLECT_EPISODE_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(async () => {
                            dispatch(updateLastActivity());
                            dispatch(getCollection());
                            dispatch({ type: UPDATE_COLLECTION });
                            return {};
                        });
                    }
                },
                UNCOLLECT_EPISODE_ERROR
            ],
            `/sync/collection/remove`,
            'POST',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                },
                body: {
                    shows: [{ ids: { trakt: show }, seasons: [{ number: season, episodes: [{ number: episode }] }] }]
                }
            }
        )
    );
};
