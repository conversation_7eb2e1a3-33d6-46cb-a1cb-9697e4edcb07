import React, { useContext, useState } from 'react';
import { StyleSheet } from 'react-native';
import { Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { ICON_SIZE, SPACING } from 'app/app/styles/sizes';
import { useDispatch, useSelector } from 'react-redux';
import { addToFavorites, removeFromFavorites } from 'app/app/redux/favorite/actions';
import { MSState } from 'app/app/redux/redux';
import { TouchableOpacity } from 'react-native-gesture-handler';

const styles = StyleSheet.create({
    icon: {
        paddingHorizontal: SPACING.normal
    }
});

export interface WatchingProps {
    ids: Trakt.Ids;
}

const Watching = ({ ids }: WatchingProps) => {
    const dispatch = useDispatch();

    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const { show, poster, isWatching } = useSelector((state: MSState) => {
        const poster = state.entities.image[ids.tmdb]?.attributes.posters?.[0]?.file_path || null;
        const isWatching = state.entities.favorite[ids.trakt];
        return {
            show: state.entities.show[ids.trakt],
            poster,
            isWatching: !!isWatching
        };
    });
    const [watching, setWatching] = useState(isWatching);
    const onPress = () => {
        if (!isWatching) {
            dispatch(addToFavorites(show, poster));
        } else {
            dispatch(removeFromFavorites(show.attributes.ids.trakt));
        }
        setWatching(!watching);
    };
    return (
        <Icon
            Component={TouchableOpacity}
            name={watching ? 'ios-tv' : 'md-tv'}
            type="ionicon"
            color={colors.onBackground}
            containerStyle={{ ...styles.icon }}
            size={ICON_SIZE.medium}
            onPress={onPress}
        />
    );
};

export default React.memo(Watching);
