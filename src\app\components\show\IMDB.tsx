import React, { useContext } from 'react';
import { StyleSheet, Linking } from 'react-native';
import { SPACING } from 'app/app/styles/sizes';
import { common, Theme } from 'app/app/styles/themes';
import { ThemeProps, ThemeContext } from 'react-native-elements';
import IMDBLogo from 'app/assets/images/companies/imdb.svg';
import i18n from 'i18n-js';
import StringHelper from 'app/services/helpers/StringHelper';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Constants from 'app/app/Constants';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    imdbContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' },
    imdb: { marginRight: SPACING.normal },
    rate: { padding: SPACING.small }
});

export interface IMDBProps {
    rate: number;
    votes: number;
    size?: number;
    imdbId: string;
}

const IMDB = ({ rate, votes, size, imdbId }: IMDBProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const onPress = () => {
        if (imdbId) {
            const uri = `${Constants.Api.Imdb.SHOW_URL}${imdbId}`;
            Linking.canOpenURL(uri).then(can => {
                if (can) {
                    Linking.openURL(uri);
                }
            });
        }
    };
    return (
        <TouchableOpacity disabled={!imdbId} onPress={onPress} style={{ ...styles.imdbContainer }}>
            <IMDBLogo {...{ width: size, height: size / 2, style: styles.imdb }} />
            {rate && votes ? (
                <>
                    <Text style={{ ...styles.rate }} bold textStyle="title" color={common.yellow}>
                        {rate.toFixed(2)}
                    </Text>
                    <Text textStyle="nano" color={colors.discreet}>
                        {`${StringHelper.nFormat(votes)} ${i18n.t('VOTES')}`}
                    </Text>
                </>
            ) : (
                <Text style={{ ...styles.rate }} bold textStyle="title" color={common.yellow}>
                    -
                </Text>
            )}
        </TouchableOpacity>
    );
};

IMDB.defaultProps = {
    size: 50
};

export default React.memo(IMDB);
