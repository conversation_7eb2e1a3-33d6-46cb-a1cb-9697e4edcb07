import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { Action } from 'redux';
import { CertificationSchema, GenreSchema } from '../schemas';

export const RESET_FORM_SUCCESS = 'RESET_FORM_SUCCESS';

export const GET_CERTIFICATIONS_REQUEST = 'GET_CERTIFICATIONS_REQUEST';
export const GET_CERTIFICATIONS_SUCCESS = 'GET_CERTIFICATIONS_SUCCESS';
export const GET_CERTIFICATIONS_ERROR = 'GET_CERTIFICATIONS_ERROR';

export const GET_GENRES_REQUEST = 'GET_GENRES_REQUEST';
export const GET_GENRES_SUCCESS = 'GET_GENRES_SUCCESS';
export const GET_GENRES_ERROR = 'GET_GENRES_ERROR';

export const GET_NETWORKS_REQUEST = 'GET_NETWORKS_REQUEST';
export const GET_NETWORKS_SUCCESS = 'GET_NETWORKS_SUCCESS';
export const GET_NETWORKS_ERROR = 'GET_NETWORKS_ERROR';

export const UPDATE_WATCHED = 'UPDATE_WATCHED';
export const UPDATE_RATINGS = 'UPDATE_RATINGS';
export const UPDATE_COLLECTION = 'UPDATE_COLLECTION';

export interface MetaAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<Trakt.Certification | Trakt.Genre, string>;
        fetched: number;
        networks: string[];
    };
}

export const resetForm = () => dispatch => {
    return new Promise(resolve => resolve(dispatch({ type: RESET_FORM_SUCCESS, payload: {} })));
};

export const getCertifications = () => dispatch => {
    return dispatch(
        apiClient(
            [
                GET_CERTIFICATIONS_REQUEST,
                {
                    type: GET_CERTIFICATIONS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json.us, [CertificationSchema]);
                            return { normalized, fetched: new Date().getTime() };
                        });
                    }
                },
                GET_CERTIFICATIONS_ERROR
            ],
            `/certifications/shows`,
            'GET',
            'trakt'
        )
    );
};

export const getGenres = () => dispatch => {
    return dispatch(
        apiClient(
            [
                GET_GENRES_REQUEST,
                {
                    type: GET_GENRES_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json, [GenreSchema]);
                            return { normalized, fetched: new Date().getTime() };
                        });
                    }
                },
                GET_GENRES_ERROR
            ],
            `/genres/shows`,
            'GET',
            'trakt'
        )
    );
};

export const getNetworks = () => dispatch => {
    return dispatch(
        apiClient(
            [
                GET_NETWORKS_REQUEST,
                {
                    type: GET_NETWORKS_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const networks = json.map(n => n.name);
                            return { networks, fetched: new Date().getTime() };
                        });
                    }
                },
                GET_NETWORKS_ERROR
            ],
            `/networks`,
            'GET',
            'trakt'
        )
    );
};
