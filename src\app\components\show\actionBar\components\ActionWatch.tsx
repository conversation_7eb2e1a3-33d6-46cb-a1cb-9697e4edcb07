import React, { useContext, useState, useEffect } from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { common, Theme } from 'app/app/styles/themes';
import { ICON_SIZE } from 'app/app/styles/sizes';
import Toast from 'react-native-toast-message';
import { useDispatch } from 'react-redux';
import { defaultOptions } from 'app/app/components/elements/Toast';
import i18n from 'i18n-js';
import {
    watchEpisode,
    unWatchEpisode,
    watchSeason,
    unWatchSeason,
    watchShow,
    unWatchShow
} from 'app/app/redux/sync/watch';
import useWatched from 'app/app/hooks/useWatched';
import useGuest from 'app/app/hooks/useGuest';
import styles from '../styles';

export interface ActionWatchProps {
    ids: Trakt.Ids;
    season: number;
    episode: number;
    type: Trakt.RateType;
}

const ActionWatch = ({ type, ids, season, episode }: ActionWatchProps) => {
    const { trakt: show } = ids;
    const isGuest = useGuest();
    const opacity = isGuest ? 0.3 : 1;

    const dispatch = useDispatch();
    const { percent } = useWatched(ids.trakt, season || -1, episode || -1);
    const [watched, setWatched] = useState(percent === 100);
    useEffect(() => {
        setWatched(percent === 100);
    }, [percent]);
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    const onWatchPress = () => {
        if (isGuest) {
            Toast.show({
                ...defaultOptions,
                text1: i18n.t('WATCH_WARNING')
            });
        } else if (type === 'episodes') {
            if (!watched) {
                dispatch(watchEpisode(show, season, episode));
            } else {
                dispatch(unWatchEpisode(show, season, episode));
            }
            setWatched(!watched);
        } else if (type === 'seasons') {
            if (!watched) {
                dispatch(watchSeason(show, season));
            } else {
                dispatch(unWatchSeason(show, season));
            }
            setWatched(!watched);
        } else if (type === 'shows') {
            if (!watched) {
                dispatch(watchShow(show));
            } else {
                dispatch(unWatchShow(show));
            }
            setWatched(!watched);
        }
    };

    return (
        <Icon
            Component={TouchableOpacity}
            name={watched ? 'check-circle' : 'check-circle-o'}
            type="font-awesome"
            color={watched ? common.green : colors.discreet}
            containerStyle={{ ...styles.icon, opacity }}
            size={ICON_SIZE.medium}
            onPress={onWatchPress}
        />
    );
};

export default React.memo(ActionWatch);
