import { createStackNavigator } from 'react-navigation-stack';
import { dark, light } from 'app/app/styles/themes';
import Constants from 'app/app/Constants';
import Lists from 'views/list/view';
import ListShows from 'views/list/shows';
import Manage from 'app/views/list/manage';
import Common from './Common';
import TransitionConfiguration from '../transitions';

export default createStackNavigator(
    {
        [Constants.Navigation.Lists.View]: Lists,
        [Constants.Navigation.Lists.SHOWS]: ListShows,
        [Constants.Navigation.Lists.MANAGE]: {
            screen: Manage,
            params: { noTabBar: true, transition: 'slideUp' }
        },
        ...Common
    },
    {
        headerMode: 'none',
        defaultNavigationOptions: ({ theme }) => {
            const colors = theme === 'dark' ? dark : light;
            return { headerStyle: { backgroundColor: colors.background } };
        },
        transitionConfig: TransitionConfiguration
    }
);
