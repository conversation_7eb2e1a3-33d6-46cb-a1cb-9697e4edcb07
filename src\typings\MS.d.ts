// eslint-disable-next-line @typescript-eslint/no-unused-vars
namespace MS {
    type Locales = 'el' | 'en';
    type Dispatch<P> = (any) => Promise<P>;
    interface Favorite {
        created: number;
        ids: Trakt.Ids;
        network: string;
        poster: string;
        title: string;
        title_sort: string;
        status: Trakt.ShowStatus;
        trakt: number;
        updated: number;
        year: number;
    }

    interface NextEpisode {
        showTitle: string;
        sortTitle: string;
        airedDate: string;
        season: number;
        episode: number;
        episodeIds: Trakt.Ids;
        showIds: Trakt.ids;
        episodeTitle: string;
    }
}
