import { combineReducers } from 'redux';
import {
    SeasonAction,
    GET_SEASON_DETAILS_REQUEST,
    GET_SEASON_DETAILS_SUCCESS,
    GET_SEASON_DETAILS_ERROR
} from './actions';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import { RESET_STORE } from '../app/actions';

/** DETAILS */
export interface SeasonDetailsState {
    loading: boolean;
    [key: number]: {
        [key: number]: {
            result: number;
            headers: Api.TraktHeaders;
        };
    };
}
export const initialSeasonDetailsState: SeasonDetailsState = {
    loading: false
};

export function details(state: SeasonDetailsState = initialSeasonDetailsState, action: SeasonAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialSeasonDetailsState;
        case GET_SEASON_DETAILS_REQUEST: {
            return { ...state, loading: true };
        }

        case GET_SEASON_DETAILS_SUCCESS: {
            const { id, season, normalized, headers } = action.payload;
            return {
                ...state,
                [id]: {
                    ...state[id],
                    [season]: {
                        result: normalized.result,
                        headers
                    }
                },
                loading: false
            };
        }
        case GET_SEASON_DETAILS_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface SeasonsState {
    details: SeasonDetailsState;
}

export const initialSeasonsState: SeasonsState = {
    details: initialSeasonDetailsState
};
export default combineReducers({
    details
});
