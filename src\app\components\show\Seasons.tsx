import React from 'react';
import { View } from 'react-native';
import _ from 'lodash';
import { useNavigation } from 'react-navigation-hooks';
import Constants from 'app/app/Constants';
import { SeasonNavParams } from 'app/views/season/view';
import i18n from 'i18n-js';
import SeasonListItem from '../listItems/season/SeasonListItem';

import Text from '../elements/Text';

export interface SeasonsProps {
    ids: Trakt.Ids;
    seasons: TMDB.Season[];
}

const Seasons = ({ seasons, ids }: SeasonsProps) => {
    // @ts-ignore
    const { push } = useNavigation();
    const onPress = (seasonNumber: number) => {
        const params: SeasonNavParams = { ids, seasonNumber };
        push(Constants.Navigation.Season.VIEW, params);
    };
    if (!seasons) {
        return <Text>{i18n.t('NOT_AVAILABLE')}</Text>;
    }
    return (
        <View>
            {seasons.map(season => {
                const { id, name, air_date, episode_count, poster_path, season_number } = season;

                return (
                    <SeasonListItem
                        {...{
                            show: ids.trakt,
                            season: season_number,
                            key: id,
                            name,
                            air_date,
                            episode_count,
                            onPress: () => onPress(season_number),
                            poster_path
                        }}
                    />
                );
            })}
        </View>
    );
};

export default React.memo(Seasons, _.isEqual);
