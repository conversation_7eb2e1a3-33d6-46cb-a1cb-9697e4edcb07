import { combineReducers } from 'redux';
import { PersistConfig, persistReducer } from 'redux-persist';
import { AsyncStorage } from 'react-native';
import { app, initialAppState, ApplicationState } from './app/reducers';
import auth, { initialAuthState, AuthState } from './auth/reducers';
import user, { UserState, initialUserState } from './user/reducers';
import search, { SearchState, initialSearchState } from './search/reducers';
import entities, { EntitiesState, initialEntitiesState } from './entities/reducers';
import shows, { ShowsState, initialShowsState } from './show/reducers';
import seasons, { SeasonsState, initialSeasonsState } from './season/reducers';
import episodes, { EpisodesState, initialEpisodesState } from './episode/reducers';
import favorites, { FavoritesState, initialFavoritesState } from './favorite/reducers';
import listReducer, { ListsState, initialListsState } from './list/reducers';
import meta, { MetaState, initialMetaState } from './meta/reducer';
import sync, { SyncState, initialSyncState } from './sync/reducers';
import comments, { CommentsState, initialCommentsState } from './comment/reducers';
import stats, { StatsState, initialStatsState } from './stats/reducers';

export const persistConfigEntities: PersistConfig<EntitiesState> = {
    key: 'entities',
    storage: AsyncStorage,
    blacklist: ['externalIds', 'comment', 'episodeRating']
};
export const persistConfigMeta: PersistConfig<MetaState> = {
    key: 'meta',
    storage: AsyncStorage,
    blacklist: ['form', 'importShows']
};

export const persistConfigUser: PersistConfig<UserState> = {
    key: 'user',
    storage: AsyncStorage,
    blacklist: ['latestRatings']
};

export interface MSState {
    app: ApplicationState;
    auth: AuthState;
    comments: CommentsState;
    entities: EntitiesState;
    episodes: EpisodesState;
    favorites: FavoritesState;
    lists: ListsState;
    meta: MetaState;
    search: SearchState;
    seasons: SeasonsState;
    shows: ShowsState;
    stats: StatsState;
    sync: SyncState;
    user: UserState;
}
export const AppInitialState: MSState = {
    app: initialAppState,
    auth: initialAuthState,
    comments: initialCommentsState,
    entities: initialEntitiesState,
    episodes: initialEpisodesState,
    favorites: initialFavoritesState,
    lists: initialListsState,
    meta: initialMetaState,
    search: initialSearchState,
    seasons: initialSeasonsState,
    shows: initialShowsState,
    stats: initialStatsState,
    sync: initialSyncState,
    user: initialUserState
};
const reducer = combineReducers({
    app,
    auth,
    comments,
    entities: persistReducer(persistConfigEntities, entities),
    episodes,
    favorites,
    lists: listReducer,
    meta: persistReducer(persistConfigMeta, meta),
    sync,
    search,
    seasons,
    stats,
    shows,
    user: persistReducer(persistConfigUser, user)
});

export default reducer;
