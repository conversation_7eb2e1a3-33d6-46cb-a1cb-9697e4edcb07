import apiClient from 'app/services/api/client';
import { Action } from 'redux';
import { normalize } from 'normalizr';
import moment from 'moment';
import { MSState } from '../redux';
import { nextCalendarSchema } from '../schemas';
import { UNATHORIZED } from '../app/actions';
import { updateShow } from './watch';

export const SYNC_FAVORITES_REQUEST = 'SYNC_FAVORITES_REQUEST';
export const SYNC_FAVORITES_SUCCESS = 'SYNC_FAVORITES_SUCCESS';
export const SYNC_FAVORITES_ERROR = 'SYNC_FAVORITES_ERROR';

export const GET_MY_CALENDAR_REQUEST = 'GET_MY_CALENDAR_REQUEST';
export const GET_MY_CALENDAR_SUCCESS = 'GET_MY_CALENDAR_SUCCESS';
export const GET_MY_CALENDAR_ERROR = 'GET_MY_CALENDAR_ERROR';

export const GET_LAST_ACTIVITY_REQUEST = 'GET_LAST_ACTIVITY_REQUEST';
export const GET_LAST_ACTIVITY_SUCCESS = 'GET_LAST_ACTIVITY_SUCCESS';
export const GET_LAST_ACTIVITY_ERROR = 'GET_LAST_ACTIVITY_ERROR';

export const UPDATE_LAST_ACTIVITY_REQUEST = 'UPDATE_LAST_ACTIVITY_REQUEST';
export const UPDATE_LAST_ACTIVITY_SUCCESS = 'UPDATE_LAST_ACTIVITY_SUCCESS';
export const UPDATE_LAST_ACTIVITY_ERROR = 'UPDATE_LAST_ACTIVITY_ERROR';

export const RESET_LAST_ACTIVITY = 'RESET_LAST_ACTIVITY';

export interface SyncAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<Trakt.MyRating> | Api.NormalizedSingleResponse<Trakt.NextEpisode>;
        headers: Api.TraktHeaders;
        show: number;
        lastActivities: Trakt.LastActivities;
    };
}

export const getLastActivity = () => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                GET_LAST_ACTIVITY_REQUEST,
                {
                    type: GET_LAST_ACTIVITY_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            return { lastActivities: json };
                        });
                    }
                },
                GET_LAST_ACTIVITY_ERROR
            ],
            `/sync/last_activities`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const updateLastActivity = () => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    return dispatch(
        apiClient(
            [
                UPDATE_LAST_ACTIVITY_REQUEST,
                {
                    type: UPDATE_LAST_ACTIVITY_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            return { lastActivities: json };
                        });
                    }
                },
                UPDATE_LAST_ACTIVITY_ERROR
            ],
            `/sync/last_activities`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const resetLastActivity = (lastActivities: Trakt.LastActivities) => dispatch => {
    return new Promise(resolve => resolve(dispatch({ type: RESET_LAST_ACTIVITY, payload: { lastActivities } })));
};

export const getMyCalendar = () => (dispatch, getState) => {
    const state: MSState = getState();
    const { access_token } = state.auth.trakt.token || {};
    if (!access_token) {
        return dispatch({ type: UNATHORIZED });
    }
    const startDate = moment()
        .add(1, 'day')
        .format('YYYY-MM-DD');
    return dispatch(
        apiClient(
            [
                GET_MY_CALENDAR_REQUEST,
                {
                    type: GET_MY_CALENDAR_SUCCESS,
                    payload: (action, state, res) => {
                        return res.json().then(json => {
                            const normalized = normalize(json, [nextCalendarSchema]);
                            return { normalized };
                        });
                    }
                },
                GET_MY_CALENDAR_ERROR
            ],
            `/calendars/my/shows/${startDate}/31`,
            'GET',
            'trakt',
            {},
            {
                headers: {
                    Authorization: `Bearer ${access_token}`
                }
            }
        )
    );
};

export const syncFavorites = () => (dispatch, getState) => {
    const state: MSState = getState();
    const { result } = state.favorites;
    dispatch({ type: SYNC_FAVORITES_REQUEST });
    const promises = result.map(favorite => {
        return updateShow(favorite, dispatch);
    });
    return Promise.all(promises)
        .then(() => {
            dispatch(getMyCalendar());
            return dispatch({ type: SYNC_FAVORITES_SUCCESS });
        })
        .catch(() => {
            return dispatch({ type: SYNC_FAVORITES_ERROR });
        });
};
