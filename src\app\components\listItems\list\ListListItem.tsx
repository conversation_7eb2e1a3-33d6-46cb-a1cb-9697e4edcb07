import React, { useEffect, useState, useRef } from 'react';
import { ListItem } from 'react-native-elements';
import { useSelector, useDispatch } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { getListItems, deleteList } from 'app/app/redux/list/actions';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import getEntities from 'app/app/redux/entities/selectors';
import _ from 'lodash';
import { useNavigation } from 'react-navigation-hooks';
import { ListShowNavParams } from 'app/views/list/shows';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { Animated } from 'react-native';
import { common } from 'app/app/styles/themes';
import i18n from 'i18n-js';
import { ManageNavParams } from 'app/views/list/manage';
import ListImages from '../../show/ListImages';
import SwipeButtons from '../../elements/SwipeButtons';
import Confirm from '../../containers/Confirm';
import ScreenLoader from '../../loaders/ScreenLoader';

export interface ListListItemProps {
    id: number;
}

const ListListItem = ({ id }: ListListItemProps) => {
    const { navigate } = useNavigation();
    const dispatch = useDispatch();
    const [confirm, setConfirm] = useState(false);
    const swipeable = useRef<Swipeable>();
    const { list, shows, fetched, loading } = useSelector((state: MSState) => {
        const list = state.entities.list[id];
        const { result: ids, fetched } = state.lists.listItems[id] || { result: [] };
        const shows = getEntities(state, ids, 'show') as Api.Entity<Trakt.Show>[];
        const { loading } = state.meta.form;
        return { list, shows, fetched, loading };
    }, _.isEqual);
    const {
        attributes: { name, description }
    } = list || { attributes: {} };
    useEffect(() => {
        if (ApiHelper.shouldFetch(fetched, UpdateThresholds.HIGH)) {
            const params: RequestParams = {
                extended: 'full'
            };
            dispatch(getListItems(id, params));
        }
    }, []);
    const ids = shows.map(show => show.attributes.ids);
    const leftElement = <ListImages {...{ id, ids }} />;
    const onPress = () => {
        const params: ListShowNavParams = { id };
        navigate(Constants.Navigation.Lists.SHOWS, params);
    };
    const onEdit = () => {
        const params: ManageNavParams = { list };
        navigate(Constants.Navigation.Lists.MANAGE, params);
    };
    const onDelete = () => {
        setConfirm(!confirm);
        dispatch(deleteList(id));
    };
    const onConfirm = () => {
        setConfirm(!confirm);
    };
    const onAfterPress = () => {
        swipeable.current?.close();
    };

    const renderRightActions = (progress: Animated.AnimatedInterpolation, dragX: Animated.AnimatedInterpolation) => {
        return (
            <SwipeButtons
                {...{
                    dragX,
                    buttons: [
                        {
                            label: i18n.t('DELETE'),
                            onPress: onConfirm,
                            iconProps: { name: 'trash' },
                            backgroundColor: common.red
                        },
                        {
                            label: i18n.t('EDIT'),
                            onPress: onEdit,
                            iconProps: { name: 'edit' },
                            backgroundColor: common.blue
                        }
                    ],
                    onAfterPress
                }}
            />
        );
    };
    return (
        <Swipeable ref={swipeable} renderRightActions={renderRightActions}>
            <ListItem
                {...{ onPress, leftElement, title: name, subtitle: description }}
                chevron
                badge={{ value: shows.length }}
            />
            <Confirm
                {...{
                    isVisible: confirm,
                    confirmation: i18n.t('DELETE_LIST_CONFIRMATION'),
                    title: i18n.t('DELETE_LIST'),
                    onConfirm: onDelete,
                    onCancel: onConfirm
                }}
            />
            {loading && <ScreenLoader />}
        </Swipeable>
    );
};

export default React.memo(ListListItem, _.isEqual);
