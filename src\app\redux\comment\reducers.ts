import { combineReducers } from 'redux';
import _ from 'lodash';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import { RESET_STORE } from '../app/actions';
import {
    CommentAction,
    GET_SHOW_COMMENTS_ERROR,
    GET_SHOW_COMMENTS_REQUEST,
    GET_SHOW_COMMENTS_SUCCESS,
    GET_REPLIES_REQUEST,
    GET_REPLIES_SUCCESS,
    GET_REPLIES_ERROR,
    GET_SEASON_COMMENTS_REQUEST,
    GET_SEASON_COMMENTS_SUCCESS,
    GET_SEASON_COMMENTS_ERROR,
    GET_EPISODE_COMMENTS_REQUEST,
    GET_EPISODE_COMMENTS_SUCCESS,
    GET_EPISODE_COMMENTS_ERROR
} from './actions';

export interface ShowCommnetsState {
    loading: boolean;
    loadingMore: boolean;
    [key: number]: {
        result: number[];
        main: number[];
        headers: Api.TraktHeaders;
    };
}
export const initialShowCommentsState: ShowCommnetsState = {
    loading: false,
    loadingMore: false
};

export function show(state: ShowCommnetsState = initialShowCommentsState, action: CommentAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialShowCommentsState;
        case GET_SHOW_COMMENTS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }
        case GET_SHOW_COMMENTS_SUCCESS: {
            const { id, normalized, headers, append, main } = action.payload;
            const result = !main ? normalized.result : [...(state[id]?.result || [])];
            const mainResult = main ? normalized.result : [...(state[id]?.main || [])];
            if (append) {
                return {
                    ...state,
                    [id]: { result: _.union(state[id].result, result), headers, main: mainResult },
                    loading: false,
                    loadingMore: false
                };
            }
            return {
                ...state,
                [id]: {
                    result,
                    headers,
                    main: mainResult
                },
                loading: false
            };
        }
        case GET_SHOW_COMMENTS_ERROR: {
            return { ...state, loading: false, loadingMore: false };
        }

        default:
            return state;
    }
}

export interface SeasonCommentsState {
    loading: boolean;
    loadingMore: boolean;
    [key: number]: {
        [key: number]: {
            result: number[];
            main: number[];
            headers: Api.TraktHeaders;
        };
    };
}
export const initialSeasonCommentsState: SeasonCommentsState = {
    loading: false,
    loadingMore: false
};

export function season(state: SeasonCommentsState = initialSeasonCommentsState, action: CommentAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialSeasonCommentsState;
        case GET_SEASON_COMMENTS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }
        case GET_SEASON_COMMENTS_SUCCESS: {
            const { id, normalized, headers, append, main, seasonNumber } = action.payload;
            const result = !main ? normalized.result : [...(state[id]?.[seasonNumber]?.result || [])];
            const mainResult = main ? normalized.result : [...(state[id]?.[seasonNumber]?.main || [])];
            if (append) {
                return {
                    ...state,
                    [id]: {
                        ...state[id],
                        [seasonNumber]: {
                            result: _.union(state[id][seasonNumber].result, result),
                            headers,
                            main: mainResult
                        }
                    },
                    loading: false,
                    loadingMore: false
                };
            }
            return {
                ...state,
                [id]: {
                    ...state[id],
                    [seasonNumber]: {
                        result,
                        headers,
                        main: mainResult
                    }
                },
                loading: false
            };
        }
        case GET_SEASON_COMMENTS_ERROR: {
            return { ...state, loading: false };
        }

        default:
            return state;
    }
}

export interface EpisodeCommentsState {
    loading: boolean;
    loadingMore: boolean;
    [key: number]: {
        [key: number]: {
            [key: number]: {
                result: number[];
                main: number[];
                headers: Api.TraktHeaders;
            };
        };
    };
}
export const initialEpisodeCommentsState: EpisodeCommentsState = {
    loading: false,
    loadingMore: false
};

export function episode(state: EpisodeCommentsState = initialEpisodeCommentsState, action: CommentAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialEpisodeCommentsState;
        case GET_EPISODE_COMMENTS_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }
        case GET_EPISODE_COMMENTS_SUCCESS: {
            const { id, normalized, headers, append, main, seasonNumber, episodeNumber } = action.payload;
            const result = !main ? normalized.result : [...(state[id]?.[seasonNumber]?.[episodeNumber]?.result || [])];
            const mainResult = main ? normalized.result : [...(state[id]?.[seasonNumber]?.[episodeNumber]?.main || [])];
            if (append) {
                return {
                    ...state,
                    [id]: {
                        ...state[id],
                        [seasonNumber]: {
                            ...state[id][seasonNumber],
                            [episodeNumber]: {
                                result: _.union(state[id][seasonNumber][episodeNumber].result, result),
                                headers,
                                main: mainResult
                            }
                        }
                    },
                    loading: false,
                    loadingMore: false
                };
            }
            return {
                ...state,
                [id]: {
                    ...state[id],
                    [seasonNumber]: {
                        [episodeNumber]: {
                            result,
                            headers,
                            main: mainResult
                        }
                    }
                },
                loading: false
            };
        }
        case GET_EPISODE_COMMENTS_ERROR: {
            return { ...state, loading: false };
        }

        default:
            return state;
    }
}

export interface RepliesState {
    loading: boolean;
    loadingMore: boolean;
    [key: number]: {
        result: number[];
        headers: Api.TraktHeaders;
    };
}
export const initialRepliesState: RepliesState = {
    loading: false,
    loadingMore: false
};

export function replies(state: RepliesState = initialRepliesState, action: CommentAction) {
    switch (action.type) {
        case SIGN_OUT_SUCCESS:
        case RESET_STORE:
            return initialRepliesState;
        case GET_REPLIES_REQUEST: {
            const { append } = action.payload;
            return { ...state, loading: !append, loadingMore: append };
        }
        case GET_REPLIES_SUCCESS: {
            const { id, normalized, headers, append } = action.payload;
            if (append) {
                return {
                    ...state,
                    [id]: { result: _.union(state[id].result, normalized.result), headers },
                    loading: false,
                    loadingMore: false
                };
            }
            return {
                ...state,
                [id]: {
                    result: normalized.result,
                    headers
                },
                loading: false
            };
        }
        case GET_REPLIES_ERROR: {
            return { ...state, loading: false };
        }
        default:
            return state;
    }
}

export interface CommentsState {
    episode: EpisodeCommentsState;
    replies: RepliesState;
    season: SeasonCommentsState;
    show: ShowCommnetsState;
}

export const initialCommentsState: CommentsState = {
    episode: initialEpisodeCommentsState,
    replies: initialRepliesState,
    season: initialSeasonCommentsState,
    show: initialShowCommentsState
};
export default combineReducers({
    episode,
    replies,
    season,
    show
});
