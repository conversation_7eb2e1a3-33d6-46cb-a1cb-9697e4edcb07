import React, { useContext, useRef } from 'react';
import { ListItem, ThemeContext, ThemeProps, Icon } from 'react-native-elements';
import { View, StyleSheet, ImageURISource, Animated } from 'react-native';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ShowViewNavParams } from 'app/views/show/view';
import Constants from 'app/app/Constants';
import _ from 'lodash';
import { useNavigation } from 'react-navigation-hooks';
import withShowFetch, { ShowFetchProps } from 'app/app/hocs/withShowFetch';
import { Theme, common, alpha } from 'app/app/styles/themes';
import { useDispatch } from 'react-redux';
import { removeFromFavorites, addToFavorites } from 'app/app/redux/favorite/actions';
import useFavorite from 'app/app/hooks/useFavorite';
import i18n from 'i18n-js';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import Text from '../../elements/Text';
import Image from '../../elements/Image';
import SwipeButtons, { SwipeButton } from '../../elements/SwipeButtons';

export const IMAGE_HEIGHT = 75;
export const DEFAULT_RATIO = 0.66;
export const ITEM_HEIGHT = 104;
const styles = StyleSheet.create({
    container: {
        height: ITEM_HEIGHT
    },
    titleContainer: {
        flexDirection: 'row'
    },
    subtitle: {
        flexDirection: 'row'
    },
    image: {
        height: IMAGE_HEIGHT,
        borderWidth: 1
    },
    favorite: {
        position: 'absolute',
        top: -3,
        right: 4,
        paddingHorizontal: 2,
        paddingBottom: 2,
        paddingTop: 5,
        borderColor: alpha(common.black, 0.3),
        borderWidth: 1,
        borderRadius: 3,
        backgroundColor: common.white
    }
});
export type ItemType = 'card' | 'list';
export interface ShowListItemProps {
    trakt: number;
    rightElement?: JSX.Element | string;
    useSortTitle: boolean;
    swipeEnabled: boolean;
    extraSwipeButtons: SwipeButton[];
}
type Props = ShowListItemProps & ShowFetchProps;
export const leftElement = (uri: ImageURISource | number, width: number, isFavorite: boolean) => (
    <View>
        <Image {...{ width, uri, height: IMAGE_HEIGHT }} />
        {isFavorite && (
            <View {...{ style: styles.favorite }}>
                <Icon {...{ size: 6, name: 'ios-tv', type: 'ionicon', color: common.green }} />
            </View>
        )}
    </View>
);

const ShowListItem = ({ trakt, rightElement, useSortTitle, show, image, swipeEnabled, extraSwipeButtons }: Props) => {
    // @ts-ignore
    const { push } = useNavigation();
    const dispatch = useDispatch();
    const isFavorite = useFavorite(trakt);
    const swipeable = useRef<Swipeable>(null);
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    if (!show) {
        return null;
    }
    const { title, year, network, status, ids } = show?.attributes;
    const [label, color] = ShowHelper.getStatus(status);

    const uri = ShowHelper.getPosterUri(image?.file_path, 20, scheme);

    const width = IMAGE_HEIGHT * 0.66;

    const titleYear = year ? year.toString() : '-';
    const titleNetwork = network ? ` / ${network}` : '';
    const subtitle = (
        <View style={styles.subtitle}>
            <Text>{`${titleYear} ${titleNetwork}`}</Text>
        </View>
    );

    const onPress = () => {
        const params: ShowViewNavParams = { ids };
        push(Constants.Navigation.Show.VIEW, params);
    };
    const renderRightActions = (progress: Animated.AnimatedInterpolation, dragX: Animated.AnimatedInterpolation) => {
        const onRemove = () => {
            dispatch(removeFromFavorites(trakt));
        };
        const onAdd = () => {
            const poster: string = image?.file_path || null;
            dispatch(addToFavorites(show, poster));
        };
        const defaultButtons = [
            {
                onPress: isFavorite ? onRemove : onAdd,
                label: isFavorite ? i18n.t('REMOVE_FAV') : i18n.t('ADD_FAV'),
                iconProps: {
                    name: 'ios-tv',
                    type: 'ionicon',
                    color: common.white
                },
                backgroundColor: isFavorite ? common.red : common.green
            }
        ];
        const buttons = _.concat(extraSwipeButtons, defaultButtons);
        const onAfterPress = () => {
            swipeable.current?.close();
        };
        return (
            <SwipeButtons
                {...{
                    dragX,
                    buttons,
                    onAfterPress
                }}
            />
        );
    };

    const item = (
        <ListItem
            containerStyle={{ ...styles.container }}
            leftElement={leftElement(uri, width, isFavorite)}
            title={
                <View style={styles.titleContainer}>
                    <Text numberOfLines={1}>{useSortTitle ? ShowHelper.getSortTitle(title) : title}</Text>
                </View>
            }
            subtitle={
                <View>
                    {subtitle}
                    <Text color={color}>{`${label}`}</Text>
                </View>
            }
            rightTitle={rightElement}
            chevron
            onPress={onPress}
        />
    );

    return swipeEnabled ? (
        <Swipeable ref={swipeable} renderRightActions={renderRightActions}>
            {item}
        </Swipeable>
    ) : (
        item
    );
};
ShowListItem.defaultProps = {
    useSortTitle: false,
    swipeEnabled: true,
    extraSwipeButtons: []
};
export default withShowFetch(React.memo(ShowListItem, _.isEqual));
