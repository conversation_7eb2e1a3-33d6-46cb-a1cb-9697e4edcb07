import React, { useContext } from 'react';
import { Platform, Dimensions, View } from 'react-native';
import { Overlay, ThemeContext, ThemeProps } from 'react-native-elements';
import StarRating from 'react-native-star-rating';
import { Theme, common } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import { ACTION_BAR_HEIGHT } from '../ActionsBar';
import styles from '../styles';

const { width } = Dimensions.get('window');
export interface ActionRatingProps {
    coverImageHeight: number;
    openRating: boolean;
    rating: number;
    onHeartPress: () => void;
    onRateChange: (rating: number) => void;
}

const ActionRating = ({ onHeartPress, coverImageHeight, openRating, rating, onRateChange }: ActionRatingProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    return (
        <Overlay
            overlayStyle={{
                ...styles.overlay,
                top: coverImageHeight - (Platform.OS === 'android' ? 3 : 2) * ACTION_BAR_HEIGHT,
                width
            }}
            isVisible={openRating}
            animationType="fade"
            onBackdropPress={onHeartPress}
        >
            <View
                style={{
                    ...styles.rateContainer
                }}
            >
                <StarRating
                    fullStar="heart"
                    emptyStar="heart"
                    emptyStarColor={common.white}
                    fullStarColor={colors.primary}
                    starSize={(width - SPACING.medium * 6) / 10}
                    disabled={false}
                    containerStyle={{
                        width,
                        paddingHorizontal: SPACING.medium,
                        paddingBottom: SPACING.large
                    }}
                    maxStars={10}
                    rating={rating || 6}
                    selectedStar={onRateChange}
                />
            </View>
        </Overlay>
    );
};

export default React.memo(ActionRating);
