import React from 'react';
import { FlatList } from 'react-native';
import NextEpisodeListItem from 'app/app/components/listItems/episode/NextEpisodeListItem';
import ArrayHelper from 'app/services/helpers/ArrayHelper';
import EmptyResults from './EmptyResults';

export interface UpcomingProps {
    data: MS.NextEpisode[];
    refreshControl: JSX.Element;
}
const renderItem = ({ item: nextEpisode }: { item: MS.NextEpisode }) => {
    return <NextEpisodeListItem {...{ episode: nextEpisode }} />;
};
const empty = <EmptyResults {...{ type: 'upcoming' }} />;
const Upcoming = ({ data, refreshControl }: UpcomingProps) => {
    const sorted = ArrayHelper.sortArray(data, item => item.airedDate);
    return (
        <FlatList<MS.NextEpisode>
            data={sorted}
            keyExtractor={item => item.episodeIds.tmdb.toString()}
            renderItem={renderItem}
            refreshControl={refreshControl}
            ListEmptyComponent={empty}
        />
    );
};

export default React.memo(Upcoming);
