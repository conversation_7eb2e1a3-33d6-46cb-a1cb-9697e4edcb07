import apiClient from 'app/services/api/client';
import { normalize } from 'normalizr';
import { Action } from 'redux';
import { OMDBRatingsSchema } from '../schemas';

export const GET_IMDB_RATING_REQUEST = 'GET_IMDB_RATING_REQUEST';
export const GET_IMDB_RATING_SUCCESS = 'GET_IMDB_RATING_SUCCESS';
export const GET_IMDB_RATING_ERROR = 'GET_IMDB_RATING_ERROR';

export interface RatingAction extends Action {
    payload: {
        normalized: Api.NormalizedResponse<OMDB.Rating>;
        id: number;
    };
}
export const getRating = (imdb: string) => {
    return apiClient(
        [
            GET_IMDB_RATING_REQUEST,
            {
                type: GET_IMDB_RATING_SUCCESS,
                payload: (action, state, res) => {
                    return res.json().then(json => {
                        const normalized =
                            json.Response === 'False'
                                ? { entities: {}, result: [] }
                                : normalize(json, OMDBRatingsSchema);
                        return { normalized, imdb };
                    });
                }
            },
            GET_IMDB_RATING_ERROR
        ],
        `/`,
        'GET',
        'omdb',
        { i: imdb }
    );
};
