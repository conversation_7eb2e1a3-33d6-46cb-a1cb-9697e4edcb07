import _ from 'lodash';
import { combineReducers } from 'redux';
import { RESET_ENTITIES } from './actions';
import { RESET_STORE } from '../app/actions';
import { SIGN_OUT_SUCCESS } from '../auth/actions';
import { SearchAction, SEARCH_SUCCESS } from '../search/actions';
import { GET_SHOW_IMAGES_SUCCESS } from '../image/actions';
import {
    GET_TRENDING_SHOWS_SUCCESS,
    GET_POPULAR_SHOWS_SUCCESS,
    GET_RECOMMENDED_SHOWS_SUCCESS,
    GET_WATCHED_SHOWS_SUCCESS,
    GET_SHOW_DETAILS_SUCCESS,
    GET_SIMILAR_SHOWS_SUCCESS,
    ShowAction,
    GET_ANTICIPATED_SHOWS_SUCCESS,
    GET_SHOW_SUCCESS
} from '../show/actions';
import { GET_SEASON_DETAILS_SUCCESS, SeasonAction } from '../season/actions';
import { GET_EPISODE_DETAILS_SUCCESS, EpisodeAction, GET_TRAKT_EPISODES_SUCCESS } from '../episode/actions';
import { GET_IMDB_RATING_SUCCESS } from '../rating/actions';
import { GET_FAVORITES_SUCCESS, FavoriteAction } from '../favorite/actions';
import { GET_LISTS_SUCCESS, ListsAction, GET_LIST_ITEMS_SUCCESS } from '../list/actions';
import { GET_PERSON_EX_IDS_SUCCESS, PersonAction } from '../person/actions';
import { SyncAction, GET_MY_CALENDAR_SUCCESS } from '../sync/actions';
import { GET_CERTIFICATIONS_SUCCESS, MetaAction, GET_GENRES_SUCCESS } from '../meta/actions';
import {
    GET_SHOW_COMMENTS_SUCCESS,
    CommentAction,
    GET_REPLIES_SUCCESS,
    POST_COMMENT_SUCCESS,
    POST_COMMENT_REPLY_SUCCESS,
    UPDATE_COMMENT_SUCCESS,
    GET_SEASON_COMMENTS_SUCCESS,
    GET_EPISODE_COMMENTS_SUCCESS
} from '../comment/actions';
import { GET_SHOW_PROGRESS_SUCCESS, GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS } from '../sync/watch';
import { GET_LATEST_RATINGS_SUCCESS, UserAction } from '../user/actions';

const reset = [SIGN_OUT_SUCCESS, RESET_STORE, RESET_ENTITIES];
const actions = [
    GET_POPULAR_SHOWS_SUCCESS,
    GET_TRENDING_SHOWS_SUCCESS,
    GET_RECOMMENDED_SHOWS_SUCCESS,
    GET_WATCHED_SHOWS_SUCCESS,
    GET_SHOW_DETAILS_SUCCESS,
    GET_SHOW_IMAGES_SUCCESS,
    SEARCH_SUCCESS,
    GET_SIMILAR_SHOWS_SUCCESS,
    GET_SEASON_DETAILS_SUCCESS,
    GET_EPISODE_DETAILS_SUCCESS,
    GET_IMDB_RATING_SUCCESS,
    GET_FAVORITES_SUCCESS,
    GET_LISTS_SUCCESS,
    GET_LIST_ITEMS_SUCCESS,
    GET_PERSON_EX_IDS_SUCCESS,
    GET_SHOW_PROGRESS_SUCCESS,
    GET_MY_CALENDAR_SUCCESS,
    GET_CERTIFICATIONS_SUCCESS,
    GET_GENRES_SUCCESS,
    GET_SHOW_COMMENTS_SUCCESS,
    GET_REPLIES_SUCCESS,
    GET_ANTICIPATED_SHOWS_SUCCESS,
    POST_COMMENT_SUCCESS,
    POST_COMMENT_REPLY_SUCCESS,
    UPDATE_COMMENT_SUCCESS,
    GET_SEASON_COMMENTS_SUCCESS,
    GET_TRAKT_EPISODES_SUCCESS,
    GET_EPISODE_COMMENTS_SUCCESS,
    GET_LATEST_RATINGS_SUCCESS,
    GET_WATCHED_HISTORY_FOR_FAVS_SUCCESS,
    GET_SHOW_SUCCESS
];

type Entities =
    | 'show'
    | 'images'
    | 'showDetails'
    | 'seasonDetails'
    | 'episode'
    | 'episodeDetails'
    | 'episodeRating'
    | 'rating'
    | 'favorite'
    | 'list'
    | 'externalIds'
    | 'watchedItem'
    | 'nextEpisode'
    | 'certification'
    | 'genre'
    | 'comment';
export type StateEntities<T = any> = {
    [key in Entities]: T;
};

export type EntitiesState = {
    certification: { [key: string]: Api.Entity<Trakt.Certification> };
    comment: { [key: string]: Api.Entity<Trakt.Comment> };
    episode: { [key: number]: Api.Entity<Trakt.Episode> };
    episodeDetails: { [key: number]: Api.Entity<TMDB.EpisodeDetails> };
    episodeRating: { [key: number]: Api.Entity<Trakt.EpisodeRating> };
    externalIds: { [key: number]: Api.Entity<TMDB.ExternalIds> };
    favorite: { [key: number]: Api.Entity<MS.Favorite> };
    genre: { [key: string]: Api.Entity<Trakt.Genre> };
    image: { [key: number]: Api.Entity<TMDB.ShowImages> };
    list: { [key: number]: Api.Entity<Trakt.CustomList> };
    nextEpisode: { [key: number]: Api.Entity<Trakt.NextEpisode> };
    rating: { [key: string]: Api.Entity<OMDB.Rating> };
    seasonDetails: { [key: number]: Api.Entity<TMDB.SeasonDetails> };
    show: { [key: number]: Api.Entity<Trakt.Show, Trakt.ShowMeta> };
    showDetails: { [key: number]: Api.Entity<TMDB.ShowDetails> };
    watchedItem: { [key: number]: Api.Entity<Trakt.WatchedItem> };
};

export const initialEntitiesState: EntitiesState = {
    certification: {},
    comment: {},
    episode: {},
    episodeDetails: {},
    episodeRating: {},
    externalIds: {},
    favorite: {},
    genre: {},
    image: {},
    list: {},
    nextEpisode: {},
    rating: {},
    seasonDetails: {},
    show: {},
    showDetails: {},
    watchedItem: {}
};

type EntityAction =
    | CommentAction
    | EpisodeAction
    | FavoriteAction
    | ListsAction
    | MetaAction
    | PersonAction
    | SearchAction
    | SeasonAction
    | ShowAction
    | SyncAction
    | UserAction;

const show = (state = { show: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.show) {
            return _.mergeWith({}, state, action.payload.normalized.entities.show);
        }
    }
    return state;
};

const showDetails = (state = { showDetails: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.showDetails) {
            return _.mergeWith({}, state, action.payload.normalized.entities.showDetails);
        }
    }
    return state;
};

const seasonDetails = (state = { showDetails: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.seasonDetails) {
            return _.mergeWith({}, state, action.payload.normalized.entities.seasonDetails);
        }
    }
    return state;
};

const episodeDetails = (state = { episodeDetails: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.episodeDetails) {
            return _.mergeWith({}, state, action.payload.normalized.entities.episodeDetails);
        }
    }
    return state;
};

const image = (state = { images: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.image) {
            return _.mergeWith({}, state, action.payload.normalized.entities.image);
        }
    }
    return state;
};

const rating = (state = { rating: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.rating) {
            return _.mergeWith({}, state, action.payload.normalized.entities.rating);
        }
    }
    return state;
};

const favorite = (state = { favorite: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (action.type === GET_FAVORITES_SUCCESS) {
        if (action.payload.normalized.entities.favorite) {
            const state = action.payload.normalized.entities.favorite as { [key: number]: Api.Entity<MS.Favorite> };
            return state;
        }
        return {};
    }
    return state;
};
const list = (state = { show: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.list) {
            return _.mergeWith({}, state, action.payload.normalized.entities.list);
        }
    }
    return state;
};
const externalIds = (state = { externalIds: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.externalIds) {
            return _.mergeWith({}, state, action.payload.normalized.entities.externalIds);
        }
    }
    return state;
};
const watchedItem = (state = { watchedItem: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.watchedItem) {
            return _.mergeWith({}, state, action.payload.normalized.entities.watchedItem);
        }
    }
    return state;
};
const nextEpisode = (state = { nextEpisode: {} }, action: EntityAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.nextEpisode) {
            return _.mergeWith({}, state, action.payload.normalized.entities.nextEpisode);
        }
    }
    return state;
};
const certification = (state = { certification: {} }, action: MetaAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.certification) {
            return action.payload.normalized.entities.certification;
        }
    }
    return state;
};
const genre = (state = { genre: {} }, action: MetaAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.genre) {
            return action.payload.normalized.entities.genre;
        }
    }
    return state;
};
const comment = (state = { comment: {} }, action: CommentAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.comment) {
            return _.mergeWith({}, state, action.payload.normalized.entities.comment);
        }
    }
    return state;
};
const episode = (state = { episode: {} }, action: EpisodeAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.episode) {
            return _.mergeWith({}, state, action.payload.normalized.entities.episode);
        }
    }
    return state;
};

const episodeRating = (state = { episodeRating: {} }, action: UserAction) => {
    if (reset.includes(action.type)) {
        return {};
    }
    if (actions.includes(action.type)) {
        if (action.payload.normalized.entities.episodeRating) {
            return _.mergeWith({}, state, action.payload.normalized.entities.episodeRating);
        }
    }
    return state;
};

export default combineReducers({
    certification,
    comment,
    episode,
    episodeDetails,
    episodeRating,
    externalIds,
    favorite,
    genre,
    image,
    list,
    nextEpisode,
    rating,
    seasonDetails,
    show,
    showDetails,
    watchedItem
});
