import React, { useState, useEffect, useContext } from 'react';
import { StyleSheet, Image, Dimensions, ImageURISource, View, ScrollView } from 'react-native';
import { Overlay, Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { SPACING, ICON_SIZE } from 'app/app/styles/sizes';
import { Theme, common } from 'app/app/styles/themes';
import Text from '../elements/Text';

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
    overlay: {
        padding: SPACING.medium,
        borderRadius: 10,
        width: width - SPACING.medium * 2,
        height: 'auto',
        maxHeight: height - 160
    },
    title: {
        paddingVertical: SPACING.normal
    },
    icon: {
        position: 'absolute',
        right: 5,
        top: 5
    }
});

export interface HelpProps {
    title: string;
    image: ImageURISource;
    text: string;
    textElement: JSX.Element;
    isVisible: boolean;
    imageRatio: number;
    delay: number;
}

const Help = ({ title, isVisible, image, text, imageRatio, delay, textElement }: HelpProps) => {
    const [visible, setVisible] = useState(isVisible);
    const {
        theme: { colors, scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    useEffect(() => {
        setTimeout(() => {
            setVisible(isVisible);
        }, delay);
    }, [isVisible]);
    const onClose = () => {
        setVisible(false);
    };
    const imageWidth = width - SPACING.medium * 4;
    const backdrop = {
        backgroundColor: scheme === 'dark' ? common.white : common.black,
        opacity: scheme === 'dark' ? 0.2 : 0.7
    };
    return (
        <Overlay
            {...{
                isVisible: visible,
                overlayStyle: styles.overlay,
                onBackdropPress: onClose,
                onDismiss: onClose,
                animationType: 'slide',
                backdropStyle: backdrop
            }}
        >
            <>
                <Icon
                    {...{
                        onPress: onClose,
                        name: 'close',
                        size: ICON_SIZE.normal,
                        containerStyle: {
                            ...styles.icon,
                            backgroundColor: colors.background,
                            borderColor: colors.discreet
                        }
                    }}
                />
                <ScrollView>
                    <View>
                        {title && (
                            <Text style={{ ...styles.title }} centered textStyle="title">
                                {title}
                            </Text>
                        )}

                        {image && (
                            <Image
                                resizeMode="cover"
                                style={{
                                    width: imageWidth,
                                    height: imageWidth * imageRatio,
                                    marginVertical: SPACING.normal,
                                    marginBottom: SPACING.large
                                }}
                                source={image}
                            />
                        )}

                        {text && !textElement && <Text>{text}</Text>}
                        <View>{textElement}</View>
                    </View>
                </ScrollView>
            </>
        </Overlay>
    );
};

Help.defaultProps = {
    image: null,
    text: null,
    textElement: null,
    title: null,
    isVisible: false,
    imageRatio: 1,
    delay: 1000
};

export default React.memo(Help);
