import { createStackNavigator } from 'react-navigation-stack';
import { dark, light } from 'app/app/styles/themes';
import Watching from 'app/views/watching/view';
import Constants from 'app/app/Constants';
import Common from './Common';
import TransitionConfiguration from '../transitions';

export default createStackNavigator(
    {
        [Constants.Navigation.Watching.VIEW]: Watching,
        ...Common
    },
    {
        headerMode: 'none',
        defaultNavigationOptions: ({ theme }) => {
            const colors = theme === 'dark' ? dark : light;
            return { headerStyle: { backgroundColor: colors.background } };
        },
        transitionConfig: TransitionConfiguration
    }
);
