import React, { memo, forwardRef, useContext } from 'react';
import { ListItem, Input, InputProps, ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import { SPACING } from 'app/app/styles/sizes';
import emoji from 'node-emoji';
import styles from './styles';

export interface TextInputProps extends InputProps {
    useEmoji?: boolean;
}

const TextInput = (props: TextInputProps, ref) => {
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const { multiline, numberOfLines, useEmoji, value } = props;
    const height = multiline && numberOfLines > 1 ? SPACING.large * numberOfLines : SPACING.medium;
    const text = useEmoji ? emoji.emojify(value) : value;
    return (
        <ListItem
            title={
                <Input
                    keyboardAppearance={scheme as 'light' | 'dark' | 'default'}
                    ref={ref}
                    containerStyle={{ ...styles.inputField }}
                    {...props}
                    inputStyle={{ height }}
                    textAlignVertical="top"
                    value={text}
                />
            }
            containerStyle={{ ...styles.fieldContainer }}
        />
    );
};

export default memo(forwardRef(TextInput));
