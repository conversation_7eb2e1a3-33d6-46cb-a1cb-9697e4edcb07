import React from 'react';
import { StyleSheet, View } from 'react-native';
import _ from 'lodash';
import { SPACING } from 'app/app/styles/sizes';
import i18n from 'i18n-js';
import { common } from 'app/app/styles/themes';
import Tag from '../containers/Tag';
import Text from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: SPACING.medium
    },
    tag: {
        borderWidth: 1,
        borderRadius: 10,
        paddingHorizontal: SPACING.normal
    }
});

export interface NetworksProps {
    networks: TMDB.Network[];
    network: string;
}

const Networks = ({ networks, network }: NetworksProps) => {
    return (
        <View style={{ ...styles.container }}>
            {networks ? (
                networks.map(network => {
                    const { name } = network;
                    return (
                        <Tag
                            {...{
                                key: name,
                                label: name,
                                backgroundColor: common.blue
                            }}
                        />
                    );
                })
            ) : network ? (
                <Tag {...{ key: network, label: network, backgroundColor: common.blue }} />
            ) : (
                <Text> {i18n.t('NO_NETWORKS')}</Text>
            )}
        </View>
    );
};

export default React.memo(Networks, _.isEqual);
