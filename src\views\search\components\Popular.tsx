import React, { useEffect } from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { popularShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Teaser from 'app/app/components/containers/Teaser';
import SmallRating from 'app/app/components/show/SmallRating';
import _ from 'lodash';

export interface PopularProps {}

type Props = PopularProps & NavigationInjectedProps<{}>;
const Popular = ({ navigation }: Props) => {
    const dispatch = useDispatch();
    const { result, headers, fetched } = useSelector((state: MSState) => {
        const { popular } = state.shows;
        return {
            result: popular.result.slice(0, Constants.App.LIST_TEASER_ITEMS),
            headers: state.shows.popular.headers,
            fetched: state.shows.popular.fetched
        };
    }, _.isEqual);
    useEffect(() => {
        if (ApiHelper.shouldFetch(fetched, UpdateThresholds.LOWER)) {
            const params: RequestParams = {
                extended: 'full',
                limit: 20
            };
            dispatch(popularShows(params));
        }
    }, []);

    const renderItem = (trakt: number) => {
        return (
            <ShowListItem
                {...{
                    key: trakt,
                    trakt,
                    rightElement: <SmallRating {...{ trakt }} />
                }}
            />
        );
    };
    const { totalItems } = ApiHelper.getPagination(headers);
    const onPress = () => {
        navigation.navigate(Constants.Navigation.Search.POPULAR);
    };
    return (
        result.length > 0 && (
            <Teaser
                {...{
                    data: result,
                    onPress,
                    renderItem,
                    title: i18n.t('POPULAR_SHOWS'),
                    subtitle: i18n.t('POPULAR_SHOWS_SUB'),
                    showMore: totalItems > Constants.App.LIST_TEASER_ITEMS
                }}
            />
        )
    );
};

export default withNavigation(React.memo(Popular, () => true));
