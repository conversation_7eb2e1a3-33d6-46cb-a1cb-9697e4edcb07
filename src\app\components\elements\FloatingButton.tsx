import React, { useContext } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Icon, IconProps, ThemeContext, ThemeProps } from 'react-native-elements';
import { SPACING } from 'app/app/styles/sizes';
import { Theme } from 'app/app/styles/themes';

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        bottom: SPACING.large,
        right: SPACING.large,
        alignItems: 'center',
        justifyContent: 'center'
    }
});

export interface FloatingButtonProps {
    iconProps: IconProps;
    backgroundColor?: string;
}

const FloatingButton = ({ iconProps, backgroundColor }: FloatingButtonProps) => {
    const defaultProps: Partial<IconProps> = {
        Component: TouchableOpacity,
        type: 'font-awesome',
        size: 50
    };
    const { name, size } = iconProps;
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    return (
        <View
            style={{
                ...styles.container,
                backgroundColor: backgroundColor || colors.onBackground,
                width: size || 50,
                height: size || 50,
                borderRadius: (size || 50) / 2
            }}
        >
            <Icon {...{ ...defaultProps, name, ...iconProps }} />
        </View>
    );
};

FloatingButton.defaultProps = {};

export default React.memo(FloatingButton);
