import React, { useContext, useEffect } from 'react';
import { View, Dimensions } from 'react-native';
import { alpha, Theme } from 'app/app/styles/themes';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { SPACING } from 'app/app/styles/sizes';
import StringHelper from 'app/services/helpers/StringHelper';
import Animated, { Easing } from 'react-native-reanimated';
import Text from '../elements/Text';
import styles, { BAR_WIDTH, TOTAL_WIDTH } from './styles';
import { BarChartValue } from './BarChart';

const { width: screenWidth } = Dimensions.get('window');
export interface BarProps {
    value: BarChartValue;
    perc: string;
}

const Bar = ({ value, perc }: BarProps) => {
    const width = new Animated.Value(0);
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    useEffect(() => {
        if (value.value > 0) {
            const toValue = (((screenWidth * BAR_WIDTH) / TOTAL_WIDTH) * parseInt(perc)) / 100;
            Animated.timing(width, {
                toValue,
                duration: 350,
                easing: Easing.ease
            }).start();
        }
    }, []);

    return (
        <View style={{ ...styles.barContainer }}>
            <Text style={{ ...styles.label }}>{value.label}</Text>
            <View style={{ ...styles.bar, borderColor: alpha(colors.primary, 0.4) }}>
                <Animated.View
                    style={{
                        ...styles.barContent,
                        backgroundColor: colors.primary,
                        width,
                        height: SPACING.medium
                    }}
                />
                <Text style={{ ...styles.perc }} textStyle="nano">{`${perc}%`}</Text>
            </View>
            <Text style={{ ...styles.value }}>{StringHelper.formatNumber(value.value)}</Text>
        </View>
    );
};

export default React.memo(Bar);
