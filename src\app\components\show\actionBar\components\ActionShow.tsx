import React, { useContext } from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Icon, ThemeContext, ThemeProps } from 'react-native-elements';
import { common, Theme } from 'app/app/styles/themes';
import { ICON_SIZE } from 'app/app/styles/sizes';
import styles from '../styles';

export interface ActionShowProps {
    onShowPress: () => void;
}

const ActionShow = ({ onShowPress }: ActionShowProps) => {
    const {
        theme: { scheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    return (
        <Icon
            Component={TouchableOpacity}
            name="tv"
            type="font-awesome"
            color={scheme === 'dark' ? common.white : common.black}
            containerStyle={{ ...styles.icon }}
            size={ICON_SIZE.medium}
            onPress={onShowPress}
        />
    );
};

export default React.memo(ActionShow);
