import { schema } from 'normalizr';
import _ from 'lodash';

const extraAttributes = [
    'rank',
    'listed_at',
    'watchers',
    'watcher_count',
    'play_count',
    'collected_count',
    'collector_count',
    'list_count'
];

const processStrategy = (value: any, key: string, id: number) => {
    const newValue = value[key] ? { ...value } : {};
    newValue.type = key;
    newValue.attributes = value[key] ? value[key] : value;
    newValue.meta = {};
    newValue.fetched = new Date().getTime();
    extraAttributes.forEach(attr => {
        if (newValue[attr] !== undefined) {
            newValue.meta[attr] = newValue[attr];
            delete newValue[attr];
        }
    });

    newValue.id = id;
    delete newValue[key];
    return newValue;
};

// normalizer schemas
export const TraktShowSchema = new schema.Entity(
    'show',
    {},
    {
        idAttribute: value => {
            return value.show ? value.show.ids.trakt : value.ids.trakt;
        },
        processStrategy: value => {
            const id = value.show ? value.show.ids.trakt : value.ids.trakt;
            return processStrategy(value, 'show', id);
        }
    }
);

export const TmdbImageSchema = new schema.Entity(
    'image',
    {},
    {
        processStrategy: value => {
            const newValue = {
                id: value.id,
                type: 'image',
                attributes: {
                    posters: value.posters,
                    backdrops: value.backdrops,
                    fetched: new Date().getTime()
                }
            };
            return newValue;
        }
    }
);

export const TmdbShowDetailsSchema = new schema.Entity(
    'showDetails',
    {},
    {
        processStrategy: value => {
            const copy = { ...value };
            let similar;
            if (copy.similar && _.isArray(copy.similar.results)) {
                similar = value.similar.results.slice(0, 7);
                delete copy.similar;
            }
            const newValue = {
                id: value.id,
                type: 'showDetails',
                attributes: { ...copy, similar, fetched: new Date().getTime() }
            };
            return newValue;
        }
    }
);

export const TmdbExternalIdsSchema = new schema.Entity(
    'externalIds',
    {},
    {
        processStrategy: value => {
            const copy = { ...value };
            const newValue = {
                id: value.id,
                type: 'externalIds',
                attributes: { ...copy, fetched: new Date().getTime() }
            };
            return newValue;
        }
    }
);

export const TmdbSeasonDetailsSchema = new schema.Entity(
    'seasonDetails',
    {},
    {
        processStrategy: value => {
            const copy = { ...value };
            const newValue = {
                id: value.id,
                type: 'seasonDetails',
                attributes: { ...copy, fetched: new Date().getTime() }
            };
            return newValue;
        }
    }
);

export const TmdbEpisodeDetailsSchema = new schema.Entity(
    'episodeDetails',
    {},
    {
        processStrategy: value => {
            const copy = { ...value };
            if (copy.credits) {
                delete copy.crew;
                delete copy.guest_stars;
            }

            const newValue = {
                id: value.id,
                type: 'episodeDetails',
                attributes: { ...copy, fetched: new Date().getTime() }
            };
            return newValue;
        }
    }
);

export const OMDBRatingsSchema = new schema.Entity(
    'rating',
    {},
    {
        idAttribute: value => {
            return value.imdbID;
        },
        processStrategy: value => {
            const newValue = {
                id: value.imdbID,
                type: 'rating',
                attributes: {
                    id: value.imdbID,
                    rating: parseFloat((value.imdbRating as string).replace(/,/g, '')),
                    votes: parseInt((value.imdbVotes as string).replace(/,/g, '')),
                    fetched: new Date().getTime()
                }
            };
            return newValue;
        }
    }
);

export const FavoritesSchema = new schema.Entity(
    'favorite',
    {},
    {
        idAttribute: value => {
            return value.trakt;
        },
        processStrategy: value => {
            const newValue = {
                id: value.trakt,
                type: 'favorite',
                attributes: { ...value }
            };
            return newValue;
        }
    }
);

export const TraktListsSchema = new schema.Entity(
    'list',
    {},
    {
        idAttribute: value => {
            return value.ids.trakt;
        },
        processStrategy: value => {
            const newValue = {
                id: value.ids.trakt,
                type: 'list',
                attributes: { ...value, fetched: new Date().getTime() }
            };
            return newValue;
        }
    }
);

export const TraktWatchedItem = new schema.Entity(
    'watchedItem',
    {},
    {
        idAttribute: value => {
            return value.id;
        },
        processStrategy: value => {
            const newValue = {
                id: value.id,
                type: 'watchedItem',
                attributes: { show: value.show.ids.trakt, season: value.episode.season, episode: value.episode.number }
            };
            return newValue;
        }
    }
);

export const nextEpisodeSchema = new schema.Entity(
    'nextEpisode',
    {},
    {
        idAttribute: value => {
            return value.ids.tmdb;
        },
        processStrategy: value => {
            const newValue = {
                id: value.ids.tmdb,
                type: 'nextEpisode',
                attributes: { ...value }
            };
            return newValue;
        }
    }
);

export const nextCalendarSchema = new schema.Entity(
    'nextEpisode',
    {},
    {
        idAttribute: value => {
            return value.episode.ids.tmdb;
        },
        processStrategy: value => {
            const attributes = { ...value.episode };
            delete attributes.episode;
            attributes.airDate = value.first_aired;
            attributes.show = value.show;
            const newValue = {
                id: value.episode.ids.tmdb,
                type: 'nextEpisode',
                attributes
            };
            return newValue;
        }
    }
);

export const CertificationSchema = new schema.Entity(
    'certification',
    {},
    {
        idAttribute: value => {
            return value.slug;
        },
        processStrategy: value => {
            const attributes = { ...value };
            const newValue = {
                id: value.slug,
                type: 'certification',
                attributes
            };
            return newValue;
        }
    }
);
export const GenreSchema = new schema.Entity(
    'genre',
    {},
    {
        idAttribute: value => {
            return value.slug;
        },
        processStrategy: value => {
            const attributes = { ...value };
            const newValue = {
                id: value.slug,
                type: 'genre',
                attributes
            };
            return newValue;
        }
    }
);

export const CommentsSchema = new schema.Entity(
    'comment',
    {},
    {
        idAttribute: value => {
            return value.id;
        },
        processStrategy: value => {
            const attributes = { ...value };
            const newValue = {
                id: value.id,
                type: 'comment',
                attributes
            };
            return newValue;
        }
    }
);

export const TraktEpisodeSchema = new schema.Entity(
    'episode',
    {},
    {
        idAttribute: value => {
            return value.ids.trakt;
        },
        processStrategy: value => {
            const attributes = { ...value };
            const newValue = {
                id: value.ids.trakt,
                type: 'episode',
                attributes
            };
            return newValue;
        }
    }
);

export const TraktEpisodesRating = new schema.Entity(
    'episodeRating',
    {},
    {
        idAttribute: value => {
            return value.episode.ids.trakt;
        },
        processStrategy: value => {
            const attributes = { ...value };
            const newValue = {
                id: value.episode.ids.trakt,
                type: 'episodeRating',
                attributes
            };
            return newValue;
        }
    }
);
