import React from 'react';
import ShowHelper from 'app/services/helpers/ShowHelper';
import { ListItem } from 'react-native-elements';
import i18n from 'i18n-js';
import { StyleSheet, View } from 'react-native';
import { common } from 'app/app/styles/themes';
import withExternalIdsFetch, { ExternalIdsFetchProps } from 'app/app/hocs/withExternalIdsFetch';
import _ from 'lodash';
import Image from '../../elements/Image';

const styles = StyleSheet.create({
    imageContainer: {
        backgroundColor: 'white'
    }
});
export const PERSON_IMAGE_WIDTH = 185 * 0.2;
export const PERSON_IMAGE_HEIGHT = 278 * 0.2;

export interface PersonListItemProps {
    id: number; // tmdb id
    name: string;
    character?: string;
    profile_path: string;
    department?: string;
    job?: string;
    gender: number;
}
type Props = PersonListItemProps & ExternalIdsFetchProps;

const PersonListItem = ({ name, character, profile_path, department, job, gender, onPress }: Props) => {
    const uri = ShowHelper.getProfileUri(profile_path, gender, 20);
    const subtitle = character ? `${i18n.t('AS')} ${character}` : `${department} ${i18n.t('DPT')} - ${job}`;
    return (
        <ListItem
            onPress={onPress}
            chevron
            title={name}
            subtitle={subtitle}
            leftElement={
                <View style={styles.imageContainer}>
                    <Image
                        {...{
                            uri,
                            width: PERSON_IMAGE_WIDTH,
                            height: PERSON_IMAGE_HEIGHT,
                            backgroundColor: common.white
                        }}
                    />
                </View>
            }
        />
    );
};

export default React.memo(withExternalIdsFetch(PersonListItem), _.isEqual);
