import React, { useState, useEffect, useContext } from 'react';
import { StyleSheet, View, Appearance, ColorSchemeName } from 'react-native';
import Text from 'app/app/components/elements/Text';
import Container from 'app/app/components/containers/Container';

import { useDispatch, useSelector } from 'react-redux';
import Storage from 'app/services/storage/Storage';
import { setScheme as setStateScheme, setLocale as setStateLocale } from 'app/app/redux/app/actions';
import { ThemeContext, Header, ListItem, ThemeProps } from 'react-native-elements';
import { createTheme, Theme } from 'app/app/styles/themes';
import i18n from 'i18n-js';
import { SPACING } from 'app/app/styles/sizes';
import English from 'app/assets/images/languages/english.svg';
import Greek from 'app/assets/images/languages/greek.svg';
import ClearButton from 'app/app/components/elements/ClearButton';
import { useNavigation } from 'react-navigation-hooks';
import Constants from 'app/app/Constants';
import { MSState } from 'app/app/redux/redux';
import LanguageButton from '../settings/view/components/LanguageButton';
import ModeButton from '../settings/view/components/ModeButton';

const styles = StyleSheet.create({
    container: {
        padding: SPACING.medium
    },
    button: {
        borderWidth: 1,
        width: 200,
        alignSelf: 'center',
        marginTop: SPACING.large
    }
});

export interface SelectSettingsProps {}

const SelectSettings = () => {
    const [scheme, setScheme] = useState(null);
    const [locale, setLocale] = useState(null);
    const dispatch = useDispatch();
    const { navigate } = useNavigation();

    const { appLocale } = useSelector((state: MSState) => {
        return {
            appLocale: state.app.locale
        };
    });

    const {
        replaceTheme,
        theme: { colors, scheme: appScheme }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    const changeLocale = (locale: MS.Locales) => {
        Storage.setItem(Storage.KEY_LOCALE, locale).then(() => {
            i18n.locale = locale;
            setLocale(locale);
            dispatch(setStateLocale(locale));
        });
    };

    const changeScheme = (scheme: ColorSchemeName) => {
        setScheme(appScheme);
        Storage.setItem(Storage.KEY_THEME, scheme).then(() => {
            dispatch(setStateScheme(scheme));
            const theme = createTheme(scheme);
            // Appearance.set({
            //    colorScheme: scheme
            // });
            replaceTheme(theme);
        });
    };

    useEffect(() => {
        changeScheme(appScheme || Appearance.getColorScheme());
        changeLocale(appLocale);
    }, [appScheme, appLocale]);

    const onPress = () => {
        Storage.setItem(Storage.KEY_BASIC_SETTINGS, 1).then(() => {
            navigate(Constants.Navigation.Auth.SIGN_IN);
        });
    };
    return (
        <Container withPadding={false}>
            <Header centerComponent={{ text: i18n.t('SET_BASIC_SETTINGS') }} />
            <View style={{ ...styles.container }}>
                <Text centered textStyle="title">
                    {i18n.t('SELECT_LANGUAGE')}
                </Text>
            </View>
            <ListItem
                onPress={() => changeLocale('en')}
                checkmark={locale === 'en'}
                leftElement={<LanguageButton size={32} Element={English} />}
                {...{ title: i18n.t('ENGLISH') }}
            />
            <ListItem
                onPress={() => changeLocale('el')}
                checkmark={locale === 'el'}
                leftElement={<LanguageButton size={32} Element={Greek} />}
                {...{ title: i18n.t('GREEK') }}
            />
            <View style={{ ...styles.container }}>
                <Text centered textStyle="title">
                    {i18n.t('SELECT_MODE')}
                </Text>
            </View>
            <ListItem
                onPress={() => changeScheme('light')}
                checkmark={scheme === 'light'}
                leftElement={<ModeButton mode="light" />}
                {...{ title: i18n.t('LIGHT') }}
            />
            <ListItem
                onPress={() => changeScheme('dark')}
                checkmark={scheme === 'dark'}
                leftElement={<ModeButton mode="dark" />}
                {...{ title: i18n.t('DARK') }}
            />
            <ClearButton
                {...{ onPress, title: i18n.t('OK'), style: { ...styles.button, borderColor: colors.primary } }}
            />
        </Container>
    );
};

export default React.memo(SelectSettings);
