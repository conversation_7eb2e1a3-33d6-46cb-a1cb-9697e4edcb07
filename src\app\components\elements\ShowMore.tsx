import React from 'react';
import { StyleSheet } from 'react-native';
import { ListItem } from 'react-native-elements';
import i18n from 'i18n-js';

const styles = StyleSheet.create({
    container: {
        borderBottomWidth: 0
    },
    rightContainer: {
        flex: 1
    }
});

export interface ShowMoreProps {
    onPress: () => void;
    title?: string;
}

const ShowMore = ({ title, onPress }: ShowMoreProps) => {
    return (
        <ListItem
            style={styles.rightContainer}
            onPress={onPress}
            chevron
            rightTitle={title || i18n.t('SHOW_MORE')}
            containerStyle={styles.container}
            rightContentContainerStyle={styles.rightContainer}
        />
    );
};

export default React.memo(ShowMore);
