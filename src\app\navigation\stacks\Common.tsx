import Constants from 'app/app/Constants';
import Show from 'views/show/view';
import Season from 'views/season/view';
import Episode from 'views/episode/view';
import People from 'views/people/view';
import Listed from 'views/show/listed';
import Comments from 'views/comment/list';
import Replies from 'app/views/comment/replies';
import Post from 'app/views/comment/post';
import Stats from 'views/stats/view';

export default {
    [Constants.Navigation.Show.VIEW]: {
        screen: Show,
        params: { noTabBar: true }
    },
    [Constants.Navigation.Season.VIEW]: {
        screen: Season,
        params: { noTabBar: true }
    },
    [Constants.Navigation.Episode.VIEW]: {
        screen: Episode,
        params: { noTabBar: true }
    },
    [Constants.Navigation.People.VIEW]: {
        screen: People,
        params: { noTabBar: true }
    },
    [Constants.Navigation.Show.LISTED]: {
        screen: Listed,
        params: { noTabBar: true, transition: 'slideUp' }
    },
    [Constants.Navigation.Comment.LIST]: {
        screen: Comments,
        params: { noTabBar: true, transition: 'slideUp' }
    },
    [Constants.Navigation.Comment.REPLIES]: {
        screen: Replies,
        params: { noTabBar: true, transition: 'slideUp' }
    },
    [Constants.Navigation.Comment.POST]: {
        screen: Post,
        params: { noTabBar: true, transition: 'slideUp' }
    },
    [Constants.Navigation.Stats.VIEW]: {
        screen: Stats,
        params: { noTabBar: true, transition: 'slideUp' }
    }
};
