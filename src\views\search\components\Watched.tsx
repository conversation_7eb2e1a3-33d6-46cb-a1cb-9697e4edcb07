import React, { useEffect } from 'react';
import { withNavigation, NavigationInjectedProps } from 'react-navigation';
import ShowListItem from 'app/app/components/listItems/show/ShowListItem';
import i18n from 'i18n-js';
import { useDispatch, useSelector } from 'react-redux';
import { watchedShows } from 'app/app/redux/show/actions';
import { MSState } from 'app/app/redux/redux';
import { RequestParams } from 'app/services/helpers/RequestHelper';
import Constants, { UpdateThresholds } from 'app/app/Constants';
import ApiHelper from 'app/services/helpers/ApiHelper';
import Teaser from 'app/app/components/containers/Teaser';
import Watchers from 'app/app/components/show/Watchers';
import _ from 'lodash';
import { View } from 'react-native';

export interface WatchedProps {}

type Props = WatchedProps & NavigationInjectedProps<{}>;
const Watched = ({ navigation }: Props) => {
    const dispatch = useDispatch();

    const { headers, result, fetched } = useSelector((state: MSState) => {
        const { watched } = state.shows;
        return {
            headers: watched.headers,
            result: watched.result.slice(0, Constants.App.LIST_TEASER_ITEMS),
            fetched: watched.fetched
        };
    }, _.isEqual);
    useEffect(() => {
        if (ApiHelper.shouldFetch(fetched, UpdateThresholds.LOW)) {
            const params: RequestParams = {
                extended: 'full',
                limit: 20
            };
            dispatch(watchedShows('weekly', params));
        }
    }, []);

    const renderItem = (trakt: number) => {
        return (
            <View {...{ key: trakt }}>
                <ShowListItem
                    {...{
                        trakt,
                        rightElement: (
                            <Watchers
                                {...{
                                    trakt,
                                    nFormatted: true,
                                    watchKey: 'watcher_count'
                                }}
                            />
                        )
                    }}
                />
            </View>
        );
    };
    const { totalItems } = ApiHelper.getPagination(headers);
    const onPress = () => {
        navigation.navigate(Constants.Navigation.Search.WATCHED);
    };
    return (
        result.length > 0 && (
            <Teaser
                {...{
                    data: result,
                    onPress,
                    renderItem,
                    title: i18n.t('WATCHED_SHOWS_WEEK'),
                    subtitle: i18n.t('WATCHED_SHOWS_WEEK_SUB'),
                    showMore: totalItems > Constants.App.LIST_TEASER_ITEMS
                }}
            />
        )
    );
};

export default withNavigation(React.memo(Watched, _.isEqual));
