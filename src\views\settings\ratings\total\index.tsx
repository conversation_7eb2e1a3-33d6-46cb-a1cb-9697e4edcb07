import React, { useEffect } from 'react';
import { Header } from 'react-native-elements';
import i18n from 'i18n-js';
import Close from 'app/app/components/header/Close';
import Container from 'app/app/components/containers/Container';
import { useDispatch, useSelector } from 'react-redux';
import { MSState } from 'app/app/redux/redux';
import { getUserStats, getLatestRatings } from 'app/app/redux/user/actions';
import ScreenLoader from 'app/app/components/loaders/ScreenLoader';
import BarChart from 'app/app/components/chart/BarChart';
import { FlatList, StyleSheet } from 'react-native';
import { SPACING } from 'app/app/styles/sizes';
import EpisodeRating from 'app/app/components/listItems/episode/EpisodeRating';
import Text from 'app/app/components/elements/Text';
import ShowMore from 'app/app/components/elements/ShowMore';
import { useNavigation } from 'react-navigation-hooks';
import Constants from 'app/app/Constants';

const RATINGS_TO_SHOW = 5;
const styles = StyleSheet.create({
    list: {
        paddingTop: SPACING.medium,
        paddingBottom: SPACING.large * 2
    },
    header: {
        paddingHorizontal: SPACING.medium
    },
    title: {
        marginVertical: SPACING.medium
    }
});
interface StateProps {
    ratings: Trakt.UserRatings;
    latestRatingsIds: number[];
    loading: boolean;
}
const TotalRatings = () => {
    // REDUX
    const dispatch = useDispatch();
    const { ratings, latestRatingsIds, loading } = useSelector<MSState, StateProps>(state => {
        const ratings = state.user.stats?.ratings;
        const latestRatingsIds = state.user.latestRatings?.result;
        return {
            ratings,
            latestRatingsIds,
            loading: state.user.latestRatings.loading
        };
    });

    useEffect(() => {
        dispatch(getUserStats());
        if (latestRatingsIds.length === 0) {
            dispatch(getLatestRatings());
        }
    }, []);

    // @ts-ignore
    const { push } = useNavigation();

    if (!ratings) {
        return <ScreenLoader />;
    }
    const values = Object.keys(ratings.distribution)
        .map(key => {
            return {
                value: ratings.distribution[key],
                label: key
            };
        })
        .reverse();

    const renderItem = ({ item }: { item: number }) => {
        return <EpisodeRating {...{ id: item }} />;
    };

    const header = (
        <>
            <BarChart {...{ values, total: ratings.total }} />
            <Text textStyle="title" centered style={{ ...styles.title }}>
                {i18n.t('LAST_RATINGS')}
            </Text>
        </>
    );
    const onPress = () => {
        push(Constants.Navigation.Settings.LATEST_RATINGS);
    };
    return (
        <>
            <Header leftComponent={<Close />} centerComponent={{ text: i18n.t('USER_RATINGS') }} />
            <Container useScrollView={false}>
                <FlatList<number>
                    data={latestRatingsIds.slice(0, RATINGS_TO_SHOW)}
                    contentContainerStyle={{ ...styles.list }}
                    keyExtractor={item => item.toString()}
                    renderItem={renderItem}
                    ListHeaderComponent={header}
                    ListHeaderComponentStyle={{ ...styles.header }}
                    ListFooterComponent={
                        latestRatingsIds.length > RATINGS_TO_SHOW ? <ShowMore {...{ onPress }} /> : null
                    }
                    ListEmptyComponent={loading && <ScreenLoader />}
                />
            </Container>
        </>
    );
};

export default React.memo(TotalRatings);
