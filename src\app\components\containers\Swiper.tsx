import React, { useState, useContext, createRef } from 'react';
import { StyleSheet, View } from 'react-native';
import ViewPager from '@react-native-community/viewpager';
import { SPACING } from 'app/app/styles/sizes';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import ClearButton from '../elements/ClearButton';

const styles = StyleSheet.create({
    buttonsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around'
    },
    buttonContainer: {
        flex: 1,
        marginHorizontal: SPACING.small
    }
});
export interface SwiperPage {
    label: string;
    page: JSX.Element;
}
export interface SwiperProps {
    pages: SwiperPage[];
}

const Swiper = ({ pages }: SwiperProps) => {
    const pager = createRef<ViewPager>();
    const [activeIndex, setActiveIndex] = useState(0);
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;

    return (
        <View>
            <View
                style={{
                    ...styles.buttonsContainer
                }}
            >
                {pages.map((page, index) => {
                    const borderBottomWidth = index === activeIndex ? 1 : 0;
                    return (
                        <ClearButton
                            key={page.label}
                            title={page.label}
                            containerStyle={{
                                ...styles.buttonContainer,
                                borderBottomColor: colors.primary,
                                borderBottomWidth
                            }}
                            onPress={() => {
                                pager.current.setPage(index);
                            }}
                        />
                    );
                })}
            </View>
            <ViewPager
                ref={pager}
                onPageSelected={e => {
                    const index = e.nativeEvent.position;
                    setActiveIndex(index);
                }}
            >
                {pages.map(page => {
                    return page.page;
                })}
            </ViewPager>
        </View>
    );
};

Swiper.defautProps = {
    containerStyle: {}
};

export default React.memo(Swiper);
