import React, { useState, useEffect } from 'react';
import { NavParams } from 'app/app/navigation/Navigator';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from 'react-navigation-hooks';
import { MSState } from 'app/app/redux/redux';
import { resetForm } from 'app/app/redux/meta/actions';
import Toast from 'react-native-toast-message';
import { defaultOptions } from 'app/app/components/elements/Toast';
import i18n from 'i18n-js';
import PostShowComment from './forms/PostShowComment';
import PostReply from './forms/PostReply';
import UpdateComment from './forms/UpdateComment';
import PostSeasonComment from './forms/PostSeasonComment';
import PostEpisodeComment from './forms/PostEpisodeComment';

type CommentType = 'show' | 'season' | 'episode' | 'list' | 'comment';
type CommentAction = 'update';
export interface PostCommentNavParams extends NavParams {
    type: CommentType;
    id: number;
    seasonNumber?: number;
    episodeNumber?: number;
    comment?: Trakt.Comment;
    replyTo?: Trakt.Comment;
    action?: CommentAction;
}

export const validate = (data: Trakt.CommentForm) => {
    const { comment } = data;
    if (!comment || comment.trim() === '') {
        return i18n.t('COMMENT_REQUIRED');
    }
    if (comment.trim().split(/\s+/g).length < 5) {
        return i18n.t('COMMENT_SMALL');
    }
    return null;
};

const PostComment = () => {
    const dispatch = useDispatch();
    const { getParam, goBack } = useNavigation();
    const type: CommentType = getParam('type');
    const id = getParam('id');
    const seasonNumber = getParam('seasonNumber');
    const episodeNumber = getParam('episodeNumber');
    const action: CommentAction = getParam('action');

    const [error, setError] = useState(null);

    const { success, loading, serverError } = useSelector((state: MSState) => {
        const { loading, error: serverError, success } = state.meta.form;
        return {
            loading,
            serverError,
            success
        };
    });

    useEffect(() => {
        if (serverError) {
            setError(serverError);
        } else if (success) {
            Toast.show({
                ...defaultOptions,
                type: 'success',
                text1: action === 'update' ? i18n.t('COMMENT_WAS_UPDATED') : i18n.t('COMMENT_WAS_POSTED')
            });
            dispatch(resetForm());
            goBack(null);
        }
    }, [serverError, success]);

    switch (type) {
        case 'show':
        default:
            return <PostShowComment {...{ id, loading, error }} />;
        case 'season':
            return <PostSeasonComment {...{ id, seasonNumber, loading, error }} />;
        case 'episode':
            return <PostEpisodeComment {...{ id, seasonNumber, episodeNumber, loading, error }} />;
        case 'comment':
            switch (action) {
                case 'update':
                    return <UpdateComment {...{ id, error, loading }} />;
                default:
                    return <PostReply {...{ id, error, loading }} />;
            }
    }
};

export default React.memo(PostComment);
