import React, { PropsWithChildren } from 'react';
import { Overlay, OverlayProps } from 'react-native-elements';

export interface ModalProps extends OverlayProps {
    onClose: () => void;
    height: number;
}
type Props = PropsWithChildren<ModalProps>;
const Modal = (props: Props) => {
    const { onClose, height, children } = props;
    return (
        <Overlay
            {...{
                onBackdropPress: onClose,
                onDismiss: onClose,
                onRequestClose: onClose,
                animationType: 'fade',
                overlayStyle: { opacity: 0.95, height },
                ...props
            }}
        >
            {children}
        </Overlay>
    );
};

export default React.memo(Modal);
