import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import _ from 'lodash';
import { SPACING } from 'app/app/styles/sizes';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import Text, { TextStyle } from '../elements/Text';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: SPACING.medium
    },
    tag: {
        borderWidth: 1,
        borderRadius: 10,
        paddingHorizontal: SPACING.normal,
        marginHorizontal: SPACING.small
    }
});

export interface TagProps {
    label: string;
    textStyle: TextStyle;
    backgroundColor: string;
}

const Tag = ({ label, textStyle, backgroundColor }: TagProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    return (
        <View
            style={{
                ...styles.tag,
                borderColor: colors.onBackground,
                backgroundColor
            }}
        >
            <Text {...{ textStyle }}>{label}</Text>
        </View>
    );
};

Tag.defaultProps = {
    textStyle: 'normal',
    backgroundColor: 'transparent'
};

export default React.memo(Tag, _.isEqual);
