import React, { useContext } from 'react';
import { TabBar as RNTabBar, TabBarProps } from 'react-native-tab-view';
import { ThemeContext, ThemeProps } from 'react-native-elements';
import { Theme } from 'app/app/styles/themes';
import i18n from 'i18n-js';
import _ from 'lodash';

const TAB_BAR_HEIGHT = 60;
// @ts-ignore
const TabBar = (props: TabBarProps) => {
    const {
        theme: { colors }
    } = useContext(ThemeContext) as ThemeProps<Theme>;
    const titles = {
        now: i18n.t('WATCH_NOW'),
        upcoming: i18n.t('UPCOMING')
    };
    return (
        <RNTabBar
            {...{
                indicatorStyle: { backgroundColor: colors.primary },
                style: { backgroundColor: 'transparent' },
                inactiveColor: colors.discreet,
                activeColor: colors.primary,
                pressOpacity: 0.3,
                labelStyle: { textTransform: 'capitalize' },
                getLabelText: scene => {
                    switch (scene.route.key) {
                        case 'watchNow':
                        default: {
                            return titles.now;
                        }
                        case 'upcoming': {
                            return titles.upcoming;
                        }
                    }
                },
                tabStyle: { backgroundColor: 'transparent', height: TAB_BAR_HEIGHT }
            }}
            {...props}
        />
    );
};

export default React.memo(TabBar, _.isEqual);
